<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المستندات المحاسبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(45deg, #2c3e50, #3498db) !important;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2c3e50);
            border: none;
            border-radius: 10px;
        }
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border: none;
            border-radius: 10px;
        }
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
            border: none;
            border-radius: 10px;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .modal-content {
            border-radius: 15px;
        }
        .form-control {
            border-radius: 10px;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: #2c3e50;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover {
            background-color: #3498db;
            color: white;
        }
        .sidebar .nav-link.active {
            background-color: #2c3e50;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-hospital-alt me-2"></i>
                نظام إدارة المستندات المحاسبية
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <span id="username">المدير الرئيسي</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <h5 class="mb-3">
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                    </h5>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" onclick="showAccounts()">
                            <i class="fas fa-list me-2"></i>إدارة الحسابات
                        </a>
                        <a class="nav-link" href="#" onclick="showAddDocument()">
                            <i class="fas fa-plus me-2"></i>إضافة مستند
                        </a>
                        <a class="nav-link" href="#" onclick="showAddAccount()">
                            <i class="fas fa-user-plus me-2"></i>إضافة حساب
                        </a>
                        <a class="nav-link" href="#" onclick="showSearch()">
                            <i class="fas fa-search me-2"></i>البحث
                        </a>
                    </nav>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9">
                <div id="main-content">
                    <!-- محتوى ديناميكي -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة لتفاصيل الحساب -->
    <div class="modal fade" id="accountDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الحساب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="accountDetailsContent">
                    <!-- محتوى تفاصيل الحساب -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحميل الحسابات عند بدء التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            showAccounts();
        });

        // عرض قائمة الحسابات
        async function showAccounts() {
            try {
                const response = await fetch('/api/accounts');
                const data = await response.json();
                
                if (data.success) {
                    let html = `
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>قائمة الحسابات</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>رقم الحساب</th>
                                                <th>اسم الحساب</th>
                                                <th>الرصيد</th>
                                                <th>العمليات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                    `;
                    
                    data.accounts.forEach(account => {
                        html += `
                            <tr>
                                <td>${account.number}</td>
                                <td>${account.name}</td>
                                <td>${account.balance.toFixed(2)}</td>
                                <td>
                                    <button class="btn btn-info btn-sm" onclick="showAccountDetails(${account.number})">
                                        <i class="fas fa-eye me-1"></i>تفاصيل
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    document.getElementById('main-content').innerHTML = html;
                } else {
                    showAlert(data.message, 'danger');
                }
            } catch (error) {
                showAlert('خطأ في جلب الحسابات', 'danger');
            }
        }

        // عرض تفاصيل الحساب
        async function showAccountDetails(accountId) {
            try {
                const response = await fetch(`/api/account/${accountId}/details`);
                const data = await response.json();
                
                if (data.success) {
                    const account = data.account;
                    let html = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الحساب</h6>
                                <p><strong>رقم الحساب:</strong> ${account.number}</p>
                                <p><strong>اسم الحساب:</strong> ${account.name}</p>
                                <p><strong>الرصيد الافتتاحي:</strong> ${account.opening_balance.toFixed(2)}</p>
                                <p><strong>الرصيد الحالي:</strong> ${account.current_balance.toFixed(2)}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>إحصائيات</h6>
                                <p><strong>عدد المستندات:</strong> ${account.documents_count}</p>
                                <p><strong>إجمالي المبالغ:</strong> ${account.total_amount.toFixed(2)}</p>
                            </div>
                        </div>
                        <hr>
                        <h6>المستندات</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المبلغ</th>
                                        <th>رقم المستند</th>
                                        <th>رقم التأدية</th>
                                        <th>القسم</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    account.documents.forEach(doc => {
                        html += `
                            <tr>
                                <td>${doc.amount.toFixed(2)}</td>
                                <td>${doc.document_number}</td>
                                <td>${doc.payment_number}</td>
                                <td>${doc.section}</td>
                            </tr>
                        `;
                    });
                    
                    html += `
                                </tbody>
                            </table>
                        </div>
                    `;
                    
                    document.getElementById('accountDetailsContent').innerHTML = html;
                    new bootstrap.Modal(document.getElementById('accountDetailsModal')).show();
                } else {
                    showAlert(data.message, 'danger');
                }
            } catch (error) {
                showAlert('خطأ في جلب تفاصيل الحساب', 'danger');
            }
        }

        // عرض نموذج إضافة مستند
        function showAddDocument() {
            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus me-2"></i>إضافة مستند جديد</h5>
                    </div>
                    <div class="card-body">
                        <form id="addDocumentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الحساب</label>
                                        <input type="number" class="form-control" id="docAccountNumber" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الحساب</label>
                                        <input type="text" class="form-control" id="docAccountName" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">المبلغ</label>
                                        <input type="number" step="0.01" class="form-control" id="docAmount" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">رقم المستند</label>
                                        <input type="text" class="form-control" id="docNumber" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">رقم التأدية</label>
                                        <input type="text" class="form-control" id="payNumber" required>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ المستند
                            </button>
                        </form>
                    </div>
                </div>
            `;
            
            document.getElementById('main-content').innerHTML = html;
            
            document.getElementById('addDocumentForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = {
                    account_number: document.getElementById('docAccountNumber').value,
                    account_name: document.getElementById('docAccountName').value,
                    amount: document.getElementById('docAmount').value,
                    document_number: document.getElementById('docNumber').value,
                    payment_number: document.getElementById('payNumber').value
                };
                
                try {
                    const response = await fetch('/api/document/add', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        showAlert('تم إضافة المستند بنجاح!', 'success');
                        document.getElementById('addDocumentForm').reset();
                    } else {
                        showAlert(data.message, 'danger');
                    }
                } catch (error) {
                    showAlert('خطأ في إضافة المستند', 'danger');
                }
            });
        }

        // عرض نموذج إضافة حساب
        function showAddAccount() {
            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-plus me-2"></i>إضافة حساب جديد</h5>
                    </div>
                    <div class="card-body">
                        <form id="addAccountForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الحساب</label>
                                        <input type="number" class="form-control" id="accountNumber" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الحساب</label>
                                        <input type="text" class="form-control" id="accountName" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">الرصيد الافتتاحي</label>
                                        <input type="number" step="0.01" class="form-control" id="openingBalance" value="0">
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>إنشاء الحساب
                            </button>
                        </form>
                    </div>
                </div>
            `;
            
            document.getElementById('main-content').innerHTML = html;
            
            document.getElementById('addAccountForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = {
                    account_number: document.getElementById('accountNumber').value,
                    account_name: document.getElementById('accountName').value,
                    opening_balance: document.getElementById('openingBalance').value
                };
                
                try {
                    const response = await fetch('/api/account/add', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        showAlert('تم إنشاء الحساب بنجاح!', 'success');
                        document.getElementById('addAccountForm').reset();
                        showAccounts(); // تحديث قائمة الحسابات
                    } else {
                        showAlert(data.message, 'danger');
                    }
                } catch (error) {
                    showAlert('خطأ في إنشاء الحساب', 'danger');
                }
            });
        }

        // عرض نموذج البحث
        function showSearch() {
            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-search me-2"></i>البحث في المستندات</h5>
                    </div>
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">كلمة البحث</label>
                                        <input type="text" class="form-control" id="searchQuery" placeholder="أدخل كلمة البحث..." required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">نوع البحث</label>
                                        <select class="form-control" id="searchType">
                                            <option value="all">الكل</option>
                                            <option value="document">رقم المستند</option>
                                            <option value="payment">رقم التأدية</option>
                                            <option value="amount">المبلغ</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </form>
                        <div id="searchResults" class="mt-4"></div>
                    </div>
                </div>
            `;
            
            document.getElementById('main-content').innerHTML = html;
            
            document.getElementById('searchForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const query = document.getElementById('searchQuery').value;
                const type = document.getElementById('searchType').value;
                
                try {
                    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}&type=${type}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        let html = `
                            <h6>نتائج البحث (${data.total} نتيجة)</h6>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>رقم الحساب</th>
                                            <th>اسم الحساب</th>
                                            <th>المبلغ</th>
                                            <th>رقم المستند</th>
                                            <th>رقم التأدية</th>
                                            <th>القسم</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;
                        
                        data.results.forEach(result => {
                            html += `
                                <tr>
                                    <td>${result.account_number}</td>
                                    <td>${result.account_name}</td>
                                    <td>${result.amount.toFixed(2)}</td>
                                    <td>${result.document_number}</td>
                                    <td>${result.payment_number}</td>
                                    <td>${result.section}</td>
                                </tr>
                            `;
                        });
                        
                        html += `
                                    </tbody>
                                </table>
                            </div>
                        `;
                        
                        document.getElementById('searchResults').innerHTML = html;
                    } else {
                        showAlert(data.message, 'danger');
                    }
                } catch (error) {
                    showAlert('خطأ في البحث', 'danger');
                }
            });
        }

        // تسجيل الخروج
        async function logout() {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    window.location.href = '/login';
                }
            } catch (error) {
                window.location.href = '/login';
            }
        }

        // عرض التنبيهات
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // إضافة التنبيه في أعلى المحتوى
            const mainContent = document.getElementById('main-content');
            mainContent.insertAdjacentHTML('afterbegin', alertHtml);
            
            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                const alert = mainContent.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        // تحديث الشريط الجانبي النشط
        function updateActiveNav(clickedElement) {
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            clickedElement.classList.add('active');
        }

        // إضافة مستمعات الأحداث للشريط الجانبي
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function() {
                updateActiveNav(this);
            });
        });
    </script>
</body>
</html>
