import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/accounting_provider.dart';
import '../models/account.dart';
import '../utils/app_theme.dart';

class AddDocumentScreen extends StatefulWidget {
  const AddDocumentScreen({super.key});

  @override
  State<AddDocumentScreen> createState() => _AddDocumentScreenState();
}

class _AddDocumentScreenState extends State<AddDocumentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _documentNumberController = TextEditingController();
  final _paymentNumberController = TextEditingController();
  
  Account? _selectedAccount;

  @override
  void dispose() {
    _amountController.dispose();
    _documentNumberController.dispose();
    _paymentNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AccountingProvider>(
        builder: (context, accountingProvider, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الصفحة
                  Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.plus,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'إضافة مستند جديد',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // بطاقة النموذج
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // اختيار الحساب
                          Text(
                            'اختيار الحساب',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          DropdownButtonFormField<Account>(
                            value: _selectedAccount,
                            decoration: const InputDecoration(
                              labelText: 'الحساب',
                              prefixIcon: Icon(FontAwesomeIcons.user),
                            ),
                            items: accountingProvider.accounts.map((account) {
                              return DropdownMenuItem<Account>(
                                value: account,
                                child: Text('${account.number} - ${account.name}'),
                              );
                            }).toList(),
                            onChanged: (Account? value) {
                              setState(() {
                                _selectedAccount = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الحساب';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 20),
                          
                          // معلومات المستند
                          Text(
                            'معلومات المستند',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // المبلغ
                          TextFormField(
                            controller: _amountController,
                            decoration: const InputDecoration(
                              labelText: 'المبلغ',
                              prefixIcon: Icon(FontAwesomeIcons.coins),
                              suffixText: 'د.أ',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال المبلغ';
                              }
                              if (double.tryParse(value) == null) {
                                return 'يرجى إدخال مبلغ صحيح';
                              }
                              if (double.parse(value) <= 0) {
                                return 'يجب أن يكون المبلغ أكبر من صفر';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // رقم المستند
                          TextFormField(
                            controller: _documentNumberController,
                            decoration: const InputDecoration(
                              labelText: 'رقم المستند',
                              prefixIcon: Icon(FontAwesomeIcons.fileAlt),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال رقم المستند';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // رقم التأدية
                          TextFormField(
                            controller: _paymentNumberController,
                            decoration: const InputDecoration(
                              labelText: 'رقم التأدية',
                              prefixIcon: Icon(FontAwesomeIcons.receipt),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال رقم التأدية';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // أزرار الإجراءات
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                  onPressed: _clearForm,
                                  child: const Text('مسح'),
                                ),
                              ),
                              
                              const SizedBox(width: 16),
                              
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: accountingProvider.isLoading 
                                      ? null 
                                      : _addDocument,
                                  child: accountingProvider.isLoading
                                      ? const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                          ),
                                        )
                                      : const Text('إضافة المستند'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // معلومات إضافية
                  Card(
                    color: Colors.blue[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                FontAwesomeIcons.infoCircle,
                                color: Colors.blue,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'معلومات مهمة',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue[800],
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 8),
                          
                          Text(
                            '• تأكد من صحة جميع البيانات قبل الإضافة\n'
                            '• سيتم تحديث رصيد الحساب تلقائياً\n'
                            '• يمكنك مراجعة المستند في تفاصيل الحساب',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _clearForm() {
    _amountController.clear();
    _documentNumberController.clear();
    _paymentNumberController.clear();
    setState(() {
      _selectedAccount = null;
    });
  }

  Future<void> _addDocument() async {
    if (_formKey.currentState!.validate()) {
      final accountingProvider = Provider.of<AccountingProvider>(context, listen: false);
      
      final error = await accountingProvider.addDocument(
        accountNumber: _selectedAccount!.number,
        accountName: _selectedAccount!.name,
        amount: double.parse(_amountController.text),
        documentNumber: _documentNumberController.text,
        paymentNumber: _paymentNumberController.text,
      );

      if (mounted) {
        if (error == null) {
          // نجح الإضافة
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة المستند بنجاح'),
              backgroundColor: AppTheme.accentColor,
            ),
          );
          _clearForm();
        } else {
          // فشل الإضافة
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(error),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    }
  }
}
