#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع إصلاح نافذة تفاصيل الحساب
"""

import sys
import traceback
import json
import os
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

# ملف حفظ معلومات تسجيل الدخول
LOGIN_SAVE_FILE = "saved_login.json"

def load_saved_login():
    """تحميل معلومات تسجيل الدخول المحفوظة"""
    try:
        if os.path.exists(LOGIN_SAVE_FILE):
            with open(LOGIN_SAVE_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if data.get("auto_login", False):
                print(f"🔓 تم العثور على معلومات تسجيل دخول محفوظة: {data['username']}")
                return data
        
        print("ℹ️ لا توجد معلومات تسجيل دخول محفوظة")
        return None
        
    except Exception as e:
        print(f"⚠️ خطأ في تحميل معلومات تسجيل الدخول: {e}")
        return None

def save_login_info(username):
    """حفظ معلومات تسجيل الدخول"""
    try:
        data = {
            "username": username,
            "last_login": datetime.now().isoformat(),
            "auto_login": True
        }
        
        with open(LOGIN_SAVE_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ معلومات تسجيل الدخول: {username}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ معلومات تسجيل الدخول: {e}")
        return False

def test_account_details_functionality():
    """اختبار وظائف نافذة تفاصيل الحساب"""
    print("🧪 اختبار وظائف نافذة تفاصيل الحساب...")
    
    try:
        from excel_manager import ExcelManager
        from manage_accounts import AccountDetailsDialog
        
        # إنشاء مدير Excel للاختبار
        excel_manager = ExcelManager()
        
        if excel_manager.load_workbook():
            accounts = excel_manager.get_accounts_list()
            
            if accounts:
                account_num, account_name, balance = accounts[0]
                sheet_name = f"{account_num}-{account_name}"
                
                print(f"✅ اختبار الحساب: {account_name} ({account_num})")
                
                # فحص وجود الورقة
                if sheet_name in excel_manager.workbook.sheetnames:
                    print(f"✅ الورقة '{sheet_name}' موجودة")
                elif str(account_num) in excel_manager.workbook.sheetnames:
                    print(f"✅ الورقة '{account_num}' موجودة")
                    sheet_name = str(account_num)
                else:
                    print(f"⚠️ لم يتم العثور على ورقة للحساب {account_num}")
                    # البحث في جميع الأوراق
                    for sheet in excel_manager.workbook.sheetnames:
                        if str(account_num) in sheet:
                            sheet_name = sheet
                            print(f"✅ تم العثور على الورقة: {sheet}")
                            break
                
                print("✅ اختبار وظائف نافذة تفاصيل الحساب نجح")
                return True
            else:
                print("❌ لا توجد حسابات في ملف Excel")
                return False
        else:
            print("❌ فشل في تحميل ملف Excel")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة تفاصيل الحساب: {e}")
        return False

def create_enhanced_app():
    """إنشاء التطبيق مع تحسينات نافذة تفاصيل الحساب"""
    try:
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        
        # إعداد معالج الأخطاء العام
        def global_exception_handler(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                print("⚠️ تم إيقاف التطبيق بواسطة المستخدم")
                return
            
            error_details = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            print(f"\n🚨 خطأ غير متوقع:")
            print(f"النوع: {exc_type.__name__}")
            print(f"الرسالة: {str(exc_value)}")
            print(f"التفاصيل:\n{error_details}")
            
            # اقتراح حلول خاصة بنافذة تفاصيل الحساب
            if "AccountDetailsDialog" in error_details:
                print("\n💡 حلول خاصة بنافذة تفاصيل الحساب:")
                print("   - تحقق من وجود الحساب في ملف Excel")
                print("   - تأكد من صحة بنية ملف Excel")
                print("   - أعد إنشاء الحساب إذا لزم الأمر")
            
            if "load_account_data" in error_details:
                print("\n💡 حلول خاصة بتحميل بيانات الحساب:")
                print("   - تحقق من وجود الورقة في ملف Excel")
                print("   - تأكد من صحة أسماء الأوراق")
                print("   - فحص بنية البيانات في الورقة")
        
        sys.excepthook = global_exception_handler
        
        print("📦 استيراد وإنشاء التطبيق...")
        from app import AccountingApp
        
        # تحميل معلومات تسجيل الدخول
        saved_login = load_saved_login()
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        if saved_login:
            # تسجيل دخول تلقائي
            print(f"🔓 تسجيل دخول تلقائي: {saved_login['username']}")
            
            # تعيين المستخدم
            if hasattr(app, 'user_manager'):
                app.user_manager.current_user = {
                    'username': saved_login['username'],
                    'role': 'admin',
                    'permissions': ['add_account', 'delete_account', 'add_document', 'view_reports', 'manage_users']
                }
            
            # إغلاق نافذة تسجيل الدخول
            if hasattr(app, 'login_window') and app.login_window:
                try:
                    if hasattr(app.login_window, 'root'):
                        app.login_window.root.destroy()
                    print("✅ تم إغلاق نافذة تسجيل الدخول")
                except:
                    pass
            
            # إظهار النافذة الرئيسية
            app.root.deiconify()
            
            # إعداد الواجهة
            if hasattr(app, 'setup_modern_ui'):
                print("🎨 إعداد الواجهة الحديثة...")
                app.setup_modern_ui()
            
            # بدء التحديث التلقائي
            if hasattr(app, 'start_auto_refresh'):
                try:
                    app.start_auto_refresh()
                    print("🔄 تم بدء التحديث التلقائي")
                except:
                    print("⚠️ لم يتم بدء التحديث التلقائي")
            
            # تحديث عنوان النافذة
            app.root.title(f"نظام إدارة المستندات المحاسبية - {saved_login['username']} (مع إصلاح تفاصيل الحساب)")
            
        else:
            # تسجيل دخول عادي مع حفظ المعلومات
            print("🔐 عرض نافذة تسجيل الدخول العادية")
            
            # تحسين دالة تسجيل الدخول
            original_on_login_success = app.on_login_success
            
            def enhanced_login_success():
                try:
                    # حفظ معلومات تسجيل الدخول
                    if hasattr(app, 'user_manager') and app.user_manager.current_user:
                        username = app.user_manager.current_user.get('username', 'admin')
                        save_login_info(username)
                    
                    # استدعاء الدالة الأصلية
                    original_on_login_success()
                    
                except Exception as e:
                    print(f"❌ خطأ في تسجيل الدخول: {str(e)}")
            
            app.on_login_success = enhanced_login_success
        
        return app, root
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {str(e)}")
        print(f"📋 التفاصيل:\n{traceback.format_exc()}")
        return None, None

def main():
    """الدالة الرئيسية"""
    
    print("=" * 80)
    print("🚀 نظام إدارة المستندات المحاسبية - مع إصلاح نافذة تفاصيل الحساب")
    print("=" * 80)
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🛠️ تم إصلاح مشاكل نافذة تفاصيل الحساب")
    print("🛡️ معالجة محسنة للأخطاء")
    print("💾 حفظ تلقائي لمعلومات تسجيل الدخول")
    print("=" * 80)
    
    try:
        # اختبار وظائف نافذة تفاصيل الحساب
        if test_account_details_functionality():
            print("✅ اختبار نافذة تفاصيل الحساب نجح")
        else:
            print("⚠️ تحذير: قد تكون هناك مشاكل في نافذة تفاصيل الحساب")
        
        # إنشاء وتشغيل التطبيق
        app, root = create_enhanced_app()
        
        if app and root:
            print("✅ بدء تشغيل التطبيق...")
            print("📋 يمكنك الآن استخدام التطبيق وفتح تفاصيل الحسابات")
            print("🔍 راقب هذه النافذة لأي أخطاء أو رسائل تشخيصية")
            print("-" * 80)
            
            # تشغيل التطبيق
            root.mainloop()
        else:
            print("❌ فشل في إنشاء التطبيق")
    
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف التطبيق بواسطة المستخدم")
    
    except Exception as e:
        print(f"\n❌ خطأ عام في التشغيل: {str(e)}")
        print(f"📋 التفاصيل:\n{traceback.format_exc()}")
        
        # اقتراح حلول
        print("\n💡 اقتراحات للحل:")
        if "Excel" in str(e):
            print("   📊 تأكد من وجود ملف accounting_system.xlsx")
            print("   🔒 أغلق ملف Excel إذا كان مفتوحاً")
        elif "import" in str(e).lower():
            print("   📦 تحقق من وجود جميع ملفات المشروع")
            print("   🔄 أعد تشغيل التطبيق")
        else:
            print("   💻 أعد تشغيل الكمبيوتر")
            print("   📞 اتصل بالدعم الفني")
    
    finally:
        print(f"\n🔚 انتهاء التشغيل - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

if __name__ == "__main__":
    main()
