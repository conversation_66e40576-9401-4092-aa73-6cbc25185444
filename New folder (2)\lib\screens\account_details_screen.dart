import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/account.dart';
import '../providers/accounting_provider.dart';
import '../utils/app_theme.dart';

class AccountDetailsScreen extends StatefulWidget {
  final Account account;

  const AccountDetailsScreen({
    super.key,
    required this.account,
  });

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل تفاصيل الحساب
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AccountingProvider>(context, listen: false)
          .loadAccountDetails(widget.account.number);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل ${widget.account.name}'),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.sync),
            onPressed: () {
              Provider.of<AccountingProvider>(context, listen: false)
                  .loadAccountDetails(widget.account.number);
            },
          ),
        ],
      ),
      
      body: Consumer<AccountingProvider>(
        builder: (context, accountingProvider, _) {
          if (accountingProvider.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل تفاصيل الحساب...'),
                ],
              ),
            );
          }

          if (accountingProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    FontAwesomeIcons.exclamationTriangle,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    accountingProvider.error!,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      accountingProvider.clearError();
                      accountingProvider.loadAccountDetails(widget.account.number);
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final details = accountingProvider.selectedAccountDetails;
          if (details == null) {
            return const Center(
              child: Text('لا توجد تفاصيل متاحة'),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await accountingProvider.loadAccountDetails(widget.account.number);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // بطاقة معلومات الحساب
                  _buildAccountInfoCard(details),
                  
                  const SizedBox(height: 16),
                  
                  // بطاقة الإحصائيات
                  _buildStatisticsCard(details),
                  
                  const SizedBox(height: 16),
                  
                  // قائمة المستندات
                  _buildDocumentsList(details),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAccountInfoCard(AccountDetails details) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.infoCircle,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات الحساب',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoRow('رقم الحساب', '${details.account.number}'),
            _buildInfoRow('اسم الحساب', details.account.name),
            _buildInfoRow(
              'الرصيد الافتتاحي', 
              '${details.openingBalance.toStringAsFixed(2)} د.أ',
            ),
            _buildInfoRow(
              'الرصيد الحالي', 
              '${details.currentBalance.toStringAsFixed(2)} د.أ',
              valueColor: details.currentBalance >= 0 
                  ? AppTheme.accentColor 
                  : AppTheme.errorColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(AccountDetails details) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.chartBar,
                  color: AppTheme.secondaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'الإحصائيات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    icon: FontAwesomeIcons.fileAlt,
                    title: 'عدد المستندات',
                    value: '${details.documentsCount}',
                    color: AppTheme.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    icon: FontAwesomeIcons.coins,
                    title: 'إجمالي المبالغ',
                    value: '${details.totalAmount.toStringAsFixed(2)} د.أ',
                    color: AppTheme.accentColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsList(AccountDetails details) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.list,
                  color: AppTheme.accentColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'المستندات (آخر ${details.account.documents.length})',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (details.account.documents.isEmpty)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      FontAwesomeIcons.folderOpen,
                      size: 48,
                      color: AppTheme.subtitleColor,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد مستندات',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.subtitleColor,
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: details.account.documents.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final document = details.account.documents[index];
                  return _buildDocumentItem(document);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentItem(Document document) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              FontAwesomeIcons.fileAlt,
              color: AppTheme.primaryColor,
              size: 16,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مستند رقم: ${document.documentNumber}',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'رقم التأدية: ${document.paymentNumber}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.subtitleColor,
                  ),
                ),
                Text(
                  document.section,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.secondaryColor,
                  ),
                ),
              ],
            ),
          ),
          
          Text(
            '${document.amount.toStringAsFixed(2)} د.أ',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.accentColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.subtitleColor,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor ?? AppTheme.textColor,
            ),
          ),
        ],
      ),
    );
  }
}
