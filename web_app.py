#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق ويب منفصل لنظام إدارة المستندات المحاسبية
Web Application - Standalone Version
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, send_from_directory
import os
import json
import openpyxl
from datetime import datetime
import uuid

app = Flask(__name__)
app.secret_key = 'web_accounting_system_2025'

# مسار ملف البيانات
DATA_FILE = 'web_accounting_data.xlsx'

class WebExcelManager:
    """مدير Excel منفصل للتطبيق الويب"""
    
    def __init__(self):
        self.workbook = None
        self.load_or_create_workbook()
    
    def load_or_create_workbook(self):
        """تحميل أو إنشاء ملف Excel"""
        try:
            if os.path.exists(DATA_FILE):
                self.workbook = openpyxl.load_workbook(DATA_FILE)
                print(f"✅ تم تحميل ملف البيانات: {DATA_FILE}")
            else:
                self.create_new_workbook()
                print(f"✅ تم إنشاء ملف بيانات جديد: {DATA_FILE}")
        except Exception as e:
            print(f"❌ خطأ في تحميل/إنشاء ملف البيانات: {e}")
            self.create_new_workbook()
    
    def create_new_workbook(self):
        """إنشاء ملف Excel جديد"""
        self.workbook = openpyxl.Workbook()
        
        # حذف الورقة الافتراضية
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])
        
        # إنشاء ورقة أرصدة الحسابات
        accounts_sheet = self.workbook.create_sheet('أرصدة الحسابات')
        
        # إعداد عناوين ورقة الحسابات
        headers = ['رقم الحساب', 'اسم الحساب', 'الرصيد الحالي', 'تاريخ الإنشاء']
        for col, header in enumerate(headers, 1):
            accounts_sheet.cell(row=1, column=col, value=header)
        
        # إنشاء حساب تجريبي
        accounts_sheet.cell(row=2, column=1, value=1)
        accounts_sheet.cell(row=2, column=2, value='حساب تجريبي')
        accounts_sheet.cell(row=2, column=3, value=1000.0)
        accounts_sheet.cell(row=2, column=4, value=datetime.now().strftime('%Y-%m-%d'))
        
        self.save_workbook()
    
    def save_workbook(self):
        """حفظ ملف Excel"""
        try:
            self.workbook.save(DATA_FILE)
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف: {e}")
            return False
    
    def get_accounts_list(self):
        """الحصول على قائمة الحسابات"""
        accounts = []
        try:
            if 'أرصدة الحسابات' in self.workbook.sheetnames:
                ws = self.workbook['أرصدة الحسابات']
                
                for row in range(2, ws.max_row + 1):
                    account_num = ws.cell(row=row, column=1).value
                    account_name = ws.cell(row=row, column=2).value
                    balance = ws.cell(row=row, column=3).value
                    
                    if account_num and account_name:
                        accounts.append({
                            'number': account_num,
                            'name': account_name,
                            'balance': float(balance) if balance else 0.0
                        })
            
            return accounts
        except Exception as e:
            print(f"❌ خطأ في جلب الحسابات: {e}")
            return []
    
    def create_account(self, account_num, account_name, opening_balance=0):
        """إنشاء حساب جديد"""
        try:
            # إضافة الحساب إلى ورقة الحسابات
            accounts_sheet = self.workbook['أرصدة الحسابات']
            
            # البحث عن أول صف فارغ
            next_row = accounts_sheet.max_row + 1
            
            accounts_sheet.cell(row=next_row, column=1, value=account_num)
            accounts_sheet.cell(row=next_row, column=2, value=account_name)
            accounts_sheet.cell(row=next_row, column=3, value=opening_balance)
            accounts_sheet.cell(row=next_row, column=4, value=datetime.now().strftime('%Y-%m-%d'))
            
            # إنشاء ورقة منفصلة للحساب
            sheet_name = f"{account_num}-{account_name}"
            account_sheet = self.workbook.create_sheet(sheet_name)
            
            # إعداد ورقة الحساب
            self.setup_account_sheet(account_sheet, account_num, account_name, opening_balance)
            
            return self.save_workbook()
        
        except Exception as e:
            print(f"❌ خطأ في إنشاء الحساب: {e}")
            return False
    
    def setup_account_sheet(self, sheet, account_num, account_name, opening_balance):
        """إعداد ورقة الحساب"""
        # العنوان الرئيسي
        sheet.cell(row=1, column=1, value=f"حساب رقم {account_num} - {account_name}")
        sheet.cell(row=2, column=1, value=f"الرصيد الافتتاحي: {opening_balance}")
        
        # عناوين الأقسام
        sections = ['القسم 1', 'القسم 2', 'القسم 3', 'القسم 4', 'القسم 5', 'القسم 6']
        
        for i, section in enumerate(sections):
            col_start = 1 + (i * 3)
            sheet.cell(row=5, column=col_start, value='المبلغ')
            sheet.cell(row=5, column=col_start + 1, value='رقم المستند')
            sheet.cell(row=5, column=col_start + 2, value='رقم التأدية')
            sheet.cell(row=4, column=col_start, value=section)
        
        # حفظ الرصيد الافتتاحي في A8
        sheet.cell(row=8, column=1, value=opening_balance)
    
    def add_document(self, account_num, account_name, amount, doc_num, pay_num):
        """إضافة مستند جديد"""
        try:
            sheet_name = f"{account_num}-{account_name}"
            
            if sheet_name not in self.workbook.sheetnames:
                return False
            
            ws = self.workbook[sheet_name]
            
            # البحث عن خلية فارغة
            for row in range(8, 1000):
                for section in range(6):
                    col_start = 1 + (section * 3)
                    
                    amount_cell = ws.cell(row=row, column=col_start)
                    doc_cell = ws.cell(row=row, column=col_start + 1)
                    pay_cell = ws.cell(row=row, column=col_start + 2)
                    
                    if not amount_cell.value and not doc_cell.value and not pay_cell.value:
                        # إدراج المستند
                        amount_cell.value = amount
                        doc_cell.value = doc_num
                        pay_cell.value = pay_num
                        
                        # تحديث الرصيد في ورقة الحسابات
                        self.update_account_balance(account_num, amount)
                        
                        return self.save_workbook()
            
            return False
        
        except Exception as e:
            print(f"❌ خطأ في إضافة المستند: {e}")
            return False
    
    def update_account_balance(self, account_num, amount):
        """تحديث رصيد الحساب"""
        try:
            accounts_sheet = self.workbook['أرصدة الحسابات']
            
            for row in range(2, accounts_sheet.max_row + 1):
                if accounts_sheet.cell(row=row, column=1).value == account_num:
                    current_balance = accounts_sheet.cell(row=row, column=3).value or 0
                    new_balance = current_balance + amount
                    accounts_sheet.cell(row=row, column=3, value=new_balance)
                    break
        
        except Exception as e:
            print(f"❌ خطأ في تحديث الرصيد: {e}")
    
    def get_account_details(self, account_num, account_name):
        """الحصول على تفاصيل الحساب"""
        try:
            sheet_name = f"{account_num}-{account_name}"
            
            if sheet_name not in self.workbook.sheetnames:
                return None
            
            ws = self.workbook[sheet_name]
            
            # جلب المستندات
            documents = []
            total_amount = 0
            
            for row in range(8, 200):
                for section in range(6):
                    col_start = 1 + (section * 3)
                    
                    amount = ws.cell(row=row, column=col_start).value
                    doc_num = ws.cell(row=row, column=col_start + 1).value
                    pay_num = ws.cell(row=row, column=col_start + 2).value
                    
                    if amount and doc_num and pay_num:
                        documents.append({
                            'amount': float(amount),
                            'document_number': str(doc_num),
                            'payment_number': str(pay_num),
                            'section': f"القسم {section + 1}",
                            'row': row
                        })
                        total_amount += float(amount)
            
            # الرصيد الافتتاحي
            opening_balance = ws.cell(row=8, column=1).value or 0
            current_balance = opening_balance + total_amount
            
            return {
                'opening_balance': opening_balance,
                'current_balance': current_balance,
                'documents_count': len(documents),
                'total_amount': total_amount,
                'documents': documents
            }
        
        except Exception as e:
            print(f"❌ خطأ في جلب تفاصيل الحساب: {e}")
            return None
    
    def search_documents(self, query, search_type='all'):
        """البحث في المستندات"""
        results = []
        try:
            accounts = self.get_accounts_list()
            
            for account in accounts:
                account_num = account['number']
                account_name = account['name']
                sheet_name = f"{account_num}-{account_name}"
                
                if sheet_name in self.workbook.sheetnames:
                    ws = self.workbook[sheet_name]
                    
                    for row in range(8, 200):
                        for section in range(6):
                            col_start = 1 + (section * 3)
                            
                            amount = ws.cell(row=row, column=col_start).value
                            doc_num = ws.cell(row=row, column=col_start + 1).value
                            pay_num = ws.cell(row=row, column=col_start + 2).value
                            
                            if amount and doc_num and pay_num:
                                # فحص معايير البحث
                                match = False
                                if search_type == 'all':
                                    match = (query in str(doc_num) or 
                                           query in str(pay_num) or 
                                           query in str(amount))
                                elif search_type == 'document':
                                    match = query in str(doc_num)
                                elif search_type == 'payment':
                                    match = query in str(pay_num)
                                elif search_type == 'amount':
                                    match = query in str(amount)
                                
                                if match:
                                    results.append({
                                        'account_number': account_num,
                                        'account_name': account_name,
                                        'amount': float(amount),
                                        'document_number': str(doc_num),
                                        'payment_number': str(pay_num),
                                        'section': f"القسم {section + 1}",
                                        'row': row
                                    })
            
            return results
        
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            return []

# إنشاء مدير البيانات
excel_manager = WebExcelManager()

# المسارات (Routes)
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('index.html')

@app.route('/login')
def login():
    """صفحة تسجيل الدخول"""
    return render_template('login.html')

@app.route('/api/login', methods=['POST'])
def api_login():
    """API تسجيل الدخول"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # تسجيل دخول بسيط للتجربة
        if username == 'admin' and password == 'admin':
            session['user_id'] = username
            session['username'] = username
            return jsonify({'success': True, 'message': 'تم تسجيل الدخول بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'اسم المستخدم أو كلمة المرور غير صحيحة'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تسجيل الدخول: {str(e)}'})

@app.route('/api/logout', methods=['POST'])
def api_logout():
    """API تسجيل الخروج"""
    session.clear()
    return jsonify({'success': True, 'message': 'تم تسجيل الخروج بنجاح'})

@app.route('/api/accounts')
def api_accounts():
    """API للحصول على قائمة الحسابات"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    try:
        accounts = excel_manager.get_accounts_list()
        return jsonify({
            'success': True,
            'accounts': accounts,
            'total': len(accounts)
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في جلب الحسابات: {str(e)}'})

@app.route('/api/account/<int:account_id>/details')
def api_account_details(account_id):
    """API للحصول على تفاصيل حساب"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    try:
        accounts = excel_manager.get_accounts_list()
        target_account = None
        
        for account in accounts:
            if account['number'] == account_id:
                target_account = account
                break
        
        if not target_account:
            return jsonify({'success': False, 'message': 'الحساب غير موجود'})
        
        details = excel_manager.get_account_details(account_id, target_account['name'])
        
        if details:
            return jsonify({
                'success': True,
                'account': {
                    'number': account_id,
                    'name': target_account['name'],
                    'balance': target_account['balance'],
                    **details
                }
            })
        else:
            return jsonify({'success': False, 'message': 'فشل في جلب تفاصيل الحساب'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في جلب تفاصيل الحساب: {str(e)}'})

@app.route('/api/document/add', methods=['POST'])
def api_add_document():
    """API لإضافة مستند جديد"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    try:
        data = request.get_json()
        account_num = int(data.get('account_number'))
        account_name = data.get('account_name')
        amount = float(data.get('amount'))
        doc_num = data.get('document_number')
        pay_num = data.get('payment_number')
        
        if not all([account_num, account_name, amount, doc_num, pay_num]):
            return jsonify({'success': False, 'message': 'جميع الحقول مطلوبة'})
        
        success = excel_manager.add_document(account_num, account_name, amount, doc_num, pay_num)
        
        if success:
            return jsonify({'success': True, 'message': 'تم إضافة المستند بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في إضافة المستند'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إضافة المستند: {str(e)}'})

@app.route('/api/account/add', methods=['POST'])
def api_add_account():
    """API لإضافة حساب جديد"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    try:
        data = request.get_json()
        account_num = int(data.get('account_number'))
        account_name = data.get('account_name')
        opening_balance = float(data.get('opening_balance', 0))
        
        if not all([account_num, account_name]):
            return jsonify({'success': False, 'message': 'رقم الحساب واسم الحساب مطلوبان'})
        
        success = excel_manager.create_account(account_num, account_name, opening_balance)
        
        if success:
            return jsonify({'success': True, 'message': 'تم إنشاء الحساب بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في إنشاء الحساب'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إنشاء الحساب: {str(e)}'})

@app.route('/api/search')
def api_search():
    """API للبحث في المستندات"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    try:
        query = request.args.get('q', '').strip()
        search_type = request.args.get('type', 'all')
        
        if not query:
            return jsonify({'success': False, 'message': 'يرجى إدخال كلمة البحث'})
        
        results = excel_manager.search_documents(query, search_type)
        
        return jsonify({
            'success': True,
            'results': results,
            'total': len(results),
            'query': query,
            'search_type': search_type
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في البحث: {str(e)}'})

if __name__ == '__main__':
    print("🚀 بدء تشغيل تطبيق الويب المنفصل...")
    print("=" * 60)
    print("🌐 التطبيق متاح على: http://localhost:5000")
    print("👤 للدخول استخدم: admin / admin")
    print("📁 ملف البيانات: web_accounting_data.xlsx")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
