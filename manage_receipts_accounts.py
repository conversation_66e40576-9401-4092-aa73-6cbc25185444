import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
import os
import subprocess
import platform
from datetime import datetime
from account_details_window import AccountDetailsWindow

class ManageReceiptsAccountsDialog(tk.Toplevel):
    """نافذة إدارة حسابات المقبوضات"""

    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("إدارة حسابات المقبوضات")
        self.parent = parent

        # تكوين النافذة
        self.geometry("900x700")
        self.configure(bg='#f0f0f0')
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent.root)
        self.grab_set()

        # متغيرات
        self.accounts_data = []
        self.selected_account = None

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_accounts_data()

        # توسيط النافذة
        self.center_window()

        # ربط إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # عنوان النافذة
        title_label = ttk.Label(main_frame,
                               text="⚙️ إدارة حسابات المقبوضات",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))

        # إطار قائمة الحسابات
        accounts_frame = ttk.LabelFrame(main_frame,
                                       text="📋 قائمة الحسابات",
                                       padding="15")
        accounts_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        accounts_frame.columnconfigure(0, weight=1)
        accounts_frame.rowconfigure(0, weight=1)

        # إنشاء Treeview للحسابات
        columns = ('account_name', 'total_documents', 'total_amount', 'last_modified')
        self.accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين
        self.accounts_tree.heading('account_name', text='اسم الحساب')
        self.accounts_tree.heading('total_documents', text='عدد المستندات')
        self.accounts_tree.heading('total_amount', text='إجمالي المبلغ')
        self.accounts_tree.heading('last_modified', text='آخر تعديل')

        # تعريف عرض الأعمدة
        self.accounts_tree.column('account_name', width=300, anchor='center')
        self.accounts_tree.column('total_documents', width=120, anchor='center')
        self.accounts_tree.column('total_amount', width=150, anchor='center')
        self.accounts_tree.column('last_modified', width=150, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(accounts_frame, orient="vertical", command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)

        # وضع العناصر
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # ربط النقر المزدوج
        self.accounts_tree.bind('<Double-1>', self.on_account_double_click)
        self.accounts_tree.bind('<<TreeviewSelect>>', self.on_account_select)

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # زر عرض التفاصيل
        details_btn = ttk.Button(row1_frame,
                                text="👁️ عرض التفاصيل",
                                command=self.show_account_details,
                                style='Accent.TButton')
        details_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر إضافة جدول جديد
        add_table_btn = ttk.Button(row1_frame,
                                  text="➕ إضافة جدول جديد",
                                  command=self.add_new_table)
        add_table_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر حذف الحساب
        delete_btn = ttk.Button(row1_frame,
                               text="🗑️ حذف الحساب",
                               command=self.delete_account)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث البيانات
        refresh_btn = ttk.Button(row1_frame,
                                text="🔄 تحديث البيانات",
                                command=self.refresh_data)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # زر الطباعة
        print_btn = ttk.Button(row2_frame,
                              text="🖨️ طباعة الحساب",
                              command=self.print_account)
        print_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر التصدير
        export_btn = ttk.Button(row2_frame,
                               text="📤 تصدير إلى Excel",
                               command=self.export_to_excel)
        export_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر المساعدة
        help_btn = ttk.Button(row2_frame,
                             text="❓ مساعدة",
                             command=self.show_help)
        help_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # زر الإغلاق
        close_btn = ttk.Button(row2_frame,
                              text="❌ إغلاق",
                              command=self.on_closing)
        close_btn.pack(side=tk.RIGHT)

    def load_accounts_data(self):
        """تحميل بيانات الحسابات من ملف المقبوضات"""
        try:
            deductions_file = "Accounting system deductions.xlsx"
            if not os.path.exists(deductions_file):
                messagebox.showwarning("تنبيه", f"ملف المقبوضات غير موجود: {deductions_file}")
                return

            workbook = openpyxl.load_workbook(deductions_file)
            self.accounts_data = []

            # مسح البيانات الحالية
            for item in self.accounts_tree.get_children():
                self.accounts_tree.delete(item)

            for sheet_name in workbook.sheetnames:
                if sheet_name != "مرحباً":  # تجاهل ورقة الترحيب
                    try:
                        ws = workbook[sheet_name]

                        # حساب إحصائيات الحساب
                        total_documents = self.count_documents(ws)
                        total_amount = self.calculate_total_amount(ws)
                        last_modified = self.get_last_modified_date(deductions_file)

                        account_data = {
                            'name': sheet_name,
                            'total_documents': total_documents,
                            'total_amount': total_amount,
                            'last_modified': last_modified
                        }

                        self.accounts_data.append(account_data)

                        # إضافة إلى Treeview
                        self.accounts_tree.insert('', 'end', values=(
                            sheet_name,
                            total_documents,
                            f"{total_amount:.3f}",
                            last_modified
                        ))

                    except Exception as e:
                        print(f"خطأ في معالجة الحساب {sheet_name}: {str(e)}")

            workbook.close()
            print(f"✅ تم تحميل {len(self.accounts_data)} حساب من ملف المقبوضات")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الحسابات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الحسابات:\n{str(e)}")

    def count_documents(self, ws):
        """حساب عدد المستندات في الحساب"""
        count = 0
        try:
            # البحث في جميع الأعمدة المخصصة للمستندات
            doc_columns = ['B', 'D', 'F', 'H', 'J', 'L', 'N']

            for col in doc_columns:
                for row in range(13, 1000):  # البحث في نطاق واسع
                    cell_value = ws[f'{col}{row}'].value
                    if cell_value and str(cell_value).strip():
                        count += 1
                    elif row > 50 and not cell_value:  # توقف إذا وصلنا لمنطقة فارغة
                        break

        except Exception as e:
            print(f"خطأ في حساب المستندات: {str(e)}")

        return count

    def calculate_total_amount(self, ws):
        """حساب إجمالي المبالغ في الحساب"""
        total = 0.0
        try:
            # البحث في جميع الأعمدة المخصصة للمبالغ
            amount_columns = ['A', 'C', 'E', 'G', 'I', 'K', 'M']

            for col in amount_columns:
                for row in range(13, 1000):  # البحث في نطاق واسع
                    cell_value = ws[f'{col}{row}'].value
                    if isinstance(cell_value, (int, float)) and cell_value > 0:
                        total += cell_value
                    elif row > 50 and not cell_value:  # توقف إذا وصلنا لمنطقة فارغة
                        break

        except Exception as e:
            print(f"خطأ في حساب المبالغ: {str(e)}")

        return total

    def get_last_modified_date(self, file_path):
        """الحصول على تاريخ آخر تعديل للملف"""
        try:
            timestamp = os.path.getmtime(file_path)
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M')
        except:
            return "غير معروف"

    def on_account_select(self, event):
        """معالج اختيار حساب من القائمة"""
        selection = self.accounts_tree.selection()
        if selection:
            item = self.accounts_tree.item(selection[0])
            self.selected_account = item['values'][0]  # اسم الحساب

    def on_account_double_click(self, event):
        """معالج النقر المزدوج على حساب"""
        self.show_account_details()

    def show_account_details(self):
        """عرض تفاصيل الحساب المحدد في نافذة منفصلة"""
        if not self.selected_account:
            messagebox.showwarning("تنبيه", "يرجى اختيار حساب من القائمة")
            return

        try:
            # فتح نافذة تفاصيل الحساب
            deductions_file = "Accounting system deductions.xlsx"
            details_window = AccountDetailsWindow(self, self.selected_account, deductions_file)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التفاصيل:\n{str(e)}")

    def add_new_table(self):
        """إضافة جدول جديد للحساب المحدد"""
        if not self.selected_account:
            messagebox.showwarning("تنبيه", "يرجى اختيار حساب من القائمة")
            return

        try:
            result = messagebox.askyesno("تأكيد",
                                        f"هل تريد إضافة جدول جديد للحساب:\n{self.selected_account}؟")
            if not result:
                return

            deductions_file = "Accounting system deductions.xlsx"
            workbook = openpyxl.load_workbook(deductions_file)

            if self.selected_account not in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب غير موجود: {self.selected_account}")
                return

            ws = workbook[self.selected_account]

            # إنشاء جدول جديد
            if self.create_new_table_in_sheet(ws, self.selected_account):
                workbook.save(deductions_file)
                messagebox.showinfo("نجاح", f"تم إضافة جدول جديد للحساب: {self.selected_account}")
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الجدول الجديد")

            workbook.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الجدول:\n{str(e)}")

    def create_new_table_in_sheet(self, ws, account_name):
        """إنشاء جدول جديد في ورقة الحساب مع قراءة عدد الجداول الموجودة"""
        try:
            # قراءة عدد الجداول الموجودة
            existing_tables = self.count_existing_tables(ws)
            print(f"📊 عدد الجداول الموجودة: {existing_tables}")

            # العثور على موقع آخر جدول
            last_table_end = self.find_last_table_end(ws)

            # موقع الجدول الجديد (بعد آخر جدول بـ 5 صفوف)
            new_table_start = last_table_end + 5

            print(f"🔄 إنشاء جدول جديد للحساب {account_name} في الصف {new_table_start}")

            # إضافة عناوين الجدول الجديد
            columns_pairs = [
                ('A', 'B'), ('C', 'D'), ('E', 'F'), ('G', 'H'),
                ('I', 'J'), ('K', 'L'), ('M', 'N')
            ]

            # إضافة عناوين الأعمدة
            for amount_col, doc_col in columns_pairs:
                ws[f'{amount_col}{new_table_start}'] = 'المبلغ'
                ws[f'{doc_col}{new_table_start}'] = 'رقم المستند'

                # تنسيق العناوين
                ws[f'{amount_col}{new_table_start}'].font = Font(size=11, bold=True)
                ws[f'{doc_col}{new_table_start}'].font = Font(size=11, bold=True)
                ws[f'{amount_col}{new_table_start}'].alignment = Alignment(horizontal='center')
                ws[f'{doc_col}{new_table_start}'].alignment = Alignment(horizontal='center')

            # إضافة صفوف البيانات (21 صف لكل قسم)
            for row in range(new_table_start + 1, new_table_start + 22):
                for amount_col, doc_col in columns_pairs:
                    # تنسيق الخلايا
                    ws[f'{amount_col}{row}'].number_format = '0.000'
                    ws[f'{amount_col}{row}'].alignment = Alignment(horizontal='center')
                    ws[f'{doc_col}{row}'].alignment = Alignment(horizontal='center')

            # إضافة صف المجاميع
            totals_row = new_table_start + 22
            for amount_col, doc_col in columns_pairs:
                ws[f'{amount_col}{totals_row}'] = f'=SUM({amount_col}{new_table_start + 1}:{amount_col}{new_table_start + 21})'
                ws[f'{amount_col}{totals_row}'].font = Font(size=11, bold=True)
                ws[f'{amount_col}{totals_row}'].number_format = '0.000'
                ws[f'{amount_col}{totals_row}'].alignment = Alignment(horizontal='center')

            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول جديد: {str(e)}")
            return False

    def count_existing_tables(self, ws):
        """عد الجداول الموجودة في الورقة"""
        table_count = 0
        row = 10  # بداية البحث

        while row < 1000:  # حد أقصى للبحث
            # فحص وجود جدول في هذا الموقع
            if self.is_table_header_at_row(ws, row):
                table_count += 1
                row += 40  # الانتقال إلى الجدول التالي
            else:
                row += 1

        return table_count

    def find_last_table_end(self, ws):
        """العثور على نهاية آخر جدول"""
        last_table_end = 10  # قيمة افتراضية
        row = 10

        while row < 1000:
            if self.is_table_header_at_row(ws, row):
                # وجدنا جدول، حساب نهايته
                table_end = row + 25  # تقدير طول الجدول
                last_table_end = max(last_table_end, table_end)
                row += 40
            else:
                row += 1

        # فحص آخر صف مستخدم فعلياً
        actual_last_row = ws.max_row
        return max(last_table_end, actual_last_row)

    def is_table_header_at_row(self, ws, row):
        """فحص وجود رأس جدول في الصف المحدد"""
        try:
            # فحص وجود عناوين الأعمدة
            if (ws[f'A{row}'].value == 'المبلغ' or
                ws[f'A{row-1}'].value == 'المبلغ' or
                ws[f'A{row+1}'].value == 'المبلغ'):
                return True
            return False
        except:
            return False

    def delete_account(self):
        """حذف الحساب المحدد"""
        if not self.selected_account:
            messagebox.showwarning("تنبيه", "يرجى اختيار حساب من القائمة")
            return

        result = messagebox.askyesno("تأكيد الحذف",
                                    f"هل أنت متأكد من حذف الحساب:\n{self.selected_account}؟\n\n"
                                    f"⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!")
        if not result:
            return

        try:
            deductions_file = "Accounting system deductions.xlsx"
            workbook = openpyxl.load_workbook(deductions_file)

            if self.selected_account in workbook.sheetnames:
                workbook.remove(workbook[self.selected_account])
                workbook.save(deductions_file)

                messagebox.showinfo("نجح الحذف", f"تم حذف الحساب: {self.selected_account}")
                self.selected_account = None
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", f"الحساب غير موجود: {self.selected_account}")

            workbook.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف الحساب:\n{str(e)}")

    def print_account(self):
        """طباعة الحساب المحدد بنموذج مخصص"""
        if not self.selected_account:
            messagebox.showwarning("تنبيه", "يرجى اختيار حساب من القائمة")
            return

        try:
            # إنشاء نموذج طباعة مخصص
            print_file = self.create_print_template()

            if print_file:
                # فتح النموذج للطباعة
                if platform.system() == "Windows":
                    os.startfile(print_file)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", print_file])
                else:  # Linux
                    subprocess.run(["xdg-open", print_file])

                messagebox.showinfo("طباعة",
                                   f"تم إنشاء نموذج الطباعة:\n{print_file}\n\n"
                                   f"يمكنك الآن طباعة النموذج مباشرة")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء نموذج الطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة الحساب:\n{str(e)}")

    def create_print_template(self):
        """إنشاء نموذج طباعة مخصص للحساب"""
        try:
            # تحميل بيانات الحساب
            deductions_file = "Accounting system deductions.xlsx"
            workbook = openpyxl.load_workbook(deductions_file)

            if self.selected_account not in workbook.sheetnames:
                return None

            ws = workbook[self.selected_account]

            # جمع بيانات المستندات
            documents_data = self.extract_documents_data(ws)

            # إنشاء نموذج طباعة Excel
            print_file = f"نموذج_طباعة_{self.selected_account}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

            # إنشاء ملف جديد
            print_workbook = openpyxl.Workbook()
            print_ws = print_workbook.active
            print_ws.title = f"كشف_{self.selected_account}"

            # إعداد النموذج
            self.setup_print_template(print_ws, documents_data)

            # حفظ النموذج
            print_workbook.save(print_file)
            print_workbook.close()
            workbook.close()

            return print_file

        except Exception as e:
            print(f"❌ خطأ في إنشاء نموذج الطباعة: {str(e)}")
            return None

    def extract_documents_data(self, ws):
        """استخراج بيانات المستندات من الورقة"""
        documents = []
        doc_columns = ['B', 'D', 'F', 'H', 'J', 'L', 'N']
        amount_columns = ['A', 'C', 'E', 'G', 'I', 'K', 'M']

        row = 13  # بداية البيانات

        while row < 1000:
            if self.is_table_header_at_row(ws, row):
                # البحث في هذا الجدول
                for data_row in range(row, row + 21):
                    for doc_col, amount_col in zip(doc_columns, amount_columns):
                        doc_value = ws[f'{doc_col}{data_row}'].value
                        amount_value = ws[f'{amount_col}{data_row}'].value

                        if doc_value and str(doc_value).strip():
                            amount = float(amount_value) if isinstance(amount_value, (int, float)) else 0.0
                            documents.append({
                                'doc_number': str(doc_value).strip(),
                                'amount': amount,
                                'date': datetime.now().strftime('%Y-%m-%d')  # تاريخ افتراضي
                            })

                row += 40  # الانتقال إلى الجدول التالي
            else:
                row += 1

        return documents

    def setup_print_template(self, ws, documents_data):
        """إعداد نموذج الطباعة"""
        # رأس النموذج
        ws['A1'] = 'وزارة الصحة الأردنية'
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A1:E1')

        ws['A2'] = 'قسم الخصومات والاستقطاعات'
        ws['A2'].font = Font(size=14, bold=True)
        ws['A2'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A2:E2')

        ws['A4'] = f'كشف حساب: {self.selected_account}'
        ws['A4'].font = Font(size=14, bold=True)
        ws['A4'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A4:E4')

        ws['A5'] = f'تاريخ الطباعة: {datetime.now().strftime("%Y-%m-%d %H:%M")}'
        ws['A5'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A5:E5')

        # عناوين الجدول
        headers = ['رقم المستند', 'المبلغ', 'التاريخ', 'ملاحظات']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=7, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # بيانات المستندات
        total_amount = 0.0
        for row_idx, doc in enumerate(documents_data, 8):
            ws.cell(row=row_idx, column=1, value=doc['doc_number'])
            ws.cell(row=row_idx, column=2, value=doc['amount'])
            ws.cell(row=row_idx, column=3, value=doc['date'])
            ws.cell(row=row_idx, column=4, value='')  # ملاحظات فارغة

            # تنسيق الخلايا
            for col in range(1, 5):
                cell = ws.cell(row=row_idx, column=col)
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

            # تنسيق المبلغ
            ws.cell(row=row_idx, column=2).number_format = '0.000'
            total_amount += doc['amount']

        # صف الإجمالي
        total_row = len(documents_data) + 9
        ws.cell(row=total_row, column=1, value='الإجمالي')
        ws.cell(row=total_row, column=1).font = Font(bold=True)
        ws.cell(row=total_row, column=2, value=total_amount)
        ws.cell(row=total_row, column=2).font = Font(bold=True)
        ws.cell(row=total_row, column=2).number_format = '0.000'

        # تنسيق صف الإجمالي
        for col in range(1, 5):
            cell = ws.cell(row=total_row, column=col)
            cell.border = Border(
                left=Side(style='thick'),
                right=Side(style='thick'),
                top=Side(style='thick'),
                bottom=Side(style='thick')
            )

        # معلومات إضافية
        info_row = total_row + 3
        ws.cell(row=info_row, column=1, value=f'عدد المستندات: {len(documents_data)}')
        ws.cell(row=info_row + 1, column=1, value='اسم وتوقيع المحاسب: ________________')
        ws.cell(row=info_row + 2, column=1, value='التاريخ: ________________')

        # تعيين عرض الأعمدة
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 25

    def export_to_excel(self):
        """تصدير الحساب المحدد إلى ملف Excel منفصل"""
        if not self.selected_account:
            messagebox.showwarning("تنبيه", "يرجى اختيار حساب من القائمة")
            return

        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ الحساب",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialvalue=f"{self.selected_account}.xlsx"
            )

            if not file_path:
                return

            # إنشاء ملف جديد مع الحساب المحدد فقط
            deductions_file = "Accounting system deductions.xlsx"
            source_workbook = openpyxl.load_workbook(deductions_file)

            if self.selected_account not in source_workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب غير موجود: {self.selected_account}")
                return

            # إنشاء ملف جديد
            new_workbook = openpyxl.Workbook()

            # حذف الورقة الافتراضية
            if 'Sheet' in new_workbook.sheetnames:
                new_workbook.remove(new_workbook['Sheet'])

            # نسخ ورقة الحساب
            source_sheet = source_workbook[self.selected_account]
            new_sheet = new_workbook.create_sheet(self.selected_account)

            # نسخ البيانات والتنسيق
            for row in source_sheet.iter_rows():
                for cell in row:
                    new_cell = new_sheet[cell.coordinate]
                    new_cell.value = cell.value
                    if cell.font:
                        new_cell.font = Font(
                            name=cell.font.name,
                            size=cell.font.size,
                            bold=cell.font.bold
                        )
                    if cell.alignment:
                        new_cell.alignment = Alignment(
                            horizontal=cell.alignment.horizontal,
                            vertical=cell.alignment.vertical
                        )
                    if cell.number_format:
                        new_cell.number_format = cell.number_format

            # حفظ الملف الجديد
            new_workbook.save(file_path)

            source_workbook.close()
            new_workbook.close()

            messagebox.showinfo("نجح التصدير",
                               f"تم تصدير الحساب بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير الحساب:\n{str(e)}")

    def refresh_data(self):
        """تحديث بيانات الحسابات"""
        self.load_accounts_data()
        messagebox.showinfo("تحديث", "تم تحديث بيانات الحسابات")

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = (
            "📚 مساعدة - إدارة حسابات المقبوضات\n\n"
            "🔹 انقر على حساب لتحديده\n"
            "🔹 انقر نقراً مزدوجاً لعرض التفاصيل\n"
            "🔹 استخدم 'عرض التفاصيل' لفتح الملف\n"
            "🔹 'إضافة جدول جديد' لإضافة جدول للحساب\n"
            "🔹 'حذف الحساب' لحذف الحساب نهائياً\n"
            "🔹 'طباعة الحساب' لطباعة بيانات الحساب\n"
            "🔹 'تصدير إلى Excel' لحفظ الحساب في ملف منفصل\n\n"
            "⚠️ تأكد من إغلاق ملف Excel قبل التعديل"
        )
        messagebox.showinfo("مساعدة", help_text)

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """معالج إغلاق النافذة"""
        self.destroy()
