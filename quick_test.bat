@echo off
chcp 65001 >nul
title Quick Test - Calculate Fix

echo ========================================
echo    Quick Test - Calculate Fix
echo ========================================
echo.

echo Running quick test for calculate fix...
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found, running test...
    echo.
    
    REM Run the quick test
    %PYTHON_PATH% quick_test.py
    
) else (
    echo ERROR: Python not found at expected location
    pause
    exit /b 1
)

echo.
echo Test completed.
pause
