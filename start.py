#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية
الملف الرئيسي للتشغيل
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة المسار الحالي لمسار البحث
if hasattr(sys, '_MEIPASS'):
    # عند التشغيل كملف تنفيذي
    base_path = sys._MEIPASS
else:
    # عند التشغيل كسكريبت Python
    base_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, base_path)

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_modules = ['openpyxl', 'tkinter']
    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        error_msg = f"المكتبات التالية مفقودة: {', '.join(missing_modules)}"
        messagebox.showerror("خطأ في المكتبات", error_msg)
        return False

    return True

def setup_arabic_support():
    """إعداد دعم اللغة العربية"""
    try:
        # تعيين ترميز UTF-8
        if sys.platform.startswith('win'):
            import locale
            locale.setlocale(locale.LC_ALL, 'Arabic_Jordan.1256')
    except:
        pass  # تجاهل الأخطاء في إعداد اللغة

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    try:
        # فحص المكتبات
        if not check_dependencies():
            return

        # إعداد دعم العربية
        setup_arabic_support()

        # استيراد التطبيق
        from app import AccountingApp

        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية")
        root.geometry("1200x700")

        # تعيين أيقونة النافذة (إذا كانت متوفرة)
        try:
            icon_path = os.path.join(base_path, 'icon.ico')
            if os.path.exists(icon_path):
                root.iconbitmap(icon_path)
        except:
            pass

        # تعيين الخط العربي
        root.option_add("*font", "Arial 11")

        # إنشاء التطبيق
        app = AccountingApp(root)

        # تشغيل التطبيق
        root.mainloop()

    except ImportError as e:
        error_msg = f"خطأ في استيراد الملفات: {str(e)}"
        messagebox.showerror("خطأ في التشغيل", error_msg)
        print(error_msg)

    except Exception as e:
        error_msg = f"حدث خطأ غير متوقع: {str(e)}"
        messagebox.showerror("خطأ", error_msg)
        print(error_msg)

if __name__ == "__main__":
    main()
