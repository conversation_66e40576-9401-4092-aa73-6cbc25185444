class Account {
  final int number;
  final String name;
  final double balance;
  final String createdDate;
  final List<Document> documents;

  Account({
    required this.number,
    required this.name,
    required this.balance,
    required this.createdDate,
    this.documents = const [],
  });

  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      number: json['number'] ?? 0,
      name: json['name'] ?? '',
      balance: (json['balance'] ?? 0).toDouble(),
      createdDate: json['created_date'] ?? '',
      documents: (json['documents'] as List<dynamic>?)
          ?.map((doc) => Document.fromJson(doc))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'balance': balance,
      'created_date': createdDate,
      'documents': documents.map((doc) => doc.toJson()).toList(),
    };
  }

  Account copyWith({
    int? number,
    String? name,
    double? balance,
    String? createdDate,
    List<Document>? documents,
  }) {
    return Account(
      number: number ?? this.number,
      name: name ?? this.name,
      balance: balance ?? this.balance,
      createdDate: createdDate ?? this.createdDate,
      documents: documents ?? this.documents,
    );
  }
}

class Document {
  final double amount;
  final String documentNumber;
  final String paymentNumber;
  final String section;
  final int row;

  Document({
    required this.amount,
    required this.documentNumber,
    required this.paymentNumber,
    required this.section,
    required this.row,
  });

  factory Document.fromJson(Map<String, dynamic> json) {
    return Document(
      amount: (json['amount'] ?? 0).toDouble(),
      documentNumber: json['document_number'] ?? '',
      paymentNumber: json['payment_number'] ?? '',
      section: json['section'] ?? '',
      row: json['row'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'document_number': documentNumber,
      'payment_number': paymentNumber,
      'section': section,
      'row': row,
    };
  }
}

class AccountDetails {
  final Account account;
  final double openingBalance;
  final double currentBalance;
  final int documentsCount;
  final double totalAmount;

  AccountDetails({
    required this.account,
    required this.openingBalance,
    required this.currentBalance,
    required this.documentsCount,
    required this.totalAmount,
  });

  factory AccountDetails.fromJson(Map<String, dynamic> json) {
    final accountData = json['account'] ?? {};
    return AccountDetails(
      account: Account.fromJson(accountData),
      openingBalance: (accountData['opening_balance'] ?? 0).toDouble(),
      currentBalance: (accountData['current_balance'] ?? 0).toDouble(),
      documentsCount: accountData['documents_count'] ?? 0,
      totalAmount: (accountData['total_amount'] ?? 0).toDouble(),
    );
  }
}
