#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض أسماء الأقسام المحدثة
"""

import sys
import traceback
import tkinter as tk
from tkinter import messagebox

def main():
    """اختبار عرض أسماء الأقسام"""

    print("🧪 اختبار عرض أسماء الأقسام المحدثة")
    print("=" * 50)
    print("📝 تم تعديل عرض أسماء الأقسام لتظهر فقط 'القسم 1', 'القسم 2', إلخ")
    print("=" * 50)

    try:
        # استيراد الوحدات
        print("📦 استيراد الوحدات...")
        from excel_manager import ExcelManager
        from manage_accounts import ManageAccountsDialog

        print("✅ تم استيراد الوحدات بنجاح")

        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        root.title("اختبار أسماء الأقسام المحدثة")
        root.geometry("800x600")

        # إنشاء مدير Excel
        print("📊 إنشاء مدير Excel...")
        excel_manager = ExcelManager()

        # تحميل ملف Excel
        print("📁 تحميل ملف Excel...")
        if excel_manager.load_data():
            print("✅ تم تحميل ملف Excel بنجاح")

            # إنشاء نافذة إدارة الحسابات
            print("🏗️ إنشاء نافذة إدارة الحسابات...")
            manage_dialog = ManageAccountsDialog(root, excel_manager)

            # إنشاء واجهة الاختبار
            main_frame = tk.Frame(root, padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # عنوان
            title_label = tk.Label(main_frame,
                                 text="اختبار أسماء الأقسام المحدثة",
                                 font=("Arial", 18, "bold"),
                                 fg="#2c3e50")
            title_label.pack(pady=(0, 20))

            # معلومات التحديث
            update_info = tk.Label(main_frame,
                                 text="📝 تم تعديل عرض أسماء الأقسام لتظهر بشكل مبسط",
                                 font=("Arial", 12),
                                 fg="#27ae60")
            update_info.pack(pady=(0, 10))

            # معلومات التحديثات
            updates_frame = tk.LabelFrame(main_frame, text="التحديث الجديد",
                                        font=("Arial", 12, "bold"))
            updates_frame.pack(fill=tk.X, pady=(0, 20))

            updates_text = tk.Text(updates_frame, height=6, width=70, wrap=tk.WORD)
            updates_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

            updates_content = """تحديث عرض أسماء الأقسام:

✅ قبل التحديث: "جدول 1 - قسم 1 (A) - صف 8"
✅ بعد التحديث: "القسم 1"

الآن ستظهر أسماء الأقسام بشكل مبسط وواضح:
- القسم 1
- القسم 2
- القسم 3
- القسم 4
- القسم 5
- القسم 6"""\n            \n            updates_text.insert(tk.END, updates_content)\n            updates_text.config(state=tk.DISABLED)\n            \n            # زر فتح إدارة الحسابات\n            open_btn = tk.Button(main_frame,\n                                text=\"📋 فتح إدارة الحسابات لاختبار التحديث\",\n                                command=lambda: manage_dialog.deiconify(),\n                                bg='#3498db', fg='white',\n                                font=(\"Arial\", 14, \"bold\"),\n                                padx=30, pady=15)\n            open_btn.pack(pady=20)\n            \n            # معلومات إضافية\n            info_label = tk.Label(main_frame,\n                                text=\"💡 اختر أي حساب وافتح تفاصيله لرؤية أسماء الأقسام المحدثة\",\n                                font=(\"Arial\", 10),\n                                fg=\"#7f8c8d\")\n            info_label.pack(pady=(20, 0))\n            \n            print(\"✅ تم إعداد واجهة الاختبار\")\n            print(\"🚀 بدء تشغيل الاختبار...\")\n            print(\"-\" * 50)\n            \n            # تشغيل التطبيق\n            root.mainloop()\n            \n        else:\n            print(\"❌ فشل في تحميل ملف Excel\")\n            messagebox.showerror(\"خطأ\", \"فشل في تحميل ملف Excel\\nتأكد من وجود ملف accounting_system.xlsx\")\n    \n    except Exception as e:\n        error_msg = f\"خطأ عام: {str(e)}\"\n        print(f\"🚨 {error_msg}\")\n        print(f\"📋 التفاصيل:\\n{traceback.format_exc()}\")\n\nif __name__ == \"__main__\":\n    main()
