import tkinter as tk
from tkinter import ttk, messagebox
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
import os
from datetime import datetime

class AccountDetailsWindow(tk.Toplevel):
    """نافذة عرض تفاصيل الحساب"""

    def __init__(self, parent, account_name, file_path):
        super().__init__(parent)
        self.title(f"تفاصيل الحساب: {account_name}")
        self.parent = parent
        self.account_name = account_name
        self.file_path = file_path

        # تكوين النافذة
        self.geometry("1000x800")  # زيادة الارتفاع لإظهار أزرار الطباعة والتصدير والإغلاق
        self.configure(bg='#f0f0f0')
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        # متغيرات البيانات
        self.account_data = []
        self.summary_data = {}

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_account_data()

        # توسيط النافذة
        self.center_window()

        # ربط إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # عنوان النافذة
        title_label = ttk.Label(main_frame,
                               text=f"📊 تفاصيل الحساب: {self.account_name}",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))

        # إطار الملخص
        summary_frame = ttk.LabelFrame(main_frame,
                                      text="📈 ملخص الحساب",
                                      padding="15")
        summary_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # معلومات الملخص
        self.summary_labels = {}

        # الصف الأول
        row1_frame = ttk.Frame(summary_frame)
        row1_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(row1_frame, text="إجمالي المستندات:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.summary_labels['total_docs'] = ttk.Label(row1_frame, text="0", font=('Arial', 10))
        self.summary_labels['total_docs'].pack(side=tk.LEFT, padx=(10, 30))

        ttk.Label(row1_frame, text="إجمالي المبلغ:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.summary_labels['total_amount'] = ttk.Label(row1_frame, text="0.000", font=('Arial', 10))
        self.summary_labels['total_amount'].pack(side=tk.LEFT, padx=(10, 30))

        # الصف الثاني
        row2_frame = ttk.Frame(summary_frame)
        row2_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(row2_frame, text="عدد الجداول:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.summary_labels['total_tables'] = ttk.Label(row2_frame, text="0", font=('Arial', 10))
        self.summary_labels['total_tables'].pack(side=tk.LEFT, padx=(10, 30))

        ttk.Label(row2_frame, text="آخر تحديث:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.summary_labels['last_update'] = ttk.Label(row2_frame, text="غير معروف", font=('Arial', 10))
        self.summary_labels['last_update'].pack(side=tk.LEFT, padx=(10, 0))

        # إطار تفاصيل المستندات
        details_frame = ttk.LabelFrame(main_frame,
                                      text="📋 تفاصيل المستندات",
                                      padding="15")
        details_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(0, weight=1)

        # إنشاء Treeview للمستندات
        columns = ('doc_number', 'amount', 'section', 'table_number', 'position')
        self.details_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين
        self.details_tree.heading('doc_number', text='رقم المستند')
        self.details_tree.heading('amount', text='المبلغ')
        self.details_tree.heading('section', text='القسم')
        self.details_tree.heading('table_number', text='رقم الجدول')
        self.details_tree.heading('position', text='الموقع')

        # تعريف عرض الأعمدة
        self.details_tree.column('doc_number', width=150, anchor='center')
        self.details_tree.column('amount', width=120, anchor='center')
        self.details_tree.column('section', width=100, anchor='center')
        self.details_tree.column('table_number', width=100, anchor='center')
        self.details_tree.column('position', width=150, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(details_frame, orient="vertical", command=self.details_tree.yview)
        self.details_tree.configure(yscrollcommand=scrollbar.set)

        # وضع العناصر
        self.details_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # زر تحديث البيانات
        refresh_btn = ttk.Button(buttons_frame,
                                text="🔄 تحديث البيانات",
                                command=self.refresh_data)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر طباعة التفاصيل
        print_btn = ttk.Button(buttons_frame,
                              text="🖨️ طباعة التفاصيل",
                              command=self.print_details)
        print_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تصدير البيانات
        export_btn = ttk.Button(buttons_frame,
                               text="📤 تصدير البيانات",
                               command=self.export_data)
        export_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإغلاق
        close_btn = ttk.Button(buttons_frame,
                              text="❌ إغلاق",
                              command=self.on_closing)
        close_btn.pack(side=tk.RIGHT)

    def load_account_data(self):
        """تحميل بيانات الحساب من ملف Excel"""
        try:
            if not os.path.exists(self.file_path):
                messagebox.showerror("خطأ", f"الملف غير موجود: {self.file_path}")
                return

            workbook = openpyxl.load_workbook(self.file_path)

            if self.account_name not in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب غير موجود: {self.account_name}")
                return

            ws = workbook[self.account_name]

            # مسح البيانات الحالية
            for item in self.details_tree.get_children():
                self.details_tree.delete(item)

            self.account_data = []
            total_amount = 0.0
            total_docs = 0
            table_count = 0

            # البحث في جميع الجداول والأقسام (مع آلية مصححة)
            doc_columns = ['B', 'D', 'F', 'H', 'J', 'L', 'N']
            amount_columns = ['A', 'C', 'E', 'G', 'I', 'K', 'M']
            section_names = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس', 'السابع']

            current_table = 1

            # البحث في الجدول الأول (يبدأ من الصف 13 - بيانات فعلية)
            table_start_row = 13

            # فحص الجدول الأول فقط (من 13 إلى 33)
            table_count = 1
            print(f"📊 فحص الجدول {current_table} في الصفوف 13-33")

            # البحث في أقسام الجدول (7 أقسام × 21 صف)
            for section_idx, (doc_col, amount_col) in enumerate(zip(doc_columns, amount_columns)):
                print(f"🔍 فحص القسم {section_names[section_idx]} (عمود {doc_col})")

                # نطاق بيانات القسم (من الصف 13 إلى 33)
                section_start = 13
                section_end = 34  # لا يشمل صف المجاميع

                for data_row in range(section_start, section_end):
                    try:
                        doc_value = ws[f'{doc_col}{data_row}'].value
                        amount_value = ws[f'{amount_col}{data_row}'].value

                        # فحص وجود مستند صحيح (فلترة محسنة)
                        doc_str = str(doc_value).strip() if doc_value else ""

                        # قائمة القيم المرفوضة
                        invalid_values = [
                            '', '0', 'ما قبله', 'None', 'null', '#N/A', '#REF!', '#VALUE!',
                            'المبلغ', 'رقم المستند', 'اسم الحساب'
                        ]

                        if (doc_value and
                            doc_str and
                            doc_str not in invalid_values and
                            doc_str.lower() not in [v.lower() for v in invalid_values] and
                            not doc_str.startswith('=') and
                            not doc_str.startswith('#') and
                            len(doc_str) > 0):

                            # تحويل المبلغ بأمان
                            try:
                                amount = float(amount_value) if amount_value else 0.0
                            except (ValueError, TypeError):
                                amount = 0.0

                            # تجاهل المبالغ الصفرية أو السالبة
                            if amount <= 0:
                                continue

                            document_data = {
                                'doc_number': str(doc_value).strip(),
                                'amount': amount,
                                'section': section_names[section_idx],
                                'table_number': current_table,
                                'position': f'{amount_col}{data_row}'
                            }

                            self.account_data.append(document_data)
                            total_amount += amount
                            total_docs += 1

                            # إضافة إلى Treeview
                            self.details_tree.insert('', 'end', values=(
                                document_data['doc_number'],
                                f"{document_data['amount']:.3f}",
                                document_data['section'],
                                document_data['table_number'],
                                document_data['position']
                            ))

                            print(f"✅ مستند: {doc_value} - مبلغ: {amount} - قسم: {section_names[section_idx]} - موقع: {amount_col}{data_row}")

                    except Exception as e:
                        print(f"❌ خطأ في قراءة الخلية {amount_col}{data_row}: {str(e)}")
                        continue

            # تحديث الملخص
            self.summary_data = {
                'total_docs': total_docs,
                'total_amount': total_amount,
                'total_tables': table_count,
                'last_update': self.get_last_modified_date()
            }

            self.update_summary_display()

            workbook.close()
            print(f"✅ تم تحميل {total_docs} مستند من {table_count} جدول")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الحساب: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الحساب:\n{str(e)}")

    def is_table_at_row(self, ws, row):
        """فحص وجود جدول في الصف المحدد (محسن)"""
        try:
            # فحص وجود عناوين الأعمدة في نطاق أوسع
            for check_row in range(max(1, row-3), row+3):
                try:
                    # فحص عناوين المبالغ والمستندات
                    cell_a = ws[f'A{check_row}'].value
                    cell_b = ws[f'B{check_row}'].value

                    if (cell_a and str(cell_a).strip() == 'المبلغ' and
                        cell_b and str(cell_b).strip() == 'رقم المستند'):
                        return True

                    # فحص بديل للجدول الأول
                    if (cell_a and str(cell_a).strip() == 'اسم الحساب'):
                        return True

                except:
                    continue

            # فحص إضافي: هل يوجد بيانات في هذا الصف؟
            has_data = False
            for col in ['A', 'B', 'C', 'D', 'E', 'F', 'G']:
                try:
                    if ws[f'{col}{row}'].value:
                        has_data = True
                        break
                except:
                    continue

            # إذا كان هناك بيانات ولم نجد عناوين، فهذا قد يكون جدول
            if has_data and row >= 13:
                return True

            return False
        except Exception as e:
            print(f"❌ خطأ في فحص الجدول في الصف {row}: {str(e)}")
            return False

    def has_data_in_range(self, ws, start_row, end_row):
        """فحص وجود بيانات في نطاق معين"""
        try:
            for row in range(start_row, end_row + 1):
                for col in ['A', 'B', 'C', 'D', 'E', 'F', 'G']:
                    try:
                        if ws[f'{col}{row}'].value:
                            return True
                    except:
                        continue
            return False
        except:
            return False

    def update_summary_display(self):
        """تحديث عرض الملخص"""
        self.summary_labels['total_docs'].config(text=str(self.summary_data['total_docs']))
        self.summary_labels['total_amount'].config(text=f"{self.summary_data['total_amount']:.3f}")
        self.summary_labels['total_tables'].config(text=str(self.summary_data['total_tables']))
        self.summary_labels['last_update'].config(text=self.summary_data['last_update'])

    def get_last_modified_date(self):
        """الحصول على تاريخ آخر تعديل للملف"""
        try:
            timestamp = os.path.getmtime(self.file_path)
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M')
        except:
            return "غير معروف"

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_account_data()
        messagebox.showinfo("تحديث", "تم تحديث بيانات الحساب")

    def print_details(self):
        """طباعة تفاصيل الحساب"""
        try:
            # إنشاء تقرير للطباعة
            report_content = self.generate_print_report()

            # حفظ التقرير في ملف مؤقت
            temp_file = f"تقرير_{self.account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            messagebox.showinfo("طباعة", f"تم إنشاء تقرير الطباعة:\n{temp_file}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير الطباعة:\n{str(e)}")

    def generate_print_report(self):
        """إنشاء تقرير للطباعة"""
        report = f"""
{'='*60}
                    تقرير تفاصيل الحساب
{'='*60}

اسم الحساب: {self.account_name}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{'='*60}
                        ملخص الحساب
{'='*60}

إجمالي المستندات: {self.summary_data['total_docs']}
إجمالي المبلغ: {self.summary_data['total_amount']:.3f}
عدد الجداول: {self.summary_data['total_tables']}
آخر تحديث: {self.summary_data['last_update']}

{'='*60}
                      تفاصيل المستندات
{'='*60}

{'رقم المستند':<15} {'المبلغ':<12} {'القسم':<10} {'رقم الجدول':<10} {'الموقع':<15}
{'-'*60}
"""

        for doc in self.account_data:
            report += f"{doc['doc_number']:<15} {doc['amount']:<12.3f} {doc['section']:<10} {doc['table_number']:<10} {doc['position']:<15}\n"

        report += f"\n{'='*60}\n"
        report += f"إجمالي المبلغ: {self.summary_data['total_amount']:.3f}\n"
        report += f"{'='*60}\n"

        return report

    def export_data(self):
        """تصدير البيانات إلى ملف Excel"""
        try:
            from tkinter import filedialog

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="تصدير بيانات الحساب",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialvalue=f"تفاصيل_{self.account_name}.xlsx"
            )

            if not file_path:
                return

            # إنشاء ملف Excel جديد
            new_workbook = openpyxl.Workbook()
            ws = new_workbook.active
            ws.title = f"تفاصيل_{self.account_name}"

            # إضافة العناوين
            headers = ['رقم المستند', 'المبلغ', 'القسم', 'رقم الجدول', 'الموقع']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # إضافة البيانات
            for row, doc in enumerate(self.account_data, 2):
                ws.cell(row=row, column=1, value=doc['doc_number'])
                ws.cell(row=row, column=2, value=doc['amount'])
                ws.cell(row=row, column=3, value=doc['section'])
                ws.cell(row=row, column=4, value=doc['table_number'])
                ws.cell(row=row, column=5, value=doc['position'])

            # حفظ الملف
            new_workbook.save(file_path)
            new_workbook.close()

            messagebox.showinfo("نجح التصدير", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات:\n{str(e)}")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """معالج إغلاق النافذة"""
        self.destroy()
