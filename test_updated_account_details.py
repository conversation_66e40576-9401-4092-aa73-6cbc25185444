#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تفاصيل الحساب المحدثة مع آلية الترحيل الجديدة
"""

import sys
import traceback
import tkinter as tk
from tkinter import messagebox

def main():
    """اختبار نافذة تفاصيل الحساب المحدثة"""
    
    print("🧪 اختبار نافذة تفاصيل الحساب المحدثة")
    print("=" * 60)
    print("🔄 تم تحديث آلية قراءة المستندات لتتطابق مع آلية الترحيل")
    print("📋 سيتم فحص جميع الجداول والأقسام في الورقة")
    print("=" * 60)
    
    try:
        # استيراد الوحدات
        print("📦 استيراد الوحدات...")
        from excel_manager import ExcelManager
        from manage_accounts import ManageAccountsDialog
        
        print("✅ تم استيراد الوحدات بنجاح")
        
        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        root.title("اختبار نافذة تفاصيل الحساب المحدثة")
        root.geometry("900x700")
        
        # إنشاء مدير Excel
        print("📊 إنشاء مدير Excel...")
        excel_manager = ExcelManager()
        
        # تحميل ملف Excel
        print("📁 تحميل ملف Excel...")
        if excel_manager.load_data():
            print("✅ تم تحميل ملف Excel بنجاح")
            
            # الحصول على قائمة الحسابات
            accounts = excel_manager.get_accounts_list()
            print(f"📋 تم العثور على {len(accounts)} حساب")
            
            if accounts:
                # البحث عن حساب "test" أو أول حساب متاح
                test_account = None
                for account in accounts:
                    account_num, account_name, balance = account
                    if "test" in account_name.lower() or "test" in str(account_num):
                        test_account = account
                        break
                
                if not test_account:
                    test_account = accounts[0]  # استخدم أول حساب
                
                account_num, account_name, balance = test_account
                print(f"🎯 سيتم اختبار الحساب: {account_name} ({account_num})")
                
                # إنشاء نافذة إدارة الحسابات
                print("🏗️ إنشاء نافذة إدارة الحسابات...")
                manage_dialog = ManageAccountsDialog(root, excel_manager)
                
                # إنشاء دالة لفتح تفاصيل الحساب مباشرة
                def test_account_details():
                    try:
                        print(f"🚀 فتح تفاصيل الحساب: {account_name}")
                        
                        # استيراد نافذة تفاصيل الحساب
                        from manage_accounts import AccountDetailsDialog
                        
                        # تحديد اسم الورقة
                        sheet_name = f"{account_num}-{account_name}"
                        
                        # فحص وجود الورقة
                        if sheet_name in excel_manager.workbook.sheetnames:
                            print(f"✅ تم العثور على الورقة: {sheet_name}")
                        elif str(account_num) in excel_manager.workbook.sheetnames:
                            sheet_name = str(account_num)
                            print(f"✅ تم العثور على الورقة: {sheet_name}")
                        else:\n                            # البحث في جميع الأوراق
                            for sheet in excel_manager.workbook.sheetnames:
                                if str(account_num) in sheet or account_name in sheet:
                                    sheet_name = sheet
                                    print(f"✅ تم العثور على الورقة: {sheet}")
                                    break
                        
                        # إنشاء نافذة التفاصيل
                        print("📋 إنشاء نافذة تفاصيل الحساب...")
                        details_dialog = AccountDetailsDialog(
                            root, excel_manager, sheet_name, account_num, account_name
                        )
                        
                        print("✅ تم إنشاء نافذة التفاصيل بنجاح!")
                        print("📄 راقب وحدة التحكم لرؤية تفاصيل قراءة المستندات")
                        
                    except Exception as e:
                        error_msg = f"خطأ في فتح تفاصيل الحساب: {str(e)}"
                        print(f"🚨 {error_msg}")
                        print(f"📋 التفاصيل:\n{traceback.format_exc()}")
                        messagebox.showerror("خطأ", error_msg)
                
                # إنشاء واجهة الاختبار
                print("🎨 إنشاء واجهة الاختبار...")
                
                # إطار رئيسي
                main_frame = tk.Frame(root, padx=20, pady=20)
                main_frame.pack(fill=tk.BOTH, expand=True)
                
                # عنوان
                title_label = tk.Label(main_frame, 
                                     text="اختبار نافذة تفاصيل الحساب المحدثة", 
                                     font=("Arial", 18, "bold"),
                                     fg="#2c3e50")
                title_label.pack(pady=(0, 20))
                
                # معلومات التحديث
                update_info = tk.Label(main_frame,
                                     text="🔄 تم تحديث آلية قراءة المستندات لتتطابق مع آلية الترحيل",
                                     font=("Arial", 12),
                                     fg="#27ae60")
                update_info.pack(pady=(0, 10))
                
                # معلومات الحساب
                info_frame = tk.LabelFrame(main_frame, text="معلومات الحساب المختار", 
                                         font=("Arial", 12, "bold"))
                info_frame.pack(fill=tk.X, pady=(0, 20))
                
                tk.Label(info_frame, text=f"رقم الحساب: {account_num}", 
                        font=("Arial", 11)).pack(anchor=tk.W, padx=10, pady=5)
                tk.Label(info_frame, text=f"اسم الحساب: {account_name}", 
                        font=("Arial", 11)).pack(anchor=tk.W, padx=10, pady=5)
                tk.Label(info_frame, text=f"الرصيد: {balance:,.2f}", 
                        font=("Arial", 11)).pack(anchor=tk.W, padx=10, pady=5)
                
                # معلومات التحديثات
                updates_frame = tk.LabelFrame(main_frame, text="التحديثات الجديدة", 
                                            font=("Arial", 12, "bold"))
                updates_frame.pack(fill=tk.X, pady=(0, 20))
                
                updates_text = tk.Text(updates_frame, height=8, width=70, wrap=tk.WORD)
                updates_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
                
                updates_content = \"\"\"التحديثات في آلية قراءة المستندات:

✅ فحص جميع الجداول في الورقة (ليس فقط الجدول الأول)
✅ البحث في جميع الأقسام الستة (A, D, G, J, M, P) 
✅ تجاهل المعادلات والخلايا الفارغة
✅ عرض معلومات الجدول والقسم لكل مستند
✅ معالجة محسنة للأخطاء مع رسائل تشخيصية
✅ توافق كامل مع آلية الترحيل المستخدمة في إضافة المستندات

الآن ستظهر جميع المستندات التي أضفتها في حساب test!\"\"\"\n                \n                updates_text.insert(tk.END, updates_content)\n                updates_text.config(state=tk.DISABLED)\n                \n                # أزرار الاختبار\n                buttons_frame = tk.Frame(main_frame)\n                buttons_frame.pack(pady=20)\n                \n                # زر فتح تفاصيل الحساب\n                test_btn = tk.Button(buttons_frame, \n                                   text=\"🔍 فتح تفاصيل الحساب المحدث\", \n                                   command=test_account_details,\n                                   bg='#3498db', fg='white', \n                                   font=("Arial", 14, \"bold\"),\n                                   padx=30, pady=15)\n                test_btn.pack(side=tk.LEFT, padx=10)\n                \n                # زر فتح إدارة الحسابات العادية\n                manage_btn = tk.Button(buttons_frame,\n                                     text=\"📋 فتح إدارة الحسابات\",\n                                     command=lambda: manage_dialog.deiconify(),\n                                     bg='#2ecc71', fg='white',\n                                     font=("Arial", 12, \"bold\"),\n                                     padx=20, pady=10)\n                manage_btn.pack(side=tk.LEFT, padx=10)\n                \n                # معلومات إضافية\n                info_label = tk.Label(main_frame,\n                                    text=\"💡 راقب وحدة التحكم لرؤية تفاصيل قراءة المستندات\",\n                                    font=("Arial", 10),\n                                    fg=\"#7f8c8d\")\n                info_label.pack(pady=(20, 0))\n                \n                print(\"✅ تم إعداد واجهة الاختبار\")\n                print(\"🚀 بدء تشغيل الاختبار...\")\n                print(\"-\" * 60)\n                \n                # تشغيل التطبيق\n                root.mainloop()\n                \n            else:\n                print(\"❌ لا توجد حسابات في ملف Excel\")\n                messagebox.showerror(\"خطأ\", \"لا توجد حسابات في ملف Excel\")\n        else:\n            print(\"❌ فشل في تحميل ملف Excel\")\n            messagebox.showerror(\"خطأ\", \"فشل في تحميل ملف Excel\\nتأكد من وجود ملف accounting_system.xlsx\")\n    \n    except ImportError as e:\n        error_msg = f\"خطأ في الاستيراد: {str(e)}\"\n        print(f\"🚨 {error_msg}\")\n        print(\"💡 تأكد من وجود الملفات:\")\n        print(\"   - excel_manager.py\")\n        print(\"   - manage_accounts.py\")\n        \n    except Exception as e:\n        error_msg = f\"خطأ عام: {str(e)}\"\n        print(f\"🚨 {error_msg}\")\n        print(f\"📋 التفاصيل:\\n{traceback.format_exc()}\")\n\nif __name__ == \"__main__\":\n    main()
