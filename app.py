import openpyxl
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font
# from ttkthemes import ThemedTk  # نستخدم Tk العادي بدلاً منه
from excel_manager import ExcelManager
from multi_excel_manager import MultiExcelManager
from document_window import AddDocumentWindow
from search_window import <PERSON>Window
from manage_accounts import ManageAccountsDialog
from user_manager import UserManager, LoginWindow, UserManagementWindow, ChangePasswordWindow
from receipts_account_window import AddReceiptsAccountWindow
from receipts_document_window import AddReceiptsDocumentWindow
from manage_receipts_accounts import ManageReceiptsAccountsDialog
from receipts_search_window import ReceiptsSearchWindow
from datetime import datetime
import os
import sys

class AccountingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        self.root.geometry("1200x700")

        # إعداد نظام المستخدمين
        self.user_manager = UserManager()

        # إعداد نظام Excel مع دعم الملفات المتعددة
        self.excel = ExcelManager()  # سيقوم بإنشاء الملفين تلقائياً

        # إعداد التحديث التلقائي
        self.auto_refresh_enabled = True
        self.last_file_modified = None

        # =============================================================================
        # نظام إدارة النوافذ المفتوحة
        # =============================================================================
        self.open_windows = {
            'manage_accounts': None,
            'add_account': None,
            'add_document': None,
            'search': None,
            'user_management': None,
            'change_password': None,
            'account_details': [],  # قائمة لأنه يمكن فتح عدة نوافذ تفاصيل
            'reports': None,
            'receipts_account': None,  # نافذة إضافة حساب مقبوضات
            'receipts_document': None,  # نافذة إضافة مستند مقبوضات
            'manage_receipts_accounts': None,  # نافذة إدارة حسابات المقبوضات
            'receipts_search': None,  # نافذة البحث في المقبوضات
            'account_balances': None  # نافذة تقارير أرصدة الحسابات
        }

        # إخفاء النافذة الرئيسية حتى تسجيل الدخول
        self.root.withdraw()

        # إعداد إغلاق النافذة قبل عرض نافذة تسجيل الدخول
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        except Exception as e:
            print(f"خطأ في إعداد بروتوكول الإغلاق: {str(e)}")

        # عرض نافذة تسجيل الدخول
        self.login_window = None
        self.show_login()

    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.login_window = LoginWindow(self.user_manager, self.on_login_success)
        # تشغيل حلقة الأحداث لنافذة تسجيل الدخول
        self.login_window.root.mainloop()

    def on_login_success(self):
        """عند نجاح تسجيل الدخول"""
        try:
            # إغلاق نافذة تسجيل الدخول بأمان
            if self.login_window and hasattr(self.login_window, 'root'):
                try:
                    self.login_window.root.destroy()
                except:
                    pass  # تجاهل الخطأ إذا كانت مغلقة بالفعل

            # إظهار النافذة الرئيسية
            self.root.deiconify()

            # إعداد الواجهة الحديثة
            self.setup_modern_ui()

            # بدء التحديث التلقائي
            if hasattr(self, 'start_auto_refresh'):
                self.start_auto_refresh()
            else:
                print("⚠️ دالة start_auto_refresh غير موجودة")

            # تحديث عنوان النافذة ليشمل اسم المستخدم
            user_info = self.user_manager.get_user_info()
            if user_info:
                self.root.title(f"نظام إدارة المستندات المحاسبية - {user_info['full_name']} ({user_info['role']})")

            # تشغيل حلقة الأحداث للنافذة الرئيسية
            self.root.mainloop()

        except Exception as e:
            print(f"خطأ في on_login_success: {str(e)}")
            # في حالة الخطأ، أظهر النافذة على أي حال
            self.root.deiconify()
            self.setup_modern_ui()
            self.root.mainloop()

    def check_permission(self, permission):
        """فحص صلاحية معينة"""
        if not self.user_manager.has_permission(permission):
            messagebox.showerror("غير مسموح", "ليس لديك صلاحية لهذه العملية")
            return False
        return True

    # =============================================================================
    # دوال إدارة النوافذ المفتوحة
    # =============================================================================

    def is_window_open(self, window_type):
        """فحص إذا كانت نافذة معينة مفتوحة"""
        try:
            if window_type in self.open_windows:
                window = self.open_windows[window_type]
                if window_type == 'account_details':
                    # تنظيف النوافذ المغلقة
                    self.open_windows[window_type] = [w for w in window if w and w.winfo_exists()]
                    return len(self.open_windows[window_type]) > 0
                else:
                    return window is not None and window.winfo_exists()
            return False
        except:
            # في حالة الخطأ، اعتبر النافذة مغلقة
            self.close_window(window_type)
            return False

    def register_window(self, window_type, window):
        """تسجيل نافذة جديدة"""
        try:
            if window_type == 'account_details':
                if window_type not in self.open_windows:
                    self.open_windows[window_type] = []
                self.open_windows[window_type].append(window)
            else:
                self.open_windows[window_type] = window

            # ربط حدث إغلاق النافذة
            window.protocol("WM_DELETE_WINDOW", lambda: self.on_window_close(window_type, window))
            print(f"✅ تم تسجيل نافذة: {window_type}")
        except Exception as e:
            print(f"❌ خطأ في تسجيل النافذة: {str(e)}")

    def close_window(self, window_type, window=None):
        """إغلاق نافذة وإزالتها من القائمة"""
        try:
            if window_type == 'account_details' and window:
                if window_type in self.open_windows:
                    if window in self.open_windows[window_type]:
                        self.open_windows[window_type].remove(window)
            else:
                self.open_windows[window_type] = None
            print(f"✅ تم إغلاق نافذة: {window_type}")
        except Exception as e:
            print(f"❌ خطأ في إغلاق النافذة: {str(e)}")

    def on_window_close(self, window_type, window):
        """معالج حدث إغلاق النافذة"""
        try:
            self.close_window(window_type, window)
            window.destroy()
        except Exception as e:
            print(f"❌ خطأ في معالج إغلاق النافذة: {str(e)}")

    def focus_existing_window(self, window_type):
        """تركيز على نافذة موجودة"""
        try:
            if window_type in self.open_windows:
                window = self.open_windows[window_type]
                if window and window.winfo_exists():
                    window.lift()  # رفع النافذة للمقدمة
                    window.focus_force()  # تركيز على النافذة
                    window.attributes('-topmost', True)  # جعلها في المقدمة
                    window.after(100, lambda: window.attributes('-topmost', False))  # إزالة الخاصية بعد قليل
                    return True
            return False
        except:
            return False

    def manage_users(self):
        """فتح نافذة إدارة المستخدمين"""
        if self.check_permission('manage_users'):
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if self.is_window_open('user_management'):
                if self.focus_existing_window('user_management'):
                    messagebox.showinfo("تنبيه", "نافذة إدارة المستخدمين مفتوحة بالفعل")
                    return

            # فتح نافذة جديدة
            window = UserManagementWindow(self.root, self.user_manager)
            self.register_window('user_management', window)

    def change_password(self):
        """فتح نافذة تغيير كلمة المرور"""
        # فحص إذا كانت النافذة مفتوحة بالفعل
        if self.is_window_open('change_password'):
            if self.focus_existing_window('change_password'):
                messagebox.showinfo("تنبيه", "نافذة تغيير كلمة المرور مفتوحة بالفعل")
                return

        # فتح نافذة جديدة
        window = ChangePasswordWindow(self.root, self.user_manager)
        self.register_window('change_password', window)

    def show_about(self):
        """عرض نافذة عن البرنامج"""
        messagebox.showinfo("عن البرنامج",
                           "🏦 نظام إدارة المستندات المحاسبية\n"
                           "الإصدار: 2.0 - محسن\n"
                           "تطوير: فريق التطوير\n"
                           "العام: 2025")

    def show_help(self):
        """عرض دليل المستخدم"""
        help_text = (
            "📚 دليل المستخدم - نظام إدارة المستندات\n\n"
            "➕ إضافة حساب: لإضافة حساب جديد\n"
            "📄 إضافة مستند: لإضافة مستند جديد\n"
            "🔍 البحث: للبحث في المستندات\n"
            "📊 التقارير: لعرض التقارير\n"
            "⚙️ إدارة الحسابات: لإدارة الحسابات"
        )
        messagebox.showinfo("دليل المستخدم", help_text)

    def backup_data(self):
        """عمل نسخة احتياطية"""
        if self.check_permission('backup_restore'):
            try:
                from datetime import datetime
                import shutil

                # اسم ملف النسخة الاحتياطية
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_filename = f"backup_{timestamp}.xlsx"

                # نسخ الملف
                shutil.copy2(self.excel.file_path, backup_filename)

                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية: {backup_filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_data(self):
        """استعادة نسخة احتياطية"""
        if self.check_permission('backup_restore'):
            try:
                file_path = filedialog.askopenfilename(
                    title="اختر ملف النسخة الاحتياطية",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
                )

                if file_path:
                    if messagebox.askyesno("تأكيد", "هل أنت متأكد من استعادة هذه النسخة؟ \nسيتم استبدال البيانات الحالية."):
                        # إعادة تحميل الملف
                        self.excel = ExcelManager(file_path)
                        messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في استعادة النسخة: {str(e)}")

    def create_detailed_report(self):
        """إنشاء تقرير مفصل"""
        if self.check_permission('view_reports'):
            try:
                # يمكن تطوير هذه الدالة لاحقاً
                messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

    def search_accounts(self):
        """بحث في الحسابات"""
        # فحص إذا كانت نافذة البحث مفتوحة بالفعل
        if self.is_window_open('search'):
            if self.focus_existing_window('search'):
                messagebox.showinfo("تنبيه", "نافذة البحث مفتوحة بالفعل")
                return

        # فتح نافذة جديدة (مع المعامل الصحيح)
        window = SearchWindow(self)
        self.register_window('search', window)

    def search_documents(self):
        """بحث في المستندات"""
        # استخدام نفس نافذة البحث
        self.search_accounts()

    def add_account(self):
        """إضافة حساب جديد"""
        if self.check_permission('add_account'):
            # فحص إذا كانت نافذة إضافة حساب مفتوحة بالفعل
            if self.is_window_open('add_account'):
                if self.focus_existing_window('add_account'):
                    messagebox.showinfo("تنبيه", "نافذة إضافة حساب مفتوحة بالفعل")
                    return

            # فتح نافذة جديدة
            window = AddAccountDialog(self.root, self.excel)
            self.register_window('add_account', window)

    def add_document(self):
        """إضافة مستند جديد"""
        if self.check_permission('add_document'):
            # فحص إذا كانت نافذة إضافة مستند مفتوحة بالفعل
            if self.is_window_open('add_document'):
                if self.focus_existing_window('add_document'):
                    messagebox.showinfo("تنبيه", "نافذة إضافة مستند مفتوحة بالفعل")
                    return

            # فتح نافذة جديدة
            window = AddDocumentWindow(self)
            self.register_window('add_document', window)

    def manage_accounts(self):
        """إدارة الحسابات المحسنة"""
        if self.check_permission('edit_account'):
            # فحص إذا كانت نافذة إدارة الحسابات مفتوحة بالفعل
            if self.is_window_open('manage_accounts'):
                if self.focus_existing_window('manage_accounts'):
                    messagebox.showinfo("تنبيه", "نافذة إدارة الحسابات مفتوحة بالفعل")
                    return

            try:
                # فتح نافذة جديدة
                window = ManageAccountsDialog(self.root, self.excel)
                self.register_window('manage_accounts', window)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الحسابات: {str(e)}")

    def add_receipts_account(self):
        """فتح نافذة إضافة حساب مقبوضات"""
        if self.check_permission('add_account'):
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if self.is_window_open('receipts_account'):
                if self.focus_existing_window('receipts_account'):
                    messagebox.showinfo("تنبيه", "نافذة إضافة حساب مقبوضات مفتوحة بالفعل")
                    return

            try:
                # فتح نافذة جديدة
                window = AddReceiptsAccountWindow(self)
                self.register_window('receipts_account', window)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إضافة حساب مقبوضات: {str(e)}")

    def add_receipts_document(self):
        """فتح نافذة إضافة مستند مقبوضات"""
        if self.check_permission('add_document'):
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if self.is_window_open('receipts_document'):
                if self.focus_existing_window('receipts_document'):
                    messagebox.showinfo("تنبيه", "نافذة إضافة مستند مقبوضات مفتوحة بالفعل")
                    return

            try:
                # فتح نافذة جديدة
                window = AddReceiptsDocumentWindow(self)
                self.register_window('receipts_document', window)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إضافة مستند مقبوضات: {str(e)}")

    def manage_receipts_accounts(self):
        """فتح نافذة إدارة حسابات المقبوضات"""
        if self.check_permission('edit_account'):
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if self.is_window_open('manage_receipts_accounts'):
                if self.focus_existing_window('manage_receipts_accounts'):
                    messagebox.showinfo("تنبيه", "نافذة إدارة حسابات المقبوضات مفتوحة بالفعل")
                    return

            try:
                # فتح نافذة جديدة
                window = ManageReceiptsAccountsDialog(self)
                self.register_window('manage_receipts_accounts', window)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة حسابات المقبوضات: {str(e)}")

    def search_receipts(self):
        """فتح نافذة البحث في المقبوضات"""
        # فحص إذا كانت النافذة مفتوحة بالفعل
        if self.is_window_open('receipts_search'):
            if self.focus_existing_window('receipts_search'):
                messagebox.showinfo("تنبيه", "نافذة البحث في المقبوضات مفتوحة بالفعل")
                return

        try:
            # فتح نافذة جديدة
            window = ReceiptsSearchWindow(self)
            self.register_window('receipts_search', window)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة البحث في المقبوضات: {str(e)}")

    def switch_to_deductions_file(self):
        """التبديل إلى ملف الخصومات"""
        try:
            deductions_file = "Accounting system deductions.xlsx"

            # فحص وجود الملف
            if not os.path.exists(deductions_file):
                messagebox.showwarning("تنبيه", f"ملف الخصومات غير موجود: {deductions_file}")
                return

            # حفظ الملف الحالي
            self.excel.save_workbook()

            # تحميل ملف الخصومات
            self.excel.current_file = deductions_file
            self.excel.file_path = deductions_file
            self.excel.workbook = openpyxl.load_workbook(deductions_file)

            # تحديث عنوان النافذة
            user_info = self.user_manager.get_user_info()
            if user_info:
                self.root.title(f"نظام إدارة الخصومات المحاسبية - {user_info['full_name']} ({user_info['role']})")

            self.update_status("تم التبديل إلى ملف الخصومات")
            messagebox.showinfo("نجاح", "تم التبديل إلى ملف الخصومات بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التبديل إلى ملف الخصومات: {str(e)}")

    def switch_to_main_file(self):
        """التبديل إلى الملف الرئيسي"""
        try:
            main_file = "accounting_system.xlsx"

            # فحص وجود الملف
            if not os.path.exists(main_file):
                messagebox.showwarning("تنبيه", f"الملف الرئيسي غير موجود: {main_file}")
                return

            # حفظ الملف الحالي
            self.excel.save_workbook()

            # تحميل الملف الرئيسي
            self.excel.current_file = main_file
            self.excel.file_path = main_file
            self.excel.workbook = openpyxl.load_workbook(main_file)

            # تحديث عنوان النافذة
            user_info = self.user_manager.get_user_info()
            if user_info:
                self.root.title(f"نظام إدارة المستندات المحاسبية - {user_info['full_name']} ({user_info['role']})")

            self.update_status("تم التبديل إلى الملف الرئيسي")
            messagebox.showinfo("نجاح", "تم التبديل إلى الملف الرئيسي بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التبديل إلى الملف الرئيسي: {str(e)}")

    def show_files_manager(self):
        """عرض نافذة إدارة الملفات"""
        try:
            FilesManagerWindow(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الملفات: {str(e)}")

    def get_current_file_info(self):
        """الحصول على معلومات الملف الحالي"""
        current_file = self.excel.current_file
        if "deductions" in current_file.lower():
            return {
                'name': current_file,
                'type': 'نظام الخصومات',
                'description': 'ملف إدارة الخصومات والاستقطاعات'
            }
        else:
            return {
                'name': current_file,
                'type': 'النظام الرئيسي',
                'description': 'ملف الحسابات والمستندات الرئيسي'
            }

    def show_account_balances_window(self):
        """فتح نافذة تقارير أرصدة الحسابات"""
        if self.check_permission('view_reports'):
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if self.is_window_open('account_balances'):
                if self.focus_existing_window('account_balances'):
                    messagebox.showinfo("تنبيه", "نافذة تقارير الأرصدة مفتوحة بالفعل")
                    return

            try:
                # فتح نافذة جديدة
                from account_balances_window import AccountBalancesWindow
                window = AccountBalancesWindow(self)
                self.register_window('account_balances', window)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة تقارير الأرصدة: {str(e)}")

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من تسجيل الخروج؟"):
            try:
                # حفظ تلقائي
                self.excel.save_workbook()
            except:
                pass

            # تسجيل الخروج
            self.user_manager.logout()

            # إخفاء النافذة الرئيسية
            self.root.withdraw()

            # عرض نافذة تسجيل الدخول مرة أخرى
            self.show_login()

    def add_new_user(self):
        """إضافة مستخدم جديد (للمدير فقط)"""
        if self.check_permission('manage_users'):
            try:
                UserManagementWindow(self.root, self.user_manager)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة المستخدمين: {str(e)}")

    def admin_change_user_password(self):
        """تغيير كلمة مرور مستخدم (للمدير فقط)"""
        if self.check_permission('manage_users'):
            try:
                ChangePasswordWindow(self.root, self.user_manager)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة تغيير كلمة المرور: {str(e)}")

    def delete_user(self):
        """حذف مستخدم (للمدير فقط)"""
        if self.check_permission('manage_users'):
            try:
                UserManagementWindow(self.root, self.user_manager)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة المستخدمين: {str(e)}")

    def show_user_statistics(self):
        """عرض إحصائيات المستخدمين"""
        if self.check_permission('manage_users'):
            try:
                # عرض إحصائيات بسيطة
                users_count = len(self.user_manager.users)
                current_user = self.user_manager.current_user
                messagebox.showinfo("إحصائيات المستخدمين",
                                   f"📊 إحصائيات النظام\n\n"
                                   f"👥 عدد المستخدمين: {users_count}\n"
                                   f"👤 المستخدم الحالي: {current_user}\n"
                                   f"🔒 نوع الحساب: {self.user_manager.get_user_role(current_user)}")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في عرض الإحصائيات: {str(e)}")

    def on_closing(self):
        """معالجة إغلاق النافذة مع حفظ تلقائي"""
        self.exit_application()

    def exit_application(self):
        """خروج من التطبيق مع حفظ البيانات"""
        if messagebox.askyesno("🚪 تأكيد الخروج",
                              "هل تريد الخروج من النظام؟\n\n💾 سيتم حفظ جميع البيانات تلقائياً."):
            try:
                # حفظ تلقائي
                self.excel.save_workbook()
                print("✅ تم حفظ البيانات بنجاح")
                messagebox.showinfo("👋 وداعاً", "شكراً لاستخدام نظام إدارة المستندات المحاسبية!")
            except Exception as e:
                print(f"⚠️ خطأ في حفظ البيانات: {str(e)}")
                if messagebox.askyesno("خطأ في الحفظ",
                                     f"حدث خطأ في الحفظ: {str(e)}\nهل تريد الإغلاق بدون حفظ؟"):
                    self.root.destroy()
                    return

            # إغلاق التطبيق
            self.root.destroy()

    def setup_modern_ui(self):
        """إعداد الواجهة الحديثة مع الصلاحيات"""
        # تعريف الخط العربي
        self.root.option_add("*font", "Arial 12")
        self.root.configure(bg='#f0f8ff')  # لون أزرق فاتح

        # إعداد القوائم مع الصلاحيات
        self.create_menu_with_permissions()

        # إعداد الإطار الرئيسي
        self.create_main_frame_with_permissions()

        # إعداد شريط الحالة
        self.create_status_bar_with_user_info()

    def create_main_frame_with_permissions(self):
        """إنشاء الإطار الرئيسي مع الصلاحيات"""
        # إطار رئيسي مع لون جميل
        main_content_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_content_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # تعيين وزن الصفوف والأعمدة
        self.root.grid_rowconfigure(1, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # عنوان الترحيب
        welcome_frame = tk.Frame(main_content_frame, bg='#ffffff', relief='raised', bd=2)
        welcome_frame.pack(fill=tk.X, padx=10, pady=(10, 20))

        user_info = self.user_manager.get_user_info()
        if user_info:
            welcome_text = f"👋 مرحباً {user_info['full_name']}\n💼 نظام إدارة المستندات المحاسبية"
        else:
            welcome_text = "💼 نظام إدارة المستندات المحاسبية"

        welcome_label = tk.Label(welcome_frame, text=welcome_text,
                                 font=("Arial", 16, "bold"), fg='#2c3e50', bg='#ffffff')
        welcome_label.pack(pady=20)

        # إطار الأزرار الرئيسية
        buttons_frame = tk.Frame(main_content_frame, bg='#f8f9fa')
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء شبكة من الأزرار
        self.create_action_buttons(buttons_frame)

    def create_action_buttons(self, parent_frame):
        """إنشاء أزرار العمليات الرئيسية"""
        # قائمة الأزرار مع الصلاحيات
        buttons_config = [
            # =============================================================================
            # قسم الحسابات الرئيسية
            # =============================================================================
            {
                'text': '📁 إضافة حساب جديد',
                'command': self.add_account,
                'permission': 'add_account',
                'color': '#3498db',
                'hover_color': '#2980b9'
            },
            {
                'text': '📝 إضافة مستند',
                'command': self.add_document,
                'permission': 'add_document',
                'color': '#27ae60',
                'hover_color': '#229954'
            },
            {
                'text': '⚙️ إدارة الحسابات',
                'command': self.manage_accounts,
                'permission': 'edit_account',
                'color': '#e74c3c',
                'hover_color': '#c0392b'
            },

            # =============================================================================
            # قسم المقبوضات
            # =============================================================================
            {
                'text': '💰 إضافة حساب مقبوضات',
                'command': self.add_receipts_account,
                'permission': 'add_account',
                'color': '#16a085',
                'hover_color': '#138d75'
            },
            {
                'text': '📄 إضافة مستند مقبوضات',
                'command': self.add_receipts_document,
                'permission': 'add_document',
                'color': '#2ecc71',
                'hover_color': '#27ae60'
            },
            {
                'text': '⚙️ إدارة حسابات المقبوضات',
                'command': self.manage_receipts_accounts,
                'permission': 'edit_account',
                'color': '#8e44ad',
                'hover_color': '#7d3c98'
            },
            {
                'text': '🔍 البحث في المقبوضات',
                'command': self.search_receipts,
                'permission': None,  # متاح للجميع
                'color': '#e74c3c',
                'hover_color': '#c0392b'
            },

            # =============================================================================
            # قسم البحث والتقارير
            # =============================================================================
            {
                'text': '🔍 بحث في الحسابات',
                'command': self.search_accounts,
                'permission': None,  # متاح للجميع
                'color': '#f39c12',
                'hover_color': '#e67e22'
            },
            {
                'text': '📊 نافذة تقارير الأرصدة',
                'command': self.show_account_balances_window,
                'permission': 'view_reports',
                'color': '#e67e22',
                'hover_color': '#d35400'
            },

            # =============================================================================
            # قسم إدارة النظام
            # =============================================================================
            {
                'text': '👥 إدارة المستخدمين',
                'command': self.manage_users,
                'permission': 'manage_users',
                'color': '#34495e',
                'hover_color': '#2c3e50'
            },
            {
                'text': '🚪 خروج من النظام',
                'command': self.exit_application,
                'permission': None,  # متاح للجميع
                'color': '#95a5a6',
                'hover_color': '#7f8c8d'
            }
        ]

        # إنشاء الأزرار في شبكة
        row = 0
        col = 0
        max_cols = 3

        for btn_config in buttons_config:
            # فحص الصلاحية
            if btn_config['permission'] and not self.user_manager.has_permission(btn_config['permission']):
                continue

            # إنشاء الزر
            btn = tk.Button(parent_frame,
                           text=btn_config['text'],
                           command=btn_config['command'],
                           font=("Arial", 12, "bold"),
                           bg=btn_config['color'],
                           fg='white',
                           relief='flat',
                           padx=20,
                           pady=15,
                           cursor='hand2',
                           width=25)

            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')

            # إضافة تأثيرات بصرية
            self.add_button_hover_effect(btn, btn_config['color'], btn_config['hover_color'])

            # انتقال للعمود التالي
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # تعيين وزن الأعمدة
        for i in range(max_cols):
            parent_frame.grid_columnconfigure(i, weight=1)

    def add_button_hover_effect(self, button, normal_color, hover_color):
        """إضافة تأثير التمرير على الزر"""
        def on_enter(e):
            button.config(bg=hover_color)

        def on_leave(e):
            button.config(bg=normal_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Label(self.root, text="جاهز",
                                  bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)
        self.root.update_idletasks()

    def create_status_bar_with_user_info(self):
        """إنشاء شريط الحالة مع معلومات المستخدم"""
        status_frame = tk.Frame(self.root, bg='#e6f3ff')
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)

        # معلومات المستخدم
        user_info = self.user_manager.get_user_info()
        if user_info:
            user_label = tk.Label(status_frame,
                                 text=f"المستخدم: {user_info['full_name']} | الدور: {user_info['role']}",
                                 bg='#e6f3ff', font=('Arial', 10))
            user_label.pack(side=tk.LEFT, padx=10)

        # زر تسجيل الخروج
        logout_btn = tk.Button(status_frame, text="تسجيل خروج",
                              command=self.logout, bg='#ff6b6b', fg='white',
                              font=('Arial', 9, 'bold'))
        logout_btn.pack(side=tk.RIGHT, padx=10)

        # شريط الحالة العادي
        self.status_bar = tk.Label(status_frame, text="جاهز",
                                  bd=1, relief=tk.SUNKEN, anchor=tk.W, bg='white')
        self.status_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(20, 10))

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من تسجيل الخروج؟"):
            # حفظ العمل
            try:
                self.excel.save_workbook()
            except:
                pass

            # تسجيل الخروج
            self.user_manager.logout()

            # إخفاء النافذة الرئيسية
            self.root.withdraw()

            # عرض نافذة تسجيل الدخول مرة أخرى
            self.show_login()

    def create_menu_with_permissions(self):
        """إنشاء القوائم مع فحص الصلاحيات"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="فتح ملف", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_command(label="حفظ باسم", command=self.save_as)
        file_menu.add_separator()

        # نسخ احتياطي (فقط للمديرين)
        if self.user_manager.has_permission('backup_restore'):
            file_menu.add_command(label="نسخ احتياطي", command=self.backup_data)
            file_menu.add_command(label="استعادة نسخة", command=self.restore_data)
            file_menu.add_separator()

        file_menu.add_command(label="خروج", command=self.on_closing)

        # قائمة الحسابات
        accounts_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الحسابات", menu=accounts_menu)

        if self.user_manager.has_permission('add_account'):
            accounts_menu.add_command(label="إضافة حساب جديد", command=self.add_account)
            accounts_menu.add_command(label="💰 إضافة حساب مقبوضات", command=self.add_receipts_account)

        if self.user_manager.has_permission('edit_account'):
            accounts_menu.add_command(label="إدارة الحسابات", command=self.manage_accounts)
            accounts_menu.add_command(label="⚙️ إدارة حسابات المقبوضات", command=self.manage_receipts_accounts)

        accounts_menu.add_command(label="بحث في الحسابات", command=self.search_accounts)

        # قائمة المستندات
        documents_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المستندات", menu=documents_menu)

        if self.user_manager.has_permission('add_document'):
            documents_menu.add_command(label="إضافة مستند", command=self.add_document)
            documents_menu.add_command(label="📄 إضافة مستند مقبوضات", command=self.add_receipts_document)

        documents_menu.add_command(label="بحث في المستندات", command=self.search_documents)

        # قائمة التقارير
        if self.user_manager.has_permission('view_reports'):
            reports_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="التقارير", menu=reports_menu)
            reports_menu.add_command(label="نافذة تقارير الأرصدة", command=self.show_account_balances_window)
            reports_menu.add_command(label="تقرير أرصدة الحسابات", command=self.show_summary_report)
            reports_menu.add_command(label="تقرير مفصل", command=self.create_detailed_report)

        # قائمة المستخدمين (فقط للمديرين)
        if self.user_manager.has_permission('manage_users'):
            users_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="👥 إدارة المستخدمين", menu=users_menu)
            users_menu.add_command(label="📋 عرض جميع المستخدمين", command=self.manage_users)
            users_menu.add_separator()
            users_menu.add_command(label="➕ إضافة مستخدم جديد", command=self.add_new_user)
            users_menu.add_command(label="🔒 تغيير كلمة مرور مستخدم", command=self.admin_change_user_password)
            users_menu.add_command(label="❌ حذف مستخدم", command=self.delete_user)
            users_menu.add_separator()
            users_menu.add_command(label="📊 إحصائيات المستخدمين", command=self.show_user_statistics)

        # قائمة المستخدم الحالي
        user_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="👤 المستخدم", menu=user_menu)
        user_menu.add_command(label="🔑 تغيير كلمة المرور", command=self.change_password)
        user_menu.add_separator()
        user_menu.add_command(label="🚪 تسجيل خروج", command=self.logout)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="عن البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)





    def show_add_account(self):
        """عرض نافذة إضافة حساب (مع منع التكرار)"""
        self.add_account()

    def show_manage_accounts_new(self):
        """عرض نافذة إدارة الحسابات (مع منع التكرار)"""
        self.manage_accounts()

    def show_add_document(self):
        """عرض نافذة إضافة مستند (مع منع التكرار)"""
        self.add_document()

    def show_search(self):
        """عرض نافذة البحث (مع منع التكرار)"""
        self.search_accounts()

    def show_summary_report(self):
        """عرض التقرير الإجمالي"""
        try:
            self.update_status("جاري إنشاء التقرير الإجمالي...")
            self.excel.create_summary_report()
            self.update_status("تم إنشاء التقرير الإجمالي بنجاح")
            messagebox.showinfo("نجاح", "تم إنشاء التقرير الإجمالي بنجاح")
        except Exception as e:
            self.update_status("خطأ في إنشاء التقرير الإجمالي")
            messagebox.showerror("خطأ", str(e))

    def show_report(self):
        try:
            self.update_status("جاري إنشاء التقرير...")
            self.excel.create_report()
            self.update_status("تم إنشاء التقرير بنجاح")
            messagebox.showinfo("نجاح", "تم إنشاء التقرير بنجاح")
        except Exception as e:
            self.update_status("خطأ في إنشاء التقرير")
            messagebox.showerror("خطأ", str(e))

    def show_documents_report(self):
        """عرض تقرير المستندات"""
        try:
            # إنشاء نافذة منبثقة لاختيار الحساب
            dialog = tk.Toplevel(self.root)
            dialog.title("اختيار الحساب")
            dialog.geometry("300x150")

            ttk.Label(dialog, text="اختر الحساب:").pack(pady=10)

            # قائمة منسدلة للحسابات
            accounts = [sheet for sheet in self.excel.workbook.sheetnames
                       if sheet not in ['التقارير', 'تقرير المستندات']]
            accounts.insert(0, "جميع الحسابات")

            account_var = tk.StringVar()
            account_combo = ttk.Combobox(dialog, textvariable=account_var, values=accounts)
            account_combo.current(0)
            account_combo.pack(pady=10)

            def generate_report():
                selected = account_var.get()
                account = None if selected == "جميع الحسابات" else selected
                if self.excel.create_documents_report(account):
                    messagebox.showinfo("نجاح", "تم إنشاء تقرير المستندات بنجاح")
                dialog.destroy()

            ttk.Button(dialog, text="إنشاء التقرير",
                      command=generate_report).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", str(e))

    def export_all_accounts_to_excel(self):
        """تصدير جميع الحسابات إلى ملف Excel بالتنسيق المرجعي"""
        try:
            print("📊 بدء عملية تصدير جميع الحسابات...")

            # التحقق من وجود حسابات
            if not self.excel.workbook or not self.excel.workbook.sheetnames:
                messagebox.showwarning("تنبيه", "لا توجد حسابات للتصدير!")
                return

            # فلترة الحسابات (استثناء الصفحات الخاصة)
            special_sheets = ['مرحباً', 'التقارير', 'تقرير المستندات', 'التقرير الإجمالي']
            accounts = [sheet for sheet in self.excel.workbook.sheetnames if sheet not in special_sheets]

            if not accounts:
                messagebox.showwarning("تنبيه", "لا توجد حسابات للتصدير!")
                return

            # عرض نافذة تأكيد
            result = messagebox.askyesno("تأكيد التصدير",
                                       f"هل تريد تصدير جميع الحسابات إلى ملف Excel?\n\n"
                                       f"عدد الحسابات: {len(accounts)}\n"
                                       f"الحسابات: {', '.join(accounts[:3])}{'...' if len(accounts) > 3 else ''}\n\n"
                                       f"سيتم إنشاء ملف جديد بالتنسيق المرجعي.")

            if not result:
                return

            # تحديث شريط الحالة
            self.update_status("جاري تصدير جميع الحسابات...")

            # استدعاء دالة التصدير من ExcelManager
            success, filename = self.excel.export_all_accounts_to_excel_reference_format()

            if success:
                self.update_status(f"تم التصدير بنجاح: {filename}")
                messagebox.showinfo("نجاح التصدير",
                                   f"✅ تم تصدير جميع الحسابات بنجاح!\n\n"
                                   f"📁 اسم الملف: {filename}\n"
                                   f"📊 عدد الحسابات: {len(accounts)}\n"
                                   f"🎨 التنسيق: مطابق للنظام المرجعي\n\n"
                                   f"يحتوي الملف على ورقة منفصلة لكل حساب بنفس التنسيق الأصلي.")
            else:
                self.update_status("فشل في التصدير")
                messagebox.showerror("خطأ في التصدير",
                                    f"❌ فشل في تصدير الحسابات!\n\n"
                                    f"الرجاء التأكد من:\n"
                                    f"• عدم فتح ملف Excel في برنامج آخر\n"
                                    f"• وجود مساحة كافية على القرص\n"
                                    f"• صلاحيات الكتابة في المجلد")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء عملية التصدير: {str(e)}"
            print(f"❌ {error_msg}")
            self.update_status("خطأ في التصدير")
            messagebox.showerror("خطأ", error_msg)

    def open_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.excel = ExcelManager()
                self.excel.workbook = openpyxl.load_workbook(file_path)
                self.excel.current_file = file_path
                self.excel.file_path = file_path
                self.update_status(f"تم فتح الملف: {file_path}")
                messagebox.showinfo("نجاح", "تم فتح الملف بنجاح")
            except Exception as e:
                self.update_status("خطأ في فتح الملف")
                messagebox.showerror("خطأ", str(e))

    def save_file(self):
        try:
            if self.excel.save_workbook():
                messagebox.showinfo("نجاح", "تم حفظ الملف بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الملف")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")

    def save_as_file(self):
        """حفظ باسم جديد"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            try:
                if self.excel.save_workbook(file_path):
                    messagebox.showinfo("نجاح", f"تم حفظ الملف: {file_path}")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ الملف")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")

    def save_as(self):
        """حفظ باسم جديد (نفس save_as_file)"""
        self.save_as_file()

# =============================================================================
# نظام إدارة الحسابات الجديد - معاد التصميم بالكامل
# =============================================================================

class ManageAccountsDialogNew(tk.Toplevel):
    """نظام إدارة الحسابات الجديد والمحسن"""

    def __init__(self, parent, excel):
        super().__init__(parent)
        self.excel = excel
        self.parent = parent

        # إعداد النافذة
        self.setup_window()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_accounts_data()

        # توسيط النافذة
        self.center_window()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.title("🏦 إدارة الحسابات - نظام محسن")
        self.geometry("1000x700")
        self.configure(bg='#f8f9fa')
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(self.parent)
        self.grab_set()

        # ربط إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة المستخدم الرئيسية"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # شريط العنوان
        self.create_header(main_frame)

        # قائمة الحسابات
        self.create_accounts_list(main_frame)

        # لوحة التحكم
        self.create_control_panel(main_frame)

        # شريط الحالة
        self.create_status_bar(main_frame)

    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # عنوان رئيسي
        title_label = ttk.Label(header_frame, text="🏦 إدارة الحسابات",
                               font=('Arial', 16, 'bold'))
        title_label.pack(side=tk.LEFT)

        # زر التحديث
        refresh_btn = ttk.Button(header_frame, text="🔄 تحديث",
                                command=self.refresh_accounts)
        refresh_btn.pack(side=tk.RIGHT, padx=(10, 0))

    def create_accounts_list(self, parent):
        """إنشاء قائمة الحسابات"""
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="📈 قائمة الحسابات", padding="10")
        list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # تعريف الأعمدة
        columns = ('#', 'رقم الحساب', 'اسم الحساب', 'الرصيد', 'عدد المستندات', 'حالة الحساب')

        # إنشاء الجدول
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        column_widths = {'#': 50, 'رقم الحساب': 120, 'اسم الحساب': 200,
                        'الرصيد': 120, 'عدد المستندات': 120, 'حالة الحساب': 100}

        for col in columns:
            self.accounts_tree.heading(col, text=col, anchor=tk.CENTER)
            self.accounts_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.accounts_tree.xview)

        self.accounts_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # وضع العناصر
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # تكوين الشبكة
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # ربط الأحداث
        self.accounts_tree.bind('<Double-1>', self.on_account_double_click)
        self.accounts_tree.bind('<Button-3>', self.show_context_menu)
        self.accounts_tree.bind('<<TreeviewSelect>>', self.on_account_select)

    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.LabelFrame(parent, text="🎛️ لوحة التحكم", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(control_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(row1_frame, text="➕ إضافة حساب جديد",
                  command=self.add_new_account).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row1_frame, text="✏️ تعديل الحساب",
                  command=self.edit_selected_account).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row1_frame, text="🗑️ حذف الحساب",
                  command=self.delete_selected_account).pack(side=tk.LEFT, padx=(0, 10))

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(control_frame)
        row2_frame.pack(fill=tk.X, pady=(0, 10))

        # تم حذف أزرار تفاصيل الحساب بناءً على طلب المستخدم
        pass

        # الصف الثالث من الأزرار
        row3_frame = ttk.Frame(control_frame)
        row3_frame.pack(fill=tk.X)

        ttk.Button(row3_frame, text="🔍 بحث متقدم",
                  command=self.advanced_search).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row3_frame, text="🔄 تحديث البيانات",
                  command=self.refresh_accounts).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row3_frame, text="❌ إغلاق",
                  command=self.on_closing).pack(side=tk.RIGHT)

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))

        self.status_label = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # عداد الحسابات
        self.accounts_count_label = ttk.Label(status_frame, text="عدد الحسابات: 0")
        self.accounts_count_label.pack(side=tk.RIGHT, padx=(10, 0))

    # =============================================================================
    # دوال تحميل ومعالجة البيانات
    # =============================================================================

    def load_accounts_data(self):
        """تحميل بيانات الحسابات"""
        try:
            # مسح البيانات الحالية
            for item in self.accounts_tree.get_children():
                self.accounts_tree.delete(item)

            account_count = 0

            # طباعة تشخيصية
            print(f"تشخيص: عدد أوراق Excel: {len(self.excel.workbook.sheetnames)}")
            print(f"تشخيص: أسماء الأوراق: {self.excel.workbook.sheetnames}")

            # تحميل الحسابات من Excel
            for sheet_name in self.excel.workbook.sheetnames:
                print(f"تشخيص: فحص الورقة: {sheet_name}")

                if sheet_name not in ['التقارير', 'تقرير المستندات', 'الملخص']:
                    print(f"تشخيص: الورقة {sheet_name} مقبولة للمعالجة")
                    try:
                        # استخراج معلومات الحساب
                        account_info = self.extract_account_info(sheet_name)
                        print(f"تشخيص: معلومات الحساب: {account_info}")

                        if account_info:
                            account_count += 1
                            print(f"تشخيص: إضافة الحساب رقم {account_count}: {account_info['name']}")

                            # إضافة الحساب للجدول
                            self.accounts_tree.insert('', 'end', values=(
                                account_count,
                                account_info['number'],
                                account_info['name'],
                                f"{account_info['balance']:,.2f}",
                                account_info['documents_count'],
                                account_info['status']
                            ))
                        else:
                            print(f"تشخيص: لم يتم استخراج معلومات من {sheet_name}")
                    except Exception as e:
                        print(f"خطأ في تحميل الحساب {sheet_name}: {str(e)}")
                else:
                    print(f"تشخيص: تجاهل الورقة: {sheet_name}")

            print(f"تشخيص: إجمالي عدد الحسابات المحملة: {account_count}")

            # تحديث عداد الحسابات
            self.accounts_count_label.config(text=f"عدد الحسابات: {account_count}")
            self.update_status(f"تم تحميل {account_count} حساب بنجاح")

            # إظهار رسالة إذا لم يتم تحميل أي حساب
            if account_count == 0:
                messagebox.showwarning("تنبيه", "لم يتم العثور على أي حسابات في ملف Excel.\nالرجاء التأكد من وجود حسابات في الملف.")

        except Exception as e:
            self.update_status(f"خطأ في تحميل البيانات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الحسابات: {str(e)}")

    def extract_account_info(self, sheet_name):
        """استخراج معلومات الحساب من اسم الورقة"""
        try:
            # تحليل اسم الورقة (رقم-اسم)
            if '-' in sheet_name:
                parts = sheet_name.split('-', 1)
                account_number = parts[0].strip()
                account_name = parts[1].strip()
            else:
                account_number = sheet_name
                account_name = sheet_name

            # حساب الرصيد وعدد المستندات
            ws = self.excel.workbook[sheet_name]
            balance = self.calculate_account_balance(ws)
            documents_count = self.count_documents(ws)

            # تحديد حالة الحساب
            if balance > 0:
                status = "نشط ✅"
            elif balance == 0:
                status = "متوازن ⚖️"
            else:
                status = "مدين ⚠️"

            return {
                'number': account_number,
                'name': account_name,
                'balance': balance,
                'documents_count': documents_count,
                'status': status,
                'sheet_name': sheet_name
            }

        except Exception as e:
            print(f"خطأ في استخراج معلومات الحساب {sheet_name}: {str(e)}")
            return None

    def calculate_account_balance(self, worksheet):
        """حساب رصيد الحساب بطريقة صحيحة"""
        try:
            balance = 0
            # البحث عن آخر رصيد في عمود الرصيد (E)
            for row in range(worksheet.max_row, 1, -1):  # البحث من الأسفل للأعلى
                cell_value = worksheet.cell(row=row, column=5).value  # عمود E (الرصيد)
                if cell_value is not None and isinstance(cell_value, (int, float)):
                    balance = cell_value
                    break

            # إذا لم نجد رصيد، نحسب من المبالغ
            if balance == 0:
                for row in range(2, worksheet.max_row + 1):
                    amount_cell = worksheet.cell(row=row, column=4).value  # عمود D (المبلغ)
                    if amount_cell and isinstance(amount_cell, (int, float)):
                        balance += amount_cell

            return balance
        except Exception as e:
            print(f"خطأ في حساب الرصيد: {str(e)}")
            return 0

    def count_documents(self, worksheet):
        """عد المستندات في الحساب بطريقة صحيحة"""
        try:
            count = 0
            # البحث عن المستندات في عمود رقم المستند (A)
            for row in range(2, worksheet.max_row + 1):  # بدء من الصف 2
                cell_value = worksheet.cell(row=row, column=1).value  # عمود A
                if cell_value is not None and str(cell_value).strip():
                    # تحقق من أن هذا ليس صف مجموع
                    if not str(cell_value).strip().lower() in ['مجموع', 'المجموع', 'total', 'اجمالي']:
                        count += 1
            return count
        except Exception as e:
            print(f"خطأ في عد المستندات: {str(e)}")
            return 0

    # =============================================================================
    # دوال الأحداث والتفاعل
    # =============================================================================

    def on_account_select(self, event):
        """عند اختيار حساب"""
        selection = self.accounts_tree.selection()
        if selection:
            item = self.accounts_tree.item(selection[0])
            values = item['values']
            if values:
                self.selected_account = {
                    'number': values[1],
                    'name': values[2],
                    'balance': values[3],
                    'documents_count': values[4],
                    'status': values[5]
                }
                self.update_status(f"تم اختيار الحساب: {values[2]}")

    def on_account_double_click(self, event):
        """عند النقر المزدوج على حساب"""
        self.show_account_details()

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # تحديد العنصر المنقور عليه
        item = self.accounts_tree.identify_row(event.y)
        if item:
            self.accounts_tree.selection_set(item)

            # إنشاء القائمة السياقية (بدون تفاصيل الحساب)
            context_menu = tk.Menu(self, tearoff=0)
            context_menu.add_command(label="✏️ تعديل الحساب", command=self.edit_selected_account)
            context_menu.add_separator()
            context_menu.add_command(label="🗑️ حذف الحساب", command=self.delete_selected_account)

            # عرض القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    # =============================================================================
    # دوال عمليات الحسابات
    # =============================================================================

    def add_new_account(self):
        """إضافة حساب جديد"""
        try:
            AddAccountDialog(self.parent, self.excel)
            # تحديث القائمة بعد إضافة الحساب
            self.refresh_accounts()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إضافة حساب: {str(e)}")

    def edit_selected_account(self):
        """تعديل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتعديله")
            return

        try:
            item = self.accounts_tree.item(selection[0])
            values = item['values']
            account_number = values[1]
            account_name = values[2]

            # فتح نافذة تعديل الحساب
            EditAccountDialog(self, account_number, account_name)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الحساب: {str(e)}")

    def delete_selected_account(self):
        """حذف الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لحذفه")
            return

        try:
            item = self.accounts_tree.item(selection[0])
            values = item['values']
            account_name = values[2]
            account_balance = values[3]

            # تأكيد الحذف
            if messagebox.askyesno("تأكيد الحذف",
                                  f"هل أنت متأكد من حذف الحساب:\n\n"
                                  f"الاسم: {account_name}\n"
                                  f"الرصيد: {account_balance}\n\n"
                                  f"⚠️ هذه العملية لا يمكن التراجع عنها!"):

                # حذف الحساب من Excel
                sheet_name = f"{values[1]}-{values[2]}"
                if sheet_name in self.excel.workbook.sheetnames:
                    del self.excel.workbook[sheet_name]

                    # حفظ التغييرات
                    if self.excel.save_workbook():
                        messagebox.showinfo("نجاح", f"تم حذف الحساب '{account_name}' بنجاح")
                        self.refresh_accounts()
                    else:
                        messagebox.showerror("خطأ", "فشل في حفظ التغييرات")
                else:
                    messagebox.showerror("خطأ", "لم يتم العثور على الحساب في ملف Excel")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف الحساب: {str(e)}")

    # تم حذف دالة show_account_details بناءً على طلب المستخدم

    # تم حذف دالة generate_account_report بناءً على طلب المستخدم

    # تم حذف دالة export_account_data بناءً على طلب المستخدم

    def advanced_search(self):
        """بحث متقدم في الحسابات"""
        try:
            AdvancedSearchDialog(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة البحث: {str(e)}")

    # تم حذف دالة show_comprehensive_details بناءً على طلب المستخدم

    def refresh_accounts(self):
        """تحديث قائمة الحسابات"""
        self.update_status("جاري تحديث البيانات...")
        self.load_accounts_data()

    # =============================================================================
    # دوال مساعدة
    # =============================================================================

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        self.update_idletasks()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        try:
            # حفظ أي تغييرات معلقة
            self.excel.save_workbook()
            self.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {str(e)}")
            self.destroy()

# =============================================================================
# نوافذ مساعدة لنظام إدارة الحسابات
# =============================================================================

class EditAccountDialog(tk.Toplevel):
    """نافذة تعديل الحساب"""

    def __init__(self, parent, account_number, account_name):
        super().__init__(parent)
        self.parent = parent
        self.account_number = account_number
        self.account_name = account_name

        self.title(f"✏️ تعديل الحساب: {account_name}")
        self.geometry("400x200")
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_interface()
        self.center_window()

    def create_interface(self):
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # حقول التعديل
        ttk.Label(main_frame, text="رقم الحساب:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.number_var = tk.StringVar(value=self.account_number)
        ttk.Entry(main_frame, textvariable=self.number_var, width=30).grid(row=0, column=1, pady=5)

        ttk.Label(main_frame, text="اسم الحساب:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar(value=self.account_name)
        ttk.Entry(main_frame, textvariable=self.name_var, width=30).grid(row=1, column=1, pady=5)

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=2, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="✅ حفظ", command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.destroy).pack(side=tk.LEFT, padx=5)

    def save_changes(self):
        # هنا يمكن إضافة منطق حفظ التغييرات
        messagebox.showinfo("نجاح", "تم حفظ التغييرات")
        self.destroy()

    def center_window(self):
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (self.winfo_width() // 2)
        y = (self.winfo_screenheight() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")

# تم حذف كلاس AccountDetailsDialog بناءً على طلب المستخدم

class AdvancedSearchDialog(tk.Toplevel):
    """نافذة البحث المتقدم"""

    def __init__(self, parent):
        super().__init__(parent)
        self.title("🔍 بحث متقدم في الحسابات")
        self.geometry("500x300")

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        # واجهة البحث
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="بحث متقدم في الحسابات",
                 font=('Arial', 14, 'bold')).pack(pady=10)

        ttk.Button(main_frame, text="إغلاق", command=self.destroy).pack(pady=10)

# =============================================================================
# تم حذف نافذة التفاصيل الشاملة بناءً على طلب المستخدم
# =============================================================================



# تم حذف جميع محتويات كلاس ComprehensiveAccountDetailsDialog

# تم حذف الكلاس المكرر - الكلاس الصحيح في الأسفل

# =============================================================================
# نوافذ إدارة الحسابات المحسنة (محذوفة)
# =============================================================================

# تم حذف الكلاسات المكررة وغير الضرورية

# تم حذف الكلاسات المكررة - الكلاس الصحيح في نهاية الملف

# =============================================================================
# نوافذ مساعدة أخرى
# =============================================================================

# تم حذف الكلاسات المكررة وغير الضرورية

# =============================================================================
# نوافذ مساعدة بسيطة
# =============================================================================

class PrintPreviewWindow(tk.Toplevel):
    def __init__(self, parent, title="معاينة الطباعة"):
        super().__init__(parent)
        self.title(title)
        self.geometry("600x400")

        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = ttk.Label(main_frame, text="➕ إضافة حساب جديد",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))

        # إطار بيانات الحساب
        data_frame = ttk.Frame(main_frame)
        data_frame.pack(fill=tk.X, pady=(0, 20))

        # رقم الحساب
        ttk.Label(data_frame, text="💼 رقم الحساب:").grid(row=0, column=0, padx=5, pady=8, sticky=tk.W)
        self.account_num = ttk.Entry(data_frame, width=30, font=("Arial", 10))
        self.account_num.grid(row=0, column=1, padx=5, pady=8, sticky=tk.W)

        # اسم الحساب
        ttk.Label(data_frame, text="🏢 اسم الحساب:").grid(row=1, column=0, padx=5, pady=8, sticky=tk.W)
        self.account_name = ttk.Entry(data_frame, width=30, font=("Arial", 10))
        self.account_name.grid(row=1, column=1, padx=5, pady=8, sticky=tk.W)

        # الرصيد الأولي
        ttk.Label(data_frame, text="💰 الرصيد الأولي:").grid(row=2, column=0, padx=5, pady=8, sticky=tk.W)
        self.balance = ttk.Entry(data_frame, width=30, font=("Arial", 10))
        self.balance.grid(row=2, column=1, padx=5, pady=8, sticky=tk.W)
        self.balance.insert(0, "0.00")

        # ملاحظات
        ttk.Label(data_frame, text="📝 ملاحظات:").grid(row=3, column=0, padx=5, pady=8, sticky=tk.NW)
        self.notes = tk.Text(data_frame, width=30, height=3, font=("Arial", 10))
        self.notes.grid(row=3, column=1, padx=5, pady=8, sticky=tk.W)

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # الأزرار الرئيسية
        add_btn = ttk.Button(buttons_frame, text="✅ إضافة الحساب",
                            command=self.add_account, width=20)
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        add_close_btn = ttk.Button(buttons_frame, text="✅ إضافة وإغلاق",
                                  command=self.add_and_close, width=20)
        add_close_btn.pack(side=tk.LEFT, padx=(0, 10))

        clear_btn = ttk.Button(buttons_frame, text="🧹 مسح",
                              command=self.clear_fields, width=15)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ttk.Button(buttons_frame, text="❌ إلغاء",
                               command=self.destroy, width=15)
        cancel_btn.pack(side=tk.RIGHT)

        # ربط المفاتيح
        self.bind('<Escape>', lambda e: self.destroy())
        self.bind('<Control-Return>', lambda e: self.add_account())

        # تعيين التركيز الأولي
        self.account_num.focus_set()

    def clear_fields(self):
        """مسح جميع حقول الإدخال"""
        self.account_num.delete(0, tk.END)
        self.account_name.delete(0, tk.END)
        self.balance.delete(0, tk.END)
        self.balance.insert(0, "0.00")
        if hasattr(self, 'notes'):
            self.notes.delete(1.0, tk.END)
        self.account_num.focus_set()

    def add_and_close(self):
        """إضافة الحساب وإغلاق النافذة مباشرة"""
        # نفس منطق add_account لكن مع إغلاق مباشر
        try:
            # التحقق من صحة البيانات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()
            balance_text = self.balance.get().strip()

            if not account_num:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الحساب")
                return

            if not account_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return

            try:
                initial_balance = float(balance_text) if balance_text else 0
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيمة رقمية صحيحة للرصيد")
                return

            # محاولة إنشاء الحساب
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance, "official")

            if result:
                messagebox.showinfo("نجاح", f"تم إضافة الحساب '{account_name}' بنجاح")
                self.destroy()  # إغلاق مباشر
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def add_account(self):
        """إضافة حساب جديد"""
        try:
            # التحقق من المدخلات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()

            if not account_num:
                messagebox.showerror("خطأ", "الرجاء إدخال رقم الحساب")
                self.account_num.focus_set()
                return

            if not account_name:
                messagebox.showerror("خطأ", "الرجاء إدخال اسم الحساب")
                self.account_name.focus_set()
                return

            try:
                initial_balance = float(self.balance.get() or "0")
            except ValueError:
                messagebox.showerror("خطأ", "الرجاء إدخال رقم صحيح للرصيد")
                self.balance.focus_set()
                return

            # محاولة إنشاء الحساب
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance, "official")

            if result:
                messagebox.showinfo("نجاح", f"تم إضافة الحساب '{account_name}' بنجاح")
                # مسح الحقول للحساب التالي
                self.clear_fields()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب. قد يكون رقم الحساب موجود مسبقاً.")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
        details_frame = ttk.LabelFrame(parent, text="📊 تفاصيل الحساب", padding="10")
        details_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(1, weight=1)

        # معلومات الحساب
        self.create_account_info(details_frame)

        # جدول المستندات
        self.create_documents_table(details_frame)

        # أزرار التحكم
        self.create_control_buttons(details_frame)

    def create_account_info(self, parent):
        """إنشاء قسم معلومات الحساب"""
        info_frame = ttk.Frame(parent)
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # عنوان الحساب
        self.account_title = ttk.Label(info_frame, text="اختر حساباً لعرض تفاصيله",
                                      font=('Arial', 14, 'bold'), foreground="gray")
        self.account_title.pack(anchor=tk.W)

        # معلومات مفصلة
        self.account_details = ttk.Label(info_frame, text="", font=('Arial', 10))
        self.account_details.pack(anchor=tk.W, pady=(5, 0))

    def create_documents_table(self, parent):
        """إنشاء جدول المستندات"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تعريف الأعمدة
        columns = ('رقم المستند', 'التاريخ', 'البيان', 'المبلغ', 'الرصيد')

        # إنشاء الجدول
        self.documents_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # تعيين عناوين الأعمدة
        column_widths = {'رقم المستند': 100, 'التاريخ': 100,
                        'البيان': 200, 'المبلغ': 120, 'الرصيد': 120}

        for col in columns:
            self.documents_tree.heading(col, text=col, anchor=tk.CENTER)
            self.documents_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.documents_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.documents_tree.xview)

        self.documents_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # وضع العناصر
        self.documents_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # تكوين الشبكة
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # ربط الأحداث
        self.documents_tree.bind('<Double-1>', self.on_document_double_click)
        self.documents_tree.bind('<Button-3>', self.show_document_context_menu)

    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(15, 0))

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(row1_frame, text="➕ إضافة مستند",
                  command=self.add_document).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row1_frame, text="✏️ تعديل مستند",
                  command=self.edit_document).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row1_frame, text="🗑️ حذف مستند",
                  command=self.delete_document).pack(side=tk.LEFT, padx=(0, 10))

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(row2_frame, text="📈 إضافة جدول",
                  command=self.add_new_table).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row2_frame, text="💾 تصدير الحساب",
                  command=self.export_account).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(row2_frame, text="🖨️ طباعة",
                  command=self.print_account).pack(side=tk.LEFT, padx=(0, 10))

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(15, 0))

        self.status_label = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # عداد الحسابات والمستندات
        self.counts_label = ttk.Label(status_frame, text="")
        self.counts_label.pack(side=tk.RIGHT, padx=(10, 0))

    # =============================================================================
    # دوال تحميل ومعالجة البيانات
    # =============================================================================

    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        try:
            # مسح القائمة الحالية
            self.accounts_listbox.delete(0, tk.END)

            account_count = 0

            # تحميل الحسابات من Excel
            for sheet_name in self.excel.workbook.sheetnames:
                if sheet_name not in ['التقارير', 'تقرير المستندات', 'الملخص']:
                    try:
                        # استخراج معلومات الحساب
                        account_info = self.extract_account_info(sheet_name)
                        if account_info:
                            account_count += 1
                            display_text = f"{account_info['number']} - {account_info['name']} (رصيد: {account_info['balance']:,.2f})"
                            self.accounts_listbox.insert(tk.END, display_text)
                    except Exception as e:
                        print(f"خطأ في تحميل الحساب {sheet_name}: {str(e)}")

            # تحديث عداد الحسابات
            self.counts_label.config(text=f"عدد الحسابات: {account_count}")
            self.update_status(f"تم تحميل {account_count} حساب بنجاح")

        except Exception as e:
            self.update_status(f"خطأ في تحميل الحسابات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحميل الحسابات: {str(e)}")

    def extract_account_info(self, sheet_name):
        """استخراج معلومات الحساب"""
        try:
            # تحليل اسم الورقة
            if '-' in sheet_name:
                parts = sheet_name.split('-', 1)
                account_number = parts[0].strip()
                account_name = parts[1].strip()
            else:
                account_number = sheet_name
                account_name = sheet_name

            # حساب الرصيد وعدد المستندات
            ws = self.excel.workbook[sheet_name]
            balance = self.calculate_account_balance(ws)
            documents_count = self.count_documents_correct(ws)

            return {
                'number': account_number,
                'name': account_name,
                'balance': balance,
                'documents_count': documents_count,
                'sheet_name': sheet_name
            }

        except Exception as e:
            print(f"خطأ في استخراج معلومات الحساب {sheet_name}: {str(e)}")
            return None

    def calculate_account_balance(self, worksheet):
        """حساب رصيد الحساب"""
        try:
            balance = 0
            # البحث عن آخر رصيد في عمود الرصيد
            for row in range(worksheet.max_row, 1, -1):
                cell_value = worksheet.cell(row=row, column=5).value  # عمود E (الرصيد)
                if cell_value is not None and isinstance(cell_value, (int, float)):
                    balance = cell_value
                    break
            return balance
        except:
            return 0

    def count_documents_correct(self, worksheet):
        """عد المستندات بطريقة صحيحة"""
        try:
            count = 0
            # البحث عن المستندات في عمود رقم المستند (A)
            for row in range(2, worksheet.max_row + 1):  # بدء من الصف 2
                cell_value = worksheet.cell(row=row, column=1).value  # عمود A
                if cell_value is not None and str(cell_value).strip():
                    # تحقق من أن هذا ليس صف مجموع
                    if not str(cell_value).strip().lower() in ['مجموع', 'المجموع', 'total', 'اجمالي']:
                        count += 1
            return count
        except Exception as e:
            print(f"خطأ في عد المستندات: {str(e)}")
            return 0

    # =============================================================================
    # دوال الأحداث والتفاعل
    # =============================================================================

    def on_account_select(self, event):
        """عند اختيار حساب من القائمة"""
        try:
            selection = self.accounts_listbox.curselection()
            if selection:
                selected_text = self.accounts_listbox.get(selection[0])
                # استخراج رقم الحساب من النص
                account_number = selected_text.split(' - ')[0]

                # البحث عن الورقة المطابقة
                for sheet_name in self.excel.workbook.sheetnames:
                    if sheet_name.startswith(account_number + '-') or sheet_name == account_number:
                        self.selected_account_sheet = sheet_name
                        self.load_account_details(sheet_name)
                        break

        except Exception as e:
            self.update_status(f"خطأ في اختيار الحساب: {str(e)}")

    def on_account_double_click(self, event):
        """عند النقر المزدوج على حساب"""
        # يمكن إضافة عملية مختلفة هنا
        pass

    def load_account_details(self, sheet_name):
        """تحميل تفاصيل الحساب المحدد"""
        try:
            # مسح الجدول الحالي
            for item in self.documents_tree.get_children():
                self.documents_tree.delete(item)

            # استخراج معلومات الحساب
            account_info = self.extract_account_info(sheet_name)
            if not account_info:
                return

            # تحديث عنوان الحساب
            self.account_title.config(
                text=f"🏦 {account_info['name']} - رقم {account_info['number']}",
                foreground="blue"
            )

            # تحديث التفاصيل
            details_text = (
                f"💰 الرصيد الحالي: {account_info['balance']:,.2f}\n"
                f"📄 عدد المستندات: {account_info['documents_count']}\n"
                f"📅 آخر تحديث: {self.get_last_update_date(sheet_name)}"
            )
            self.account_details.config(text=details_text)

            # تحميل المستندات
            self.load_documents(sheet_name)

            # حفظ بيانات الحساب الحالي
            self.current_account_data = account_info

            # تحديث شريط الحالة
            self.update_status(f"تم تحميل تفاصيل الحساب: {account_info['name']}")

        except Exception as e:
            self.update_status(f"خطأ في تحميل تفاصيل الحساب: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل الحساب: {str(e)}")

    def load_documents(self, sheet_name):
        """تحميل مستندات الحساب"""
        try:
            ws = self.excel.workbook[sheet_name]
            documents_count = 0
            running_balance = 0

            # قراءة الرصيد الافتتاحي (إذا وجد)
            opening_balance_cell = ws.cell(row=1, column=5).value
            if opening_balance_cell and isinstance(opening_balance_cell, (int, float)):
                running_balance = opening_balance_cell

            # قراءة المستندات
            for row in range(2, ws.max_row + 1):
                doc_num = ws.cell(row=row, column=1).value
                if doc_num is not None and str(doc_num).strip():
                    # تحقق من أن هذا ليس صف مجموع
                    if not str(doc_num).strip().lower() in ['مجموع', 'المجموع', 'total', 'اجمالي']:
                        documents_count += 1

                        # قراءة بيانات المستند
                        date = ws.cell(row=row, column=2).value or ""
                        description = ws.cell(row=row, column=3).value or ""
                        amount = ws.cell(row=row, column=4).value or 0
                        balance = ws.cell(row=row, column=5).value or 0

                        # تنسيق البيانات
                        if isinstance(amount, (int, float)):
                            amount_str = f"{amount:,.2f}"
                        else:
                            amount_str = str(amount)

                        if isinstance(balance, (int, float)):
                            balance_str = f"{balance:,.2f}"
                        else:
                            balance_str = str(balance)

                        # إضافة المستند للجدول
                        self.documents_tree.insert('', 'end', values=(
                            str(doc_num),
                            str(date),
                            str(description),
                            amount_str,
                            balance_str
                        ))

            # تحديث عداد المستندات
            self.counts_label.config(
                text=f"عدد المستندات: {documents_count} | الرصيد: {running_balance:,.2f}"
            )

        except Exception as e:
            self.update_status(f"خطأ في تحميل المستندات: {str(e)}")

    def get_last_update_date(self, sheet_name):
        """الحصول على تاريخ آخر تحديث"""
        try:
            # يمكن تحسين هذه الدالة لاحقاً
            from datetime import datetime
            return datetime.now().strftime("%Y-%m-%d %H:%M")
        except:
            return "غير متاح"

    # =============================================================================
    # دوال عمليات المستندات والجداول
    # =============================================================================

    def on_document_double_click(self, event):
        """عند النقر المزدوج على مستند"""
        self.edit_document()

    def show_document_context_menu(self, event):
        """عرض القائمة السياقية للمستند"""
        # تحديد العنصر المنقور عليه
        item = self.documents_tree.identify_row(event.y)
        if item:
            self.documents_tree.selection_set(item)

            # إنشاء القائمة السياقية
            context_menu = tk.Menu(self, tearoff=0)
            context_menu.add_command(label="✏️ تعديل المستند", command=self.edit_document)
            context_menu.add_command(label="🗑️ حذف المستند", command=self.delete_document)
            context_menu.add_separator()
            context_menu.add_command(label="📝 نسخ المستند", command=self.copy_document)

            # عرض القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def add_document(self):
        """إضافة مستند جديد"""
        if not self.selected_account_sheet:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب أولاً")
            return

        try:
            # فتح نافذة إضافة مستند
            from document_window import AddDocumentWindow
            AddDocumentWindow(self, self.excel, self.selected_account_sheet)

            # تحديث العرض بعد إضافة المستند
            self.after(1000, lambda: self.load_account_details(self.selected_account_sheet))

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إضافة مستند: {str(e)}")

    def edit_document(self):
        """تعديل المستند المحدد"""
        selection = self.documents_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار مستند لتعديله")
            return

        try:
            item = self.documents_tree.item(selection[0])
            values = item['values']

            # فتح نافذة تعديل المستند
            EditDocumentDialog(self, values, self.selected_account_sheet)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل المستند: {str(e)}")

    def delete_document(self):
        """حذف المستند المحدد (بدون إعادة ترتيب)"""
        selection = self.documents_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار مستند لحذفه")
            return

        try:
            item = self.documents_tree.item(selection[0])
            values = item['values']
            doc_num = values[0]
            description = values[2]

            # تأكيد الحذف
            if messagebox.askyesno("تأكيد الحذف",
                                  f"هل أنت متأكد من حذف المستند:\n\n"
                                  f"رقم المستند: {doc_num}\n"
                                  f"البيان: {description}\n\n"
                                  f"⚠️ سيتم مسح المحتوى فقط بدون إعادة ترتيب!"):

                # حذف محتوى المستند بدون إعادة ترتيب
                self.delete_document_content(doc_num)

                # تحديث العرض
                self.load_account_details(self.selected_account_sheet)

                messagebox.showinfo("نجاح", f"تم مسح محتوى المستند {doc_num} بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف المستند: {str(e)}")

    def delete_document_content(self, doc_num):
        """مسح محتوى المستند بدون إعادة ترتيب"""
        try:
            ws = self.excel.workbook[self.selected_account_sheet]

            # البحث عن المستند ومسح محتواه
            for row in range(2, ws.max_row + 1):
                cell_value = ws.cell(row=row, column=1).value
                if cell_value and str(cell_value).strip() == str(doc_num).strip():
                    # مسح محتوى الصف (ترك فراغ)
                    for col in range(1, 6):  # الأعمدة A إلى E
                        ws.cell(row=row, column=col).value = None
                    break

            # حفظ التغييرات
            self.excel.save_workbook()

        except Exception as e:
            raise Exception(f"فشل في مسح محتوى المستند: {str(e)}")

    def copy_document(self):
        """نسخ بيانات المستند"""
        selection = self.documents_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار مستند لنسخه")
            return

        try:
            item = self.documents_tree.item(selection[0])
            values = item['values']

            # نسخ البيانات إلى الحافظة
            clipboard_text = f"رقم المستند: {values[0]}\nالتاريخ: {values[1]}\nالبيان: {values[2]}\nالمبلغ: {values[3]}\nالرصيد: {values[4]}"
            self.clipboard_clear()
            self.clipboard_append(clipboard_text)

            messagebox.showinfo("نجاح", "تم نسخ بيانات المستند إلى الحافظة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نسخ المستند: {str(e)}")

    def add_new_table(self):
        """إضافة جدول جديد أسفل الجدول الموجود"""
        if not self.selected_account_sheet:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب أولاً")
            return

        try:
            # فتح نافذة تأكيد إضافة جدول
            if messagebox.askyesno("تأكيد",
                                  f"هل تريد إضافة جدول جديد أسفل الجدول الموجود\n"
                                  f"في الحساب: {self.current_account_data['name']}?"):

                self.create_new_table_below()

                # تحديث العرض
                self.load_account_details(self.selected_account_sheet)

                messagebox.showinfo("نجاح", "تم إضافة جدول جديد بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة جدول جديد: {str(e)}")

    def create_new_table_below(self):
        """إنشاء جدول جديد أسفل الجدول الموجود"""
        try:
            ws = self.excel.workbook[self.selected_account_sheet]

            # العثور على آخر صف مستخدم
            last_row = ws.max_row

            # إضافة مسافة بين الجداول (3 صفوف فارغة)
            new_table_start = last_row + 4

            # إضافة عناوين الجدول الجديد
            headers = ['رقم المستند', 'التاريخ', 'البيان', 'المبلغ', 'الرصيد']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=new_table_start, column=col)
                cell.value = header
                # تنسيق العناوين
                cell.font = openpyxl.styles.Font(bold=True)
                cell.alignment = openpyxl.styles.Alignment(horizontal='center')

            # إضافة بعض الصفوف الفارغة للجدول الجديد (10 صفوف)
            for row in range(new_table_start + 1, new_table_start + 11):
                for col in range(1, 6):
                    ws.cell(row=row, column=col).value = None

            # حفظ التغييرات
            self.excel.save_workbook()

        except Exception as e:
            raise Exception(f"فشل في إنشاء جدول جديد: {str(e)}")

    # =============================================================================
    # دوال التصدير والطباعة
    # =============================================================================

    def export_account(self):
        """تصدير الحساب إلى ملف Excel"""
        if not self.selected_account_sheet:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتصديره")
            return

        try:
            # اختيار مكان حفظ الملف
            filename = filedialog.asksaveasfilename(
                title=f"تصدير الحساب: {self.current_account_data['name']}",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"حساب_{self.current_account_data['number']}_{self.current_account_data['name']}.xlsx"
            )

            if filename:
                # إنشاء ملف Excel جديد
                import openpyxl
                new_wb = openpyxl.Workbook()
                new_ws = new_wb.active
                new_ws.title = self.current_account_data['name']

                # نسخ بيانات الحساب
                source_ws = self.excel.workbook[self.selected_account_sheet]

                # نسخ جميع البيانات
                for row in source_ws.iter_rows():
                    for cell in row:
                        new_ws.cell(row=cell.row, column=cell.column).value = cell.value
                        # نسخ التنسيق أيضاً
                        if cell.font:
                            new_ws.cell(row=cell.row, column=cell.column).font = cell.font
                        if cell.alignment:
                            new_ws.cell(row=cell.row, column=cell.column).alignment = cell.alignment

                # حفظ الملف
                new_wb.save(filename)

                messagebox.showinfo("نجاح", f"تم تصدير الحساب بنجاح إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الحساب: {str(e)}")

    def print_account(self):
        """طباعة الحساب"""
        if not self.selected_account_sheet:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لطباعته")
            return

        try:
            # إنشاء نافذة معاينة الطباعة
            PrintPreviewDialog(self, self.current_account_data, self.selected_account_sheet)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في طباعة الحساب: {str(e)}")

    def export_all_accounts(self):
        """تصدير جميع الحسابات"""
        try:
            # اختيار مكان حفظ الملف
            filename = filedialog.asksaveasfilename(
                title="تصدير جميع الحسابات",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname="جميع_الحسابات.xlsx"
            )

            if filename:
                # نسخ الملف الأصلي
                import shutil
                shutil.copy2(self.excel.current_file, filename)

                messagebox.showinfo("نجاح", f"تم تصدير جميع الحسابات بنجاح إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير جميع الحسابات: {str(e)}")

    def refresh_all(self):
        """تحديث جميع البيانات"""
        try:
            # تحديث قائمة الحسابات
            self.load_accounts()

            # إعادة تحميل تفاصيل الحساب الحالي
            if self.selected_account_sheet:
                self.load_account_details(self.selected_account_sheet)

            self.update_status("تم تحديث جميع البيانات بنجاح")

        except Exception as e:
            self.update_status(f"خطأ في تحديث البيانات: {str(e)}")

    # =============================================================================
    # دوال مساعدة
    # =============================================================================

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        self.update_idletasks()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        try:
            # حفظ أي تغييرات معلقة
            self.excel.save_workbook()
            self.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {str(e)}")
            self.destroy()

# =============================================================================
# نوافذ مساعدة لنظام التفاصيل الشاملة
# =============================================================================

class EditDocumentDialog(tk.Toplevel):
    """نافذة تعديل المستند"""

    def __init__(self, parent, document_data, sheet_name):
        super().__init__(parent)
        self.parent = parent
        self.document_data = document_data
        self.sheet_name = sheet_name

        self.title(f"✏️ تعديل المستند: {document_data[0]}")
        self.geometry("400x300")
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_interface()
        self.center_window()

    def create_interface(self):
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        ttk.Label(main_frame, text="تعديل بيانات المستند",
                 font=('Arial', 12, 'bold')).pack(pady=(0, 15))

        # حقول التعديل
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.X)

        # رقم المستند
        ttk.Label(fields_frame, text="رقم المستند:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.doc_num_var = tk.StringVar(value=self.document_data[0])
        ttk.Entry(fields_frame, textvariable=self.doc_num_var, width=30).grid(row=0, column=1, pady=5)

        # التاريخ
        ttk.Label(fields_frame, text="التاريخ:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.date_var = tk.StringVar(value=self.document_data[1])
        ttk.Entry(fields_frame, textvariable=self.date_var, width=30).grid(row=1, column=1, pady=5)

        # البيان
        ttk.Label(fields_frame, text="البيان:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.description_var = tk.StringVar(value=self.document_data[2])
        ttk.Entry(fields_frame, textvariable=self.description_var, width=30).grid(row=2, column=1, pady=5)

        # المبلغ
        ttk.Label(fields_frame, text="المبلغ:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.amount_var = tk.StringVar(value=str(self.document_data[3]).replace(',', ''))
        ttk.Entry(fields_frame, textvariable=self.amount_var, width=30).grid(row=3, column=1, pady=5)

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="✅ حفظ", command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.destroy).pack(side=tk.LEFT, padx=5)

    def save_changes(self):
        # هنا يمكن إضافة منطق حفظ التغييرات
        messagebox.showinfo("نجاح", "تم حفظ التغييرات")
        self.destroy()

    def center_window(self):
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (self.winfo_width() // 2)
        y = (self.winfo_screenheight() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")

class PrintPreviewDialog(tk.Toplevel):
    """نافذة معاينة الطباعة"""

    def __init__(self, parent, account_data, sheet_name):
        super().__init__(parent)
        self.account_data = account_data
        self.sheet_name = sheet_name

        self.title(f"🖨️ معاينة طباعة: {account_data['name']}")
        self.geometry("600x600")  # زيادة الارتفاع لإظهار أزرار الطباعة والإغلاق

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        # عرض معاينة الطباعة
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text=f"معاينة طباعة الحساب: {account_data['name']}",
                 font=('Arial', 14, 'bold')).pack(pady=10)

        # أزرار الطباعة
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="🖨️ طباعة", command=self.print_document).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إغلاق", command=self.destroy).pack(side=tk.LEFT, padx=5)

    def print_document(self):
        # هنا يمكن إضافة منطق الطباعة الفعلي
        messagebox.showinfo("طباعة", "تم إرسال المستند للطباعة")
        self.destroy()

class AddAccountDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("🏦 إدارة الحسابات")
        self.excel = excel
        self.parent = parent
        self.geometry("800x600")
        self.resizable(True, True)
        self.configure(bg='#f8f9fa')

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        # إعداد الواجهة المحسنة
        self.create_enhanced_interface()

        # تحميل البيانات
        self.load_accounts_data()

        # توسيط النافذة
        self.center_window()

    def create_enhanced_interface(self):
        """إنشاء واجهة محسنة لإدارة الحسابات"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = ttk.Label(main_frame, text="🏦 إدارة الحسابات",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # إطار قائمة الحسابات الموجودة
        accounts_frame = ttk.LabelFrame(main_frame, text="📁 الحسابات الموجودة", padding="10")
        accounts_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # جدول الحسابات
        columns = ('رقم الحساب', 'اسم الحساب', 'الرصيد')
        self.accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show='headings', height=10)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=150, anchor='center')

        # شريط تمرير عمودي
        scrollbar_v = ttk.Scrollbar(accounts_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar_v.set)

        # ترتيب الجدول وشريط التمرير
        self.accounts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار أزرار إدارة الحسابات
        accounts_buttons_frame = ttk.Frame(main_frame)
        accounts_buttons_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Button(accounts_buttons_frame, text="✏️ تعديل الحساب",
                  command=self.edit_selected_account).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(accounts_buttons_frame, text="🗑️ حذف الحساب",
                  command=self.delete_selected_account).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(accounts_buttons_frame, text="🔄 تحديث القائمة",
                  command=self.load_accounts_data).pack(side=tk.LEFT, padx=(0, 10))

        # إطار إضافة حساب جديد
        add_frame = ttk.LabelFrame(main_frame, text="➕ إضافة حساب جديد", padding="15")
        add_frame.pack(fill=tk.X, pady=(0, 20))

        # صف رقم الحساب
        account_num_frame = ttk.Frame(add_frame)
        account_num_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(account_num_frame, text="🔢 رقم الحساب:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
        self.account_num_var = tk.StringVar()
        self.account_num_entry = ttk.Entry(account_num_frame, textvariable=self.account_num_var, font=('Arial', 10))
        self.account_num_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # صف اسم الحساب
        account_name_frame = ttk.Frame(add_frame)
        account_name_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(account_name_frame, text="🏢 اسم الحساب:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
        self.account_name_var = tk.StringVar()
        self.account_name_entry = ttk.Entry(account_name_frame, textvariable=self.account_name_var, font=('Arial', 10))
        self.account_name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # صف الرصيد الافتتاحي
        balance_frame = ttk.Frame(add_frame)
        balance_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(balance_frame, text="💰 الرصيد الافتتاحي:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
        self.balance_var = tk.StringVar(value="0")
        self.balance_entry = ttk.Entry(balance_frame, textvariable=self.balance_var, font=('Arial', 10))
        self.balance_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # أزرار العمليات
        buttons_frame = ttk.Frame(add_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="➕ إضافة الحساب",
                  command=self.add_new_account).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="🗑️ مسح الحقول",
                  command=self.clear_fields).pack(side=tk.LEFT, padx=(0, 10))

        # إطار أزرار الإغلاق
        close_frame = ttk.Frame(main_frame)
        close_frame.pack(fill=tk.X)

        ttk.Button(close_frame, text="❌ إغلاق",
                  command=self.destroy).pack(side=tk.RIGHT)

    def load_accounts_data(self):
        """تحميل بيانات الحسابات من الملف"""
        try:
            # مسح البيانات الحالية
            for item in self.accounts_tree.get_children():
                self.accounts_tree.delete(item)

            # جلب الحسابات من excel_manager
            accounts = self.excel.get_all_accounts()

            # إضافة الحسابات إلى الجدول
            for account in accounts:
                self.accounts_tree.insert('', tk.END, values=(
                    account['account_num'],
                    account['account_name'],
                    f"{account['balance']:.3f}"
                ))

            print(f"✅ تم تحميل {len(accounts)} حساب")

        except Exception as e:
            print(f"❌ خطأ في تحميل الحسابات: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في تحميل الحسابات: {str(e)}")

    def add_new_account(self):
        """إضافة حساب جديد"""
        try:
            # الحصول على البيانات
            account_num = self.account_num_var.get().strip()
            account_name = self.account_name_var.get().strip()
            balance = self.balance_var.get().strip()

            # التحقق من البيانات
            if not account_num or not account_name:
                messagebox.showwarning("تنبيه", "يرجى إدخال رقم الحساب واسم الحساب")
                return

            # التحقق من الرصيد
            try:
                balance_float = float(balance) if balance else 0.0
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للرصيد")
                return

            # إضافة الحساب
            success = self.excel.create_account_sheet(account_num, account_name, balance_float)

            if success:
                messagebox.showinfo("نجاح", f"تم إضافة الحساب '{account_num}-{account_name}' بنجاح")
                self.clear_fields()
                self.load_accounts_data()

        except Exception as e:
            print(f"❌ خطأ في إضافة الحساب: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في إضافة الحساب: {str(e)}")

    def clear_fields(self):
        """مسح جميع الحقول"""
        self.account_num_var.set("")
        self.account_name_var.set("")
        self.balance_var.set("0")
        self.account_num_entry.focus_set()

    def edit_selected_account(self):
        """تعديل الحساب المحدد"""
        try:
            selection = self.accounts_tree.selection()
            if not selection:
                messagebox.showwarning("تنبيه", "يرجى اختيار حساب لتعديله")
                return

            # استخراج بيانات الحساب
            item = selection[0]
            values = self.accounts_tree.item(item)['values']
            account_num, account_name, balance = values
            old_sheet_name = f"{account_num}-{account_name}"

            # فتح نافذة تعديل
            EditAccountDialog(self, self.excel, old_sheet_name, account_num, account_name)

        except Exception as e:
            print(f"❌ خطأ في تعديل الحساب: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في تعديل الحساب: {str(e)}")

    def delete_selected_account(self):
        """حذف الحساب المحدد"""
        try:
            selection = self.accounts_tree.selection()
            if not selection:
                messagebox.showwarning("تنبيه", "يرجى اختيار حساب لحذفه")
                return

            # استخراج بيانات الحساب
            item = selection[0]
            values = self.accounts_tree.item(item)['values']
            account_num, account_name, balance = values
            sheet_name = f"{account_num}-{account_name}"

            # رسالة تأكيد
            confirm_msg = f"هل أنت متأكد من حذف الحساب:\n\n"
            confirm_msg += f"🔢 رقم الحساب: {account_num}\n"
            confirm_msg += f"🏢 اسم الحساب: {account_name}\n"
            confirm_msg += f"💰 الرصيد: {balance}\n\n"
            confirm_msg += "⚠️ سيتم حذف جميع المستندات والبيانات نهائياً!"

            if messagebox.askyesno("تأكيد الحذف", confirm_msg):
                # حذف الحساب
                success = self.excel.delete_account(sheet_name)

                if success:
                    messagebox.showinfo("نجاح", f"تم حذف الحساب '{sheet_name}' بنجاح")
                    self.load_accounts_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الحساب")

        except Exception as e:
            print(f"❌ خطأ في حذف الحساب: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في حذف الحساب: {str(e)}")

    def center_window(self):
        """توسيط النافذة"""
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (self.winfo_width() // 2)
        y = (self.winfo_screenheight() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")


class EditAccountDialog(tk.Toplevel):
    """نافذة تعديل الحساب"""

    def __init__(self, parent, excel, old_sheet_name, account_num, account_name):
        super().__init__(parent)
        self.title("✏️ تعديل الحساب")
        self.excel = excel
        self.parent = parent
        self.old_sheet_name = old_sheet_name
        self.geometry("400x300")
        self.resizable(False, False)
        self.configure(bg='#f8f9fa')

        # تهيئة المتغيرات
        self.account_num_var = tk.StringVar(value=account_num)
        self.account_name_var = tk.StringVar(value=account_name)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        # إعداد الواجهة
        self.create_interface()

        # توسيط النافذة
        self.center_window()

    def create_interface(self):
        """إنشاء واجهة تعديل الحساب"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = ttk.Label(main_frame, text="✏️ تعديل بيانات الحساب",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))

        # حقل رقم الحساب
        num_frame = ttk.Frame(main_frame)
        num_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(num_frame, text="🔢 رقم الحساب:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
        self.account_num_entry = ttk.Entry(num_frame, textvariable=self.account_num_var, font=('Arial', 10))
        self.account_num_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # حقل اسم الحساب
        name_frame = ttk.Frame(main_frame)
        name_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(name_frame, text="🏢 اسم الحساب:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
        self.account_name_entry = ttk.Entry(name_frame, textvariable=self.account_name_var, font=('Arial', 10))
        self.account_name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # أزرار العمليات
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="✅ حفظ التعديلات",
                  command=self.save_changes).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="❌ إلغاء",
                  command=self.destroy).pack(side=tk.RIGHT)

    def save_changes(self):
        """حفظ التعديلات"""
        try:
            # الحصول على البيانات الجديدة
            new_account_num = self.account_num_var.get().strip()
            new_account_name = self.account_name_var.get().strip()

            # التحقق من البيانات
            if not new_account_num or not new_account_name:
                messagebox.showwarning("تنبيه", "يرجى إدخال رقم الحساب واسم الحساب")
                return

            # تعديل الحساب
            new_sheet_name = self.excel.rename_account(self.old_sheet_name, new_account_num, new_account_name)

            if new_sheet_name:
                messagebox.showinfo("نجاح", f"تم تعديل الحساب بنجاح")
                # تحديث قائمة الحسابات في النافذة الرئيسية
                if hasattr(self.parent, 'load_accounts_data'):
                    self.parent.load_accounts_data()
                self.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في تعديل الحساب")

        except Exception as e:
            print(f"❌ خطأ في حفظ التعديلات: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في حفظ التعديلات: {str(e)}")

    def center_window(self):
        """توسيط النافذة"""
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (self.winfo_width() // 2)
        y = (self.winfo_screenheight() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")

        # ملاحظات
        ttk.Label(data_frame, text="📝 ملاحظات:").grid(row=3, column=0, padx=5, pady=8, sticky=tk.NW)
        self.notes = tk.Text(data_frame, width=30, height=3, font=("Arial", 10))
        self.notes.grid(row=3, column=1, padx=5, pady=8, sticky=tk.W)

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # الأزرار الرئيسية
        add_btn = ttk.Button(buttons_frame, text="✅ إضافة الحساب",
                            command=self.add_account, width=20)
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        add_close_btn = ttk.Button(buttons_frame, text="✅ إضافة وإغلاق",
                                  command=self.add_and_close, width=20)
        add_close_btn.pack(side=tk.LEFT, padx=(0, 10))

        clear_btn = ttk.Button(buttons_frame, text="🧹 مسح",
                              command=self.clear_fields, width=15)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ttk.Button(buttons_frame, text="❌ إلغاء",
                               command=self.destroy, width=15)
        cancel_btn.pack(side=tk.RIGHT)

        # ربط المفاتيح
        self.bind('<Escape>', lambda e: self.destroy())
        self.bind('<Control-Return>', lambda e: self.add_account())

        # تعيين التركيز الأولي
        self.account_num.focus_set()

    def clear_fields(self):
        """مسح جميع حقول الإدخال"""
        self.account_num.delete(0, tk.END)
        self.account_name.delete(0, tk.END)
        self.balance.delete(0, tk.END)
        self.balance.insert(0, "0.00")
        if hasattr(self, 'notes'):
            self.notes.delete(1.0, tk.END)
        self.account_num.focus_set()

    def add_and_close(self):
        """إضافة الحساب وإغلاق النافذة مباشرة"""
        # نفس منطق add_account لكن مع إغلاق مباشر
        try:
            # التحقق من صحة البيانات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()
            balance_text = self.balance.get().strip()

            if not account_num:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الحساب")
                return

            if not account_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return

            try:
                initial_balance = float(balance_text) if balance_text else 0
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيمة رقمية صحيحة للرصيد")
                return

            # محاولة إنشاء الحساب
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance, "official")

            if result:
                messagebox.showinfo("نجاح", f"تم إضافة الحساب '{account_name}' بنجاح")
                self.destroy()  # إغلاق مباشر
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def add_account(self):
        """إضافة حساب جديد"""
        try:
            # التحقق من صحة البيانات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()
            balance_text = self.balance.get().strip()

            if not account_num:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الحساب")
                self.account_num.focus_set()
                return

            if not account_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                self.account_name.focus_set()
                return

            try:
                initial_balance = float(balance_text) if balance_text else 0
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيمة رقمية صحيحة للرصيد")
                self.balance.focus_set()
                return

            # محاولة إنشاء الحساب
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance, "official")

            if result:
                messagebox.showinfo("نجاح", f"تم إضافة الحساب '{account_name}' بنجاح")
                # مسح الحقول للحساب التالي
                self.clear_fields()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب. قد يكون رقم الحساب موجود مسبقاً.")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = ttk.Label(main_frame, text="➕ إضافة حساب جديد",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))

        # إطار الحسابات الموجودة
        existing_frame = ttk.LabelFrame(main_frame, text="📈 الحسابات الموجودة", padding="10")
        existing_frame.pack(fill=tk.X, pady=(0, 20))

        # قائمة منسدلة للحسابات الموجودة
        accounts_label = ttk.Label(existing_frame, text="📊 عرض الحسابات:")
        accounts_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.accounts_combobox = ttk.Combobox(existing_frame, width=35, state="readonly",
                                             font=("Arial", 10))
        self.accounts_combobox.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
        self.accounts_combobox.bind('<<ComboboxSelected>>', self.on_account_selected)

        # زر تحديث القائمة
        refresh_btn = ttk.Button(existing_frame, text="🔄 تحديث",
                                command=self.load_existing_accounts)
        refresh_btn.grid(row=0, column=2, padx=(10, 0))

        # معلومات الحساب المحدد
        self.account_info_label = ttk.Label(existing_frame, text="اختر حساباً لعرض معلوماته",
                                           foreground="gray")
        self.account_info_label.grid(row=1, column=0, columnspan=3, pady=(10, 0), sticky=tk.W)

        # إطار إضافة حساب جديد
        new_account_frame = ttk.LabelFrame(main_frame, text="➕ بيانات الحساب الجديد", padding="15")
        new_account_frame.pack(fill=tk.X, pady=(0, 20))

        # حقول الإدخال
        ttk.Label(new_account_frame, text="💼 رقم الحساب:").grid(row=0, column=0, padx=5, pady=8, sticky=tk.W)
        self.account_num = ttk.Entry(new_account_frame, width=35, font=("Arial", 11))
        self.account_num.grid(row=0, column=1, padx=5, pady=8, sticky=tk.W)
        self.account_num.bind('<Return>', lambda e: self.account_name.focus_set())

        ttk.Label(new_account_frame, text="🏢 اسم الحساب:").grid(row=1, column=0, padx=5, pady=8, sticky=tk.W)
        self.account_name = ttk.Entry(new_account_frame, width=35, font=("Arial", 11))
        self.account_name.grid(row=1, column=1, padx=5, pady=8, sticky=tk.W)
        self.account_name.bind('<Return>', lambda e: self.balance.focus_set())

        ttk.Label(new_account_frame, text="💰 الرصيد الأولي:").grid(row=2, column=0, padx=5, pady=8, sticky=tk.W)
        self.balance = ttk.Entry(new_account_frame, width=35, font=("Arial", 11))
        self.balance.grid(row=2, column=1, padx=5, pady=8, sticky=tk.W)
        self.balance.bind('<Return>', lambda e: self.add_account())
        self.balance.insert(0, "0.00")  # قيمة افتراضية

        # ملاحظات
        ttk.Label(new_account_frame, text="📝 ملاحظات:").grid(row=3, column=0, padx=5, pady=8, sticky=tk.NW)
        self.notes = tk.Text(new_account_frame, width=35, height=3, font=("Arial", 10))
        self.notes.grid(row=3, column=1, padx=5, pady=8, sticky=tk.W)

        # إطار خيارات الإغلاق
        close_options_frame = ttk.LabelFrame(main_frame, text="⚙️ خيارات الإغلاق", padding="10")
        close_options_frame.pack(fill=tk.X, pady=(0, 15))

        self.close_option = tk.StringVar(value="auto")
        ttk.Radiobutton(close_options_frame, text="إغلاق تلقائي بعد الإضافة",
                       variable=self.close_option, value="auto").pack(anchor=tk.W)
        ttk.Radiobutton(close_options_frame, text="البقاء مفتوحاً لإضافة حسابات أخرى",
                       variable=self.close_option, value="stay").pack(anchor=tk.W)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 5))

        # زر إضافة (حسب الخيار المحدد)
        add_btn = ttk.Button(row1_frame, text="✅ إضافة الحساب",
                            command=self.add_account)
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر إضافة وإغلاق (مباشر)
        add_close_btn = ttk.Button(row1_frame, text="✅ إضافة وإغلاق",
                                  command=self.add_and_close)
        add_close_btn.pack(side=tk.LEFT, padx=(0, 10))

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(fill=tk.X)

        # زر مسح الحقول
        clear_btn = ttk.Button(row2_frame, text="🧹 مسح الحقول",
                              command=self.clear_fields)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث القائمة
        refresh_btn = ttk.Button(row2_frame, text="🔄 تحديث القائمة",
                                command=self.load_existing_accounts)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر إلغاء
        cancel_btn = ttk.Button(row2_frame, text="❌ إلغاء",
                               command=self.destroy)
        cancel_btn.pack(side=tk.RIGHT)

        # ربط المفاتيح
        self.bind('<Escape>', lambda e: self.destroy())
        self.bind('<Control-Return>', lambda e: self.add_account())

        # تعيين التركيز الأولي
        self.account_num.focus_set()

    def load_existing_accounts(self):
        """تحميل الحسابات الموجودة في القائمة المنسدلة"""
        try:
            # مسح القائمة الحالية
            self.accounts_combobox['values'] = ()

            # جمع الحسابات من Excel
            accounts_list = []
            account_details = {}

            for sheet_name in self.excel.workbook.sheetnames:
                if sheet_name not in ['التقارير', 'تقرير المستندات', 'الملخص']:
                    try:
                        # استخراج معلومات الحساب
                        account_info = self.extract_account_info(sheet_name)
                        if account_info:
                            display_text = f"{account_info['number']} - {account_info['name']} (الرصيد: {account_info['balance']:,.2f})"
                            accounts_list.append(display_text)
                            account_details[display_text] = account_info
                    except Exception as e:
                        print(f"خطأ في تحميل الحساب {sheet_name}: {str(e)}")

            # تحديث القائمة
            if accounts_list:
                self.accounts_combobox['values'] = tuple(sorted(accounts_list))
                self.accounts_combobox.set("اختر حساباً لعرض معلوماته")
                self.account_info_label.config(text=f"تم تحميل {len(accounts_list)} حساب بنجاح ✅")
            else:
                self.accounts_combobox.set("لا توجد حسابات")
                self.account_info_label.config(text="لا توجد حسابات مضافة بعد")

            # حفظ تفاصيل الحسابات
            self.account_details = account_details

        except Exception as e:
            self.account_info_label.config(text=f"خطأ في تحميل الحسابات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحميل الحسابات: {str(e)}")

    def extract_account_info(self, sheet_name):
        """استخراج معلومات الحساب من اسم الورقة"""
        try:
            # تحليل اسم الورقة (رقم-اسم)
            if '-' in sheet_name:
                parts = sheet_name.split('-', 1)
                account_number = parts[0].strip()
                account_name = parts[1].strip()
            else:
                account_number = sheet_name
                account_name = sheet_name

            # حساب الرصيد وعدد المستندات
            ws = self.excel.workbook[sheet_name]
            balance = self.calculate_account_balance(ws)
            documents_count = self.count_documents(ws)

            return {
                'number': account_number,
                'name': account_name,
                'balance': balance,
                'documents_count': documents_count,
                'sheet_name': sheet_name
            }

        except Exception as e:
            print(f"خطأ في استخراج معلومات الحساب {sheet_name}: {str(e)}")
            return None

    def calculate_account_balance(self, worksheet):
        """حساب رصيد الحساب"""
        try:
            balance = 0
            # البحث عن خلايا المبالغ وجمعها
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, (int, float)):
                        # تحقق من أن الخلية تحتوي على مبلغ مالي
                        if cell.column == 1:  # عمود المبالغ عادة
                            balance += cell.value
            return balance
        except:
            return 0

    def count_documents(self, worksheet):
        """عد المستندات في الحساب بطريقة صحيحة"""
        try:
            count = 0
            # البحث عن المستندات في عمود رقم المستند (A)
            for row in range(2, worksheet.max_row + 1):  # بدء من الصف 2
                cell_value = worksheet.cell(row=row, column=1).value  # عمود A
                if cell_value is not None and str(cell_value).strip():
                    # تحقق من أن هذا ليس صف مجموع
                    if not str(cell_value).strip().lower() in ['مجموع', 'المجموع', 'total', 'اجمالي']:
                        count += 1
            return count
        except Exception as e:
            print(f"خطأ في عد المستندات: {str(e)}")
            return 0

    def on_account_selected(self, event):
        """عند اختيار حساب من القائمة المنسدلة"""
        try:
            selected_account = self.accounts_combobox.get()
            if selected_account and selected_account in self.account_details:
                account_info = self.account_details[selected_account]

                # عرض معلومات الحساب
                info_text = (
                    f"💼 رقم الحساب: {account_info['number']} | "
                    f"🏢 الاسم: {account_info['name']} | "
                    f"💰 الرصيد: {account_info['balance']:,.2f} | "
                    f"📄 عدد المستندات: {account_info['documents_count']}"
                )
                self.account_info_label.config(text=info_text, foreground="blue")

                # ملء حقول الإدخال ببيانات مشابهة (اختياري)
                # يمكن للمستخدم الاستفادة من هذه البيانات

        except Exception as e:
            self.account_info_label.config(text=f"خطأ في عرض معلومات الحساب: {str(e)}",
                                          foreground="red")

    def clear_fields(self):
        """مسح جميع حقول الإدخال"""
        self.account_num.delete(0, tk.END)
        self.account_name.delete(0, tk.END)
        self.balance.delete(0, tk.END)
        self.balance.insert(0, "0.00")
        self.notes.delete(1.0, tk.END)

        # إعادة تعيين التركيز
        self.account_num.focus_set()

        # رسالة تأكيد
        self.account_info_label.config(text="تم مسح جميع الحقول ✅", foreground="green")

        # إعادة الرسالة بعد 3 ثوان (مع فحص وجود النافذة)
        try:
            self.after(3000, lambda: self.reset_info_label_safely())
        except tk.TclError:
            # النافذة مدمرة بالفعل
            pass

    def reset_info_label_safely(self):
        """إعادة تعيين نص شريط الحالة بأمان"""
        try:
            if self.winfo_exists():  # فحص وجود النافذة
                self.account_info_label.config(
                    text="اختر حساباً لعرض معلوماته",
                    foreground="gray"
                )
        except (tk.TclError, AttributeError):
            # النافذة مدمرة أو العنصر غير موجود
            pass

    def safe_destroy(self):
        """إغلاق آمن للنافذة"""
        try:
            if self.winfo_exists():  # فحص وجود النافذة
                self.destroy()
        except (tk.TclError, AttributeError):
            # النافذة مدمرة بالفعل
            pass

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def add_account(self):
        """إضافة حساب جديد"""
        try:
            # التحقق من صحة البيانات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()
            balance_text = self.balance.get().strip()

            # التحقق من الحقول المطلوبة
            if not account_num:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الحساب")
                return

            if not account_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return

            # التحقق من الرصيد
            try:
                initial_balance = float(balance_text) if balance_text else 0
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيمة رقمية صحيحة للرصيد الأولي")
                return

            # الحصول على الملاحظات
            notes_text = self.notes.get(1.0, tk.END).strip()

            # محاولة إنشاء الحساب بالتنسيق الجديد
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance, "official")

            if result:
                # رسالة نجاح محسنة
                success_msg = (
                    f"✅ تم إضافة الحساب بنجاح!\n\n"
                    f"💼 رقم الحساب: {account_num}\n"
                    f"🏢 اسم الحساب: {account_name}\n"
                    f"💰 الرصيد الأولي: {initial_balance:,.2f}\n"
                )
                if notes_text:
                    success_msg += f"📝 الملاحظات: {notes_text}"

                messagebox.showinfo("✅ نجح العملية", success_msg)

                # تحديث القائمة المنسدلة (غير موجود في النافذة البسيطة)
                # self.load_existing_accounts() - غير مطلوب

                # تحديث نافذة إدارة الحسابات إذا كانت مفتوحة
                if hasattr(self.parent, 'refresh_accounts'):
                    self.parent.refresh_accounts()

                # عرض رسالة تأكيد (تم إزالة شريط الحالة من النافذة البسيطة)
                # self.account_info_label.config(...) - غير مطلوب في النافذة البسيطة

                # مسح الحقول لإضافة حساب جديد
                self.clear_fields()

                # معالجة الإغلاق حسب اختيار المستخدم
                if self.close_option.get() == "auto":
                    # إغلاق تلقائي بعد 3 ثوان (مع فحص وجود النافذة)
                    try:
                        self.after(3000, self.safe_destroy)
                    except tk.TclError:
                        # النافذة مدمرة بالفعل
                        pass
                else:
                    # البقاء مفتوحاً - مسح الحقول للحساب التالي
                    self.clear_fields()
                    # self.load_existing_accounts() - غير موجود في النافذة البسيطة
                    self.account_num.focus_set()  # التركيز على حقل رقم الحساب

            else:
                # عرض رسالة خطأ (تم إزالة شريط الحالة)
                # self.account_info_label.config(...) - غير مطلوب في النافذة البسيطة
                messagebox.showerror("خطأ", "فشل في إضافة الحساب. قد يكون رقم الحساب موجود مسبقاً.")

        except Exception as e:
            # معالجة الأخطاء غير المتوقعة
            error_msg = f"حدث خطأ أثناء إضافة الحساب: {str(e)}"
            messagebox.showerror("خطأ", error_msg)
            # self.account_info_label.config(...) - غير مطلوب في النافذة البسيطة

    def add_and_close(self):
        """إضافة الحساب وإغلاق النافذة مباشرة"""
        try:
            # التحقق من المدخلات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()

            if not account_num:
                messagebox.showerror("خطأ", "الرجاء إدخال رقم الحساب")
                self.account_num.focus_set()
                return

            if not account_name:
                messagebox.showerror("خطأ", "الرجاء إدخال اسم الحساب")
                self.account_name.focus_set()
                return

            try:
                initial_balance = float(self.balance.get() or "0")
            except ValueError:
                messagebox.showerror("خطأ", "الرجاء إدخال رقم صحيح للرصيد")
                self.balance.focus_set()
                return

            # الحصول على الملاحظات
            notes_text = self.notes.get(1.0, tk.END).strip()

            # محاولة إنشاء الحساب
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance, "official")

            if result:
                # رسالة نجاح محسنة
                success_msg = (
                    f"✅ تم إضافة الحساب بنجاح!\n\n"
                    f"💼 رقم الحساب: {account_num}\n"
                    f"🏢 اسم الحساب: {account_name}\n"
                    f"💰 الرصيد الأولي: {initial_balance:,.2f}\n"
                )
                if notes_text:
                    success_msg += f"📝 الملاحظات: {notes_text}"

                messagebox.showinfo("✅ نجح العملية", success_msg)

                # تحديث نافذة إدارة الحسابات إذا كانت مفتوحة
                if hasattr(self.parent, 'refresh_accounts'):
                    self.parent.refresh_accounts()

                # إغلاق مباشر بغض النظر عن الخيار المحدد
                self.destroy()

            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الحساب: {str(e)}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def start_auto_refresh(self):
        """بدء التحديث التلقائي للبيانات"""
        if self.auto_refresh_enabled:
            self.check_file_changes()
            # تشغيل الفحص كل 5 ثواني
            self.root.after(5000, self.start_auto_refresh)

    def check_file_changes(self):
        """فحص تغييرات الملف"""
        try:
            if os.path.exists(self.excel.current_file):
                current_modified = os.path.getmtime(self.excel.current_file)

                if self.last_file_modified is None:
                    self.last_file_modified = current_modified
                elif current_modified > self.last_file_modified:
                    print("🔄 تم اكتشاف تغييرات في الملف - تحديث البيانات...")
                    self.refresh_data()
                    self.last_file_modified = current_modified
        except Exception as e:
            print(f"⚠️ خطأ في فحص تغييرات الملف: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات تلقائياً"""
        try:
            # إعادة تحميل الملف
            if os.path.exists(self.excel.current_file):
                import openpyxl
                self.excel.workbook = openpyxl.load_workbook(self.excel.current_file)
                print("✅ تم تحديث البيانات تلقائياً")
        except Exception as e:
            print(f"❌ خطأ في تحديث البيانات: {str(e)}")


# =============================================================================
# نافذة إضافة حساب جديد (النسخة المرجعية)
# =============================================================================

class AddAccountDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إضافة حساب جديد")
        self.excel = excel
        self.geometry("400x300")

        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # حقول الإدخال مع التنقل بزر Enter
        ttk.Label(main_frame, text="💼 رقم الحساب:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.account_num = ttk.Entry(main_frame, width=30, font=("Arial", 11))
        self.account_num.grid(row=0, column=1, padx=5, pady=5)
        self.account_num.bind('<Return>', lambda e: self.account_name.focus_set())

        ttk.Label(main_frame, text="🏢 اسم الحساب:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.account_name = ttk.Entry(main_frame, width=30, font=("Arial", 11))
        self.account_name.grid(row=1, column=1, padx=5, pady=5)
        self.account_name.bind('<Return>', lambda e: self.balance.focus_set())

        ttk.Label(main_frame, text="💰 الرصيد الأولي:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.balance = ttk.Entry(main_frame, width=30, font=("Arial", 11))
        self.balance.grid(row=2, column=1, padx=5, pady=5)
        self.balance.bind('<Return>', lambda e: self.add_account())

        # تعيين التركيز الأولي
        self.account_num.focus_set()

        # أزرار محسنة
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=20)

        add_btn = ttk.Button(buttons_frame, text="✅ إضافة الحساب",
                            command=self.add_account)
        add_btn.pack(side=tk.LEFT, padx=5)

        cancel_btn = ttk.Button(buttons_frame, text="❌ إلغاء",
                               command=self.destroy)
        cancel_btn.pack(side=tk.LEFT, padx=5)

        # ربط زر Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.destroy())

        # تعيين الزر الافتراضي
        add_btn.focus_set()

    def add_account(self):
        try:
            # التحقق من صحة البيانات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()
            balance_text = self.balance.get().strip()

            # التحقق من الحقول المطلوبة
            if not account_num:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الحساب")
                return

            if not account_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return

            # التحقق من الرصيد
            try:
                initial_balance = float(balance_text) if balance_text else 0
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيمة رقمية صحيحة للرصيد الأولي")
                return

            # محاولة إنشاء الحساب بالتنسيق الجديد
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance, "official")

            if result:
                messagebox.showinfo("نجاح", f"تم إضافة الحساب '{account_name}' بنجاح")
                self.destroy()
            else:
                # الخطأ سيظهر من ExcelManager
                pass

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")


if __name__ == '__main__':
    try:
        print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية...")

        # إنشاء النافذة الرئيسية
        root = tk.Tk()

        # تعيين العنوان والحجم قبل إنشاء التطبيق
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")
        root.resizable(True, True)

        # تعيين أيقونة التطبيق (اختياري)
        try:
            root.iconbitmap(default='icon.ico')
        except:
            pass  # إذا لم توجد الأيقونة

        print("✅ تم إعداد النافذة الرئيسية")

        # إنشاء التطبيق
        print("🔧 إنشاء التطبيق...")
        app = AccountingApp(root)

        print("✅ تم إنشاء التطبيق بنجاح")
        print("🎉 النظام جاهز للاستخدام!")

        # بدء حلقة الأحداث
        root.mainloop()

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")

        try:
            import traceback
            print("🔍 تفاصيل الخطأ:")
            traceback.print_exc()

            # محاولة عرض رسالة خطأ
            try:
                messagebox.showerror("خطأ في التشغيل", f"فشل في تشغيل النظام:\n{str(e)}")
            except:
                pass

        except:
            print(f"فشل في تشغيل النظام: {str(e)}")

        finally:
            try:
                input("اضغط Enter للخروج...")
            except:
                pass
