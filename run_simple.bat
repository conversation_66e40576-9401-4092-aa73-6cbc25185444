@echo off
title Accounting System

echo ========================================
echo    Accounting System
echo    Ministry of Health - Jordan
echo ========================================
echo.

echo Starting system...
echo.

REM Set Python path
set "PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists at specific path
if exist "%PYTHON_EXE%" (
    echo Python found at: %PYTHON_EXE%
    goto :run_system
)

REM Try python command
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found in PATH
    set "PYTHON_EXE=python"
    goto :run_system
)

REM Try py command
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found via py command
    set "PYTHON_EXE=py"
    goto :run_system
)

REM Python not found
echo ERROR: Python not found
echo Please install Python from: https://www.python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation
pause
exit /b 1

:run_system
echo.
echo Checking openpyxl...
"%PYTHON_EXE%" -c "import openpyxl" 2>nul
if %errorlevel% neq 0 (
    echo Installing openpyxl...
    "%PYTHON_EXE%" -m pip install openpyxl
    if %errorlevel% neq 0 (
        echo Failed to install openpyxl
        pause
        exit /b 1
    )
)

echo.
echo Starting Accounting System...
echo.

REM Run the system
if exist "launcher.py" (
    "%PYTHON_EXE%" launcher.py
) else if exist "app.py" (
    "%PYTHON_EXE%" app.py
) else (
    echo ERROR: No launcher file found
    pause
    exit /b 1
)

echo.
echo System closed
pause
