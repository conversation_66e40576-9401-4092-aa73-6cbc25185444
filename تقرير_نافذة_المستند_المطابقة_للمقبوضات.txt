================================================================
تقرير نافذة المستند المطابقة للمقبوضات
Document Window Matching Receipts Window Report
================================================================

تاريخ التحديث: 2025-01-XX
الإصدار: 5.0.0 (النافذة المطابقة)
المطور: فريق تطوير الأنظمة - وزارة الصحة الأردنية

================================================================
🎯 الهدف من التحديث
================================================================

إعادة تصميم نافذة "إضافة مستند" لتكون مطابقة تماماً لنافذة "إضافة مستند مقبوضات" 
مع نظام التوزيع والميزات التالية:

✅ نظام توزيع المبالغ على عدة حسابات
✅ تحديث تلقائي لإجمالي المبلغ
✅ تحديث تلقائي لقائمة الحسابات
✅ التنقل بزر الإدخال بين الحقول
✅ الاحتفاظ بالحساب المختار للمستند التالي
✅ إنشاء جداول جديدة تلقائياً عند الامتلاء

================================================================
🔧 التصميم الجديد المطابق
================================================================

### 1. هيكل النافذة الجديد:
---------------------------

```
📄 إضافة مستند جديد

📋 معلومات المستند
├── رقم المستند: [Entry]
├── رقم التأدية: [Entry]
└── إجمالي المبلغ: [Entry - للعرض فقط]

📊 توزيع المبالغ على الحسابات
┌─────────────────────────────────────────┐
│ اسم الحساب    │ المبلغ (0.000) │ حذف │
├─────────────────────────────────────────┤
│ [Combobox]     │ [Entry]         │ 🗑️  │
│ [Combobox]     │ [Entry]         │ 🗑️  │
│ ...            │ ...             │ ... │
└─────────────────────────────────────────┘
[➕ إضافة حساب جديد]

الأزرار:
[💾 حفظ المستند] [🧮 حساب الإجمالي] [🔄 تحديث الحسابات] 
[🗑️ مسح الكل] [❓ مساعدة] [❌ إغلاق]

الحالة: جاهز لإضافة مستند جديد
```

### 2. المتغيرات الجديدة:
-------------------------

```python
# متغيرات النموذج الرئيسية
self.document_num_var = tk.StringVar()      # رقم المستند
self.payment_num_var = tk.StringVar()       # رقم التأدية
self.total_amount_var = tk.StringVar()      # إجمالي المبلغ

# متغيرات النظام
self.available_accounts = []               # قائمة الحسابات
self.distribution_rows = []                # صفوف التوزيع
self.last_selected_account = ""            # الحساب المختار سابقاً
```

### 3. نظام التوزيع:
-------------------

#### كل صف في جدول التوزيع يحتوي على:
```python
row_data = {
    'frame': row_frame,                    # إطار الصف
    'account_var': account_var,            # متغير الحساب
    'amount_var': amount_var,              # متغير المبلغ
    'account_combo': account_combo,        # قائمة الحسابات
    'amount_entry': amount_entry,          # حقل المبلغ
    'delete_btn': delete_btn               # زر الحذف
}
```

================================================================
⚡ الميزات الجديدة المطابقة
================================================================

### 1. نظام التوزيع المتقدم:
---------------------------
✅ **إضافة عدة حسابات** للمستند الواحد
✅ **توزيع المبالغ** على حسابات مختلفة
✅ **حساب تلقائي للإجمالي** عند تغيير أي مبلغ
✅ **إضافة وحذف الصفوف** ديناميكياً
✅ **شريط تمرير** للجدول عند وجود صفوف كثيرة

### 2. التحديث التلقائي:
-----------------------
✅ **تحديث قائمة الحسابات** كل 10 ثواني
✅ **تحديث صامت** بدون رسائل مزعجة
✅ **الحفاظ على الاختيارات** أثناء التحديث
✅ **إضافة الحسابات الجديدة** تلقائياً

### 3. التنقل بزر الإدخال:
--------------------------
✅ **رقم المستند → رقم التأدية**
✅ **رقم التأدية → أول حقل مبلغ**
✅ **حقل مبلغ → حقل مبلغ التالي**
✅ **آخر حقل مبلغ → إضافة صف جديد**

### 4. الاحتفاظ بالبيانات:
--------------------------
✅ **حفظ الحساب المختار** للمستند التالي
✅ **مسح الحقول الأخرى** فقط
✅ **التركيز على رقم المستند** للإدخال السريع
✅ **الاحتفاظ بصفوف التوزيع** مع مسح المبالغ

### 5. إنشاء الجداول التلقائي:
-----------------------------
✅ **استخدام آلية النظام الموجودة** في excel_manager.py
✅ **إنشاء جدول جديد** عند امتلاء الجدول الحالي
✅ **ترحيل الرصيد** تلقائياً للجدول الجديد
✅ **معالجة الأخطاء** والتحقق من الصلاحيات

================================================================
🔄 آلية العمل المطابقة
================================================================

### عند فتح النافذة:
-------------------
1. تحميل قائمة الحسابات المحدثة
2. إضافة صف أولي للتوزيع
3. إعداد التحديث التلقائي (كل 10 ثواني)
4. توسيط النافذة
5. التركيز على رقم المستند

### عند إدخال البيانات:
-----------------------
1. **رقم المستند** → Enter → **رقم التأدية**
2. **رقم التأدية** → Enter → **أول حقل مبلغ**
3. **اختيار الحساب** من القائمة المنسدلة
4. **إدخال المبلغ** → تحديث الإجمالي تلقائياً
5. **Enter** → الانتقال للصف التالي أو إضافة صف جديد

### عند حفظ المستند:
--------------------
1. **التحقق من صحة البيانات**:
   - رقم المستند مطلوب
   - رقم التأدية مطلوب
   - حساب واحد على الأقل مع مبلغ صحيح

2. **حفظ المستندات**:
   - لكل حساب في جدول التوزيع
   - استخدام آلية add_document الموجودة
   - إنشاء جداول جديدة عند الحاجة

3. **بعد الحفظ الناجح**:
   - عرض رسالة نجاح مفصلة
   - حفظ الحساب المختار
   - مسح البيانات للمستند التالي
   - التركيز على رقم المستند

================================================================
🧮 آلية حساب الإجمالي
================================================================

### التحديث التلقائي:
--------------------
```python
def calculate_total(self):
    """حساب إجمالي المبالغ"""
    total = 0.0
    for row_data in self.distribution_rows:
        try:
            amount = float(row_data['amount_var'].get() or 0)
            total += amount
        except ValueError:
            pass
    
    self.total_amount_var.set(f"{total:.3f}")
```

### متى يتم التحديث:
------------------
✅ عند تغيير أي مبلغ في جدول التوزيع
✅ عند إضافة صف جديد
✅ عند حذف صف
✅ عند الضغط على زر "حساب الإجمالي"

================================================================
💾 آلية الحفظ المطابقة
================================================================

### التحقق من البيانات:
-----------------------
```python
def validate_inputs(self):
    # فحص رقم المستند
    if not self.document_num_var.get().strip():
        return False
    
    # فحص رقم التأدية
    if not self.payment_num_var.get().strip():
        return False
    
    # فحص وجود حساب واحد على الأقل مع مبلغ
    valid_entries = 0
    for row_data in self.distribution_rows:
        account = row_data['account_var'].get()
        amount_str = row_data['amount_var'].get()
        
        if account and amount_str:
            try:
                amount = float(amount_str)
                if amount > 0:
                    valid_entries += 1
            except ValueError:
                return False
    
    return valid_entries > 0
```

### عملية الحفظ:
----------------
```python
def save_document(self):
    # التحقق من صحة البيانات
    if not self.validate_inputs():
        return
    
    # حفظ المستندات لكل حساب
    for row_data in self.distribution_rows:
        account = row_data['account_var'].get()
        amount_str = row_data['amount_var'].get()
        
        if account and amount_str:
            amount = float(amount_str)
            if amount > 0:
                # استخدام آلية النظام الموجودة
                success = self.parent.excel.add_document(
                    account,
                    amount,
                    self.document_num_var.get(),
                    self.payment_num_var.get()
                )
    
    # مسح البيانات للمستند التالي
    self.clear_for_next_document()
```

================================================================
🔄 آلية التحديث التلقائي
================================================================

### إعداد التحديث:
-----------------
```python
def setup_auto_refresh(self):
    def refresh_accounts():
        old_accounts = self.available_accounts.copy()
        self.load_accounts_list_silent()
        
        # تحديث قوائم الحسابات في جميع الصفوف
        if old_accounts != self.available_accounts:
            self.update_all_account_combos()
            print(f"🔄 تم تحديث تلقائي: {len(self.available_accounts)} حساب")
        
        # جدولة التحديث التالي
        self.after(10000, refresh_accounts)  # 10 ثواني
    
    # بدء التحديث التلقائي
    self.after(10000, refresh_accounts)
```

### تحديث القوائم:
-----------------
```python
def update_all_account_combos(self):
    """تحديث قوائم الحسابات في جميع الصفوف"""
    for row_data in self.distribution_rows:
        current_selection = row_data['account_var'].get()
        row_data['account_combo']['values'] = self.available_accounts
        
        # إعادة تعيين الاختيار إن وجد
        if current_selection and current_selection in self.available_accounts:
            row_data['account_var'].set(current_selection)
```

================================================================
🎯 مقارنة مع نافذة المقبوضات
================================================================

### أوجه التشابه (100% مطابقة):
------------------------------
✅ **نفس التخطيط** والتصميم
✅ **نفس نظام التوزيع** على عدة حسابات
✅ **نفس آلية التحديث التلقائي** للحسابات
✅ **نفس التنقل بزر الإدخال**
✅ **نفس الأزرار والوظائف**
✅ **نفس رسائل الحالة**

### الاختلافات الوظيفية:
------------------------
🔄 **نافذة المقبوضات**: تحفظ في ملف "Accounting system deductions.xlsx"
🔄 **نافذة المستندات**: تحفظ في ملف "Accounting system.xlsx"

🔄 **نافذة المقبوضات**: تستخدم آلية حفظ المقبوضات
🔄 **نافذة المستندات**: تستخدم آلية add_document مع إنشاء الجداول التلقائي

================================================================
🧪 اختبار الميزات الجديدة
================================================================

### اختبار نظام التوزيع:
------------------------
1. افتح نافذة إضافة مستند
2. أدخل رقم المستند ورقم التأدية
3. اختر حساب وأدخل مبلغ
4. اضغط "إضافة حساب جديد"
5. اختر حساب آخر وأدخل مبلغ آخر
6. تأكد من تحديث الإجمالي تلقائياً

### اختبار التنقل بالإدخال:
---------------------------
1. اضغط Enter في رقم المستند → ينتقل لرقم التأدية
2. اضغط Enter في رقم التأدية → ينتقل لأول حقل مبلغ
3. اضغط Enter في حقل مبلغ → ينتقل للحقل التالي
4. اضغط Enter في آخر حقل → يضيف صف جديد

### اختبار التحديث التلقائي:
---------------------------
1. افتح النافذة
2. أضف حساب جديد من نافذة أخرى
3. انتظر 10 ثواني
4. تأكد من ظهور الحساب الجديد في القوائم

### اختبار الحفظ والاحتفاظ:
---------------------------
1. أدخل مستند واحفظه
2. تأكد من مسح البيانات
3. تأكد من الاحتفاظ بالحساب المختار
4. تأكد من التركيز على رقم المستند

### اختبار إنشاء الجداول:
-------------------------
1. اختر حساب ممتلئ الجداول
2. أدخل مستند جديد
3. احفظ المستند
4. تأكد من إنشاء جدول جديد تلقائياً

================================================================
⚠️ ملاحظات مهمة
================================================================

### للمطورين:
--------------
✅ **التوافق الكامل** مع النظام الموجود
✅ **استخدام آليات النظام** الموجودة في excel_manager.py
✅ **معالجة أخطاء شاملة** ومتقدمة
✅ **كود منظم وموثق** بالكامل

### للمستخدمين:
----------------
✅ **واجهة مألوفة** مطابقة لنافذة المقبوضات
✅ **سهولة في الاستخدام** والتنقل
✅ **توفير الوقت** مع التحديث التلقائي
✅ **مرونة في التوزيع** على عدة حسابات

### للصيانة:
------------
✅ **تحديث تلقائي** يقلل الأخطاء
✅ **إنشاء جداول تلقائي** يمنع فقدان البيانات
✅ **رسائل حالة واضحة** تساعد في التشخيص
✅ **احتفاظ بالبيانات** يسرع الإدخال

================================================================
🎉 خلاصة التحديث
================================================================

تم بنجاح إعادة تصميم نافذة "إضافة مستند" لتصبح:

✅ **مطابقة 100%** لنافذة المقبوضات في التصميم والوظائف
✅ **متقدمة في الميزات** مع نظام التوزيع المتطور
✅ **ذكية في التحديث** مع التحديث التلقائي للحسابات
✅ **سريعة في الاستخدام** مع التنقل بزر الإدخال
✅ **موثوقة في الحفظ** مع إنشاء الجداول التلقائي

### النتيجة النهائية:
--------------------
🎉 نافذة إضافة مستند احترافية ومطابقة تماماً للمقبوضات
🎉 نظام توزيع متقدم على عدة حسابات
🎉 تحديث تلقائي وذكي للبيانات
🎉 تجربة مستخدم سلسة ومألوفة
🎉 استقرار وموثوقية عالية مع إنشاء الجداول التلقائي

================================================================

تم إعداد هذا التقرير بواسطة:
فريق تطوير الأنظمة - وزارة الصحة الأردنية

آخر تحديث: 2025-01-XX
================================================================
