@echo off
echo ========================================
echo تشغيل تطبيق Flutter المحاسبي
echo ========================================
echo.

echo 📱 فحص Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter غير مثبت! يرجى تثبيت Flutter أولاً
    echo 🔗 تحميل من: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo ✅ Flutter مثبت بنجاح
echo.

echo 📦 تحميل المكتبات...
flutter pub get

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم تحميل المكتبات بنجاح!
    echo.
    echo 🚀 بدء تشغيل التطبيق...
    echo 📱 سيتم فتح التطبيق في المتصفح أو المحاكي
    echo.
    flutter run -d web-server --web-port 8080
) else (
    echo.
    echo ❌ حدث خطأ في تحميل المكتبات!
    echo 💡 جرب الأوامر التالية يدوياً:
    echo    flutter clean
    echo    flutter pub get
    echo    flutter run -d web-server
)

echo.
echo ========================================
pause
