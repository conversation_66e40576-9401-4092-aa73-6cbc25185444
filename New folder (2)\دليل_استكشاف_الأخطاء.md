# دليل استكشاف الأخطاء - نظام إدارة المستندات المحاسبية

## 🚨 مشكلة: لا يتم إضافة الحساب

### الأعراض:
- النقر على "إضافة حساب" لا يؤدي إلى إنشاء حساب جديد
- لا تظهر رسالة نجاح أو خطأ
- الحساب غير موجود في ملف Excel

### الحلول المحتملة:

#### 1. فحص البيانات المدخلة
```
✅ تأكد من ملء جميع الحقول المطلوبة:
   - رقم الحساب (مطلوب)
   - اسم الحساب (مطلوب)
   - الرصيد الافتتاحي (اختياري)

❌ تجنب:
   - ترك الحقول فارغة
   - استخدام أرقام أو أسماء مكررة
   - أسماء طويلة جداً (أكثر من 25 حرف)
```

#### 2. فحص صلاحيات الملف
```
🔧 تأكد من:
   - وجود صلاحيات الكتابة في مجلد النظام
   - عدم فتح ملف Excel في برنامج آخر
   - عدم حماية ملف Excel بكلمة مرور
```

#### 3. فحص المكتبات
```
📦 تشغيل الأوامر التالية:
   python -c "import openpyxl; print('openpyxl متوفر')"
   python -c "import tkinter; print('tkinter متوفر')"

إذا ظهر خطأ، قم بتثبيت المكتبة:
   pip install openpyxl
```

#### 4. استخدام أداة التشخيص
```
🔍 تشغيل أداة التشخيص:
   python تشخيص_المشاكل.py

أو اختبار إضافة الحساب:
   python test_add_account.py
```

## 🚨 مشكلة: خطأ "الحساب موجود مسبقاً"

### الحل:
```
1. تحقق من أسماء الحسابات الموجودة
2. استخدم رقم أو اسم مختلف
3. أو احذف الحساب المكرر من ملف Excel
```

## 🚨 مشكلة: خطأ في حفظ الملف

### الأعراض:
- رسالة "خطأ في حفظ الملف"
- تم إنشاء الحساب لكن لم يتم حفظه

### الحلول:
```
1. تأكد من إغلاق ملف Excel في أي برنامج آخر
2. تحقق من صلاحيات الكتابة في المجلد
3. تأكد من وجود مساحة كافية على القرص الصلب
4. أعد تشغيل النظام كمدير (Run as Administrator)
```

## 🚨 مشكلة: النظام لا يبدأ

### الأعراض:
- خطأ "Python غير معروف"
- خطأ في استيراد المكتبات
- النافذة لا تظهر

### الحلول:

#### 1. تثبيت Python
```
📥 تحميل Python من:
   https://www.python.org/downloads/

⚠️ مهم: تحديد "Add Python to PATH" أثناء التثبيت
```

#### 2. تثبيت المكتبات
```
💻 فتح Command Prompt وتشغيل:
   pip install openpyxl ttkthemes pillow

أو استخدام ملف المتطلبات:
   pip install -r requirements.txt
```

#### 3. استخدام النسخة المستقلة
```
📁 استخدم النسخة في مجلد dist_standalone:
   dist_standalone/تشغيل_النظام.bat
```

## 🚨 مشكلة: النص العربي لا يظهر بشكل صحيح

### الحلول:
```
1. تأكد من أن النظام يدعم UTF-8
2. تغيير إعدادات اللغة في Windows إلى العربية
3. تثبيت خطوط عربية إضافية
4. استخدام Command Prompt مع دعم Unicode
```

## 🚨 مشكلة: بطء في الأداء

### الحلول:
```
1. إغلاق البرامج غير الضرورية
2. تأكد من وجود ذاكرة كافية (RAM)
3. تنظيف ملف Excel من البيانات غير المستخدمة
4. إعادة تشغيل النظام
```

## 🛠️ أدوات التشخيص المتاحة

### 1. أداة التشخيص الشاملة
```bash
python تشخيص_المشاكل.py
```
**الوظيفة**: فحص شامل لجميع مكونات النظام

### 2. اختبار إضافة الحساب
```bash
python test_add_account.py
```
**الوظيفة**: اختبار محدد لوظيفة إضافة الحسابات

### 3. اختبار النظام العام
```bash
python test_system.py
```
**الوظيفة**: اختبار عام لجميع وظائف النظام

## 📞 الحصول على المساعدة

### خطوات الحصول على الدعم:
```
1. تشغيل أداة التشخيص وحفظ النتائج
2. تجميع معلومات النظام:
   - نسخة Windows
   - نسخة Python
   - رسائل الخطأ الكاملة
3. التواصل مع فريق الدعم الفني
```

### معلومات مفيدة للدعم:
```
- نسخة النظام: 1.0
- تاريخ آخر تحديث: 2024/12/19
- الملفات المطلوبة: app.py, excel_manager.py, وملفات أخرى
- المكتبات المطلوبة: openpyxl, tkinter, ttkthemes
```

## ✅ نصائح للوقاية من المشاكل

### 1. النسخ الاحتياطي
```
📁 احتفظ بنسخة احتياطية من:
   - ملف accounting_system.xlsx
   - جميع ملفات النظام
   - إعدادات النظام
```

### 2. التحديث المنتظم
```
🔄 تأكد من:
   - تحديث Python بانتظام
   - تحديث المكتبات المطلوبة
   - تحديث نسخة النظام
```

### 3. الاستخدام الآمن
```
⚠️ تجنب:
   - تعديل ملفات النظام يدوياً
   - فتح ملف Excel أثناء تشغيل النظام
   - استخدام أسماء حسابات معقدة أو طويلة
```

---

**للمساعدة الإضافية**: راجع ملف README.md أو تواصل مع فريق الدعم الفني
