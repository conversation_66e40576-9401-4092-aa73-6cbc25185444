# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['launcher.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        # ملفات Python الأساسية
        ('app.py', '.'),
        ('launcher.py', '.'),
        ('excel_manager.py', '.'),
        ('multi_excel_manager.py', '.'),
        ('document_window.py', '.'),
        ('search_window.py', '.'),
        ('manage_accounts.py', '.'),
        ('user_manager.py', '.'),
        ('receipts_account_window.py', '.'),
        ('receipts_document_window.py', '.'),
        ('manage_receipts_accounts.py', '.'),
        ('receipts_search_window.py', '.'),
        ('account_balances_window.py', '.'),

        # ملفات البيانات والإعدادات
        ('requirements.txt', '.'),
        ('setup.py', '.'),
        ('install_dependencies.py', '.'),
        ('users.json', '.'),
        ('*.xlsx', '.'),
        ('*.json', '.'),

        # ملفات التوثيق
        ('*.md', '.'),
        ('*.txt', '.'),

        # ملفات الموارد (إذا كانت موجودة)
        ('*.ico', '.'),
        ('*.png', '.'),
        ('*.jpg', '.'),
        ('*.gif', '.'),

        # ملفات التشغيل
        ('*.bat', '.'),
        ('*.spec', '.'),
    ],
    hiddenimports=[
        # مكتبات openpyxl الشاملة
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.workbook.workbook',
        'openpyxl.worksheet',
        'openpyxl.worksheet.worksheet',
        'openpyxl.worksheet.table',
        'openpyxl.worksheet.cell_range',
        'openpyxl.styles',
        'openpyxl.styles.fonts',
        'openpyxl.styles.borders',
        'openpyxl.styles.fills',
        'openpyxl.styles.alignment',
        'openpyxl.styles.colors',
        'openpyxl.styles.numbers',
        'openpyxl.styles.protection',
        'openpyxl.styles.named_styles',
        'openpyxl.utils',
        'openpyxl.utils.cell',
        'openpyxl.utils.dataframe',
        'openpyxl.utils.exceptions',
        'openpyxl.utils.indexed_list',
        'openpyxl.reader',
        'openpyxl.reader.excel',
        'openpyxl.reader.workbook',
        'openpyxl.writer',
        'openpyxl.writer.excel',
        'openpyxl.writer.workbook',
        'openpyxl.formatting',
        'openpyxl.formatting.rule',
        'openpyxl.chart',
        'openpyxl.drawing',

        # مكتبات tkinter الشاملة
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'tkinter.scrolledtext',
        'tkinter.simpledialog',
        'tkinter.colorchooser',
        'tkinter.commondialog',
        'tkinter.constants',
        'tkinter.dnd',
        'tkinter.tix',

        # مكتبات النظام الأساسية
        'datetime',
        'os',
        'os.path',
        'sys',
        'threading',
        'locale',
        'tempfile',
        'subprocess',
        'webbrowser',
        'json',
        'traceback',
        'logging',
        'pathlib',
        'shutil',
        'glob',
        'time',
        'calendar',
        'math',
        'decimal',
        'fractions',
        'statistics',
        'collections',
        'itertools',
        'functools',
        'operator',
        'copy',
        'pickle',
        'base64',
        'hashlib',
        'hmac',
        'uuid',
        'random',
        're',
        'string',
        'io',
        'gzip',
        'zipfile',
        'tarfile',

        # مكتبات واجهة المستخدم المحسنة
        'ttkthemes',
        'ttkthemes.themed_tk',
        'ttkthemes.themed_style',
        'ttkthemes.themed_ttk',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageFont',
        'PIL.ImageDraw',
        'PIL.ImageFilter',
        'PIL.ImageEnhance',
        'PIL.ImageOps',

        # مكتبات الأمان والتشفير
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.backends',
        'cryptography.x509',

        # مكتبات معالجة التواريخ والوقت
        'dateutil',
        'dateutil.parser',
        'dateutil.tz',
        'dateutil.relativedelta',

        # مكتبات التحقق والتحقق
        'jsonschema',
        'jsonschema.validators',
        'jsonschema.exceptions',

        # مكتبات دعم اللغة العربية
        'arabic_reshaper',
        'bidi',
        'bidi.algorithm',

        # مكتبات الشبكة والاتصالات
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.exceptions',
        'requests.models',
        'requests.sessions',
        'urllib3',
        'urllib3.util',
        'urllib3.exceptions',
        'urllib3.poolmanager',
        'certifi',
        'charset_normalizer',
        'idna',

        # مكتبات ضغط الملفات
        'zstandard',

        # مكتبات مراقبة النظام
        'psutil',
        'psutil._common',
        'psutil._psutil_windows',

        # مكتبات إدارة الحزم
        'packaging',
        'packaging.version',
        'packaging.requirements',
        'six',
        'six.moves',

        # مكتبات التطبيق المحلية
        'excel_manager',
        'multi_excel_manager',
        'document_window',
        'search_window',
        'manage_accounts',
        'user_manager',
        'receipts_account_window',
        'receipts_document_window',
        'manage_receipts_accounts',
        'receipts_search_window',
        'account_balances_window',
        'launcher',
        'silent_runner',
        'launcher_silent_simple',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_المستندات_المحاسبية',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_info={
        'version': '1.1.0',
        'description': 'نظام إدارة المستندات المحاسبية مع وظيفة الطباعة - وزارة الصحة الأردنية',
        'company': 'وزارة الصحة - المملكة الأردنية الهاشمية',
        'product': 'نظام إدارة المستندات المحاسبية',
        'copyright': '2025 وزارة الصحة الأردنية',
    }
)
