#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل آمن لنظام إدارة المستندات المحاسبية
وزارة الصحة - التأمين الصحي
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def create_simple_login():
    """إنشاء نافذة تسجيل دخول مبسطة"""
    
    def authenticate_user():
        username = username_entry.get().strip()
        password = password_entry.get()
        
        if username == "admin" and password == "admin":
            login_root.quit()
            login_root.destroy()
            return True
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            password_entry.delete(0, tk.END)
            return False
    
    def on_cancel():
        login_root.quit()
        login_root.destroy()
        sys.exit()
    
    # إنشاء نافذة تسجيل الدخول
    login_root = tk.Tk()
    login_root.title("تسجيل الدخول - نظام إدارة المستندات المحاسبية")
    login_root.geometry("400x300")
    login_root.resizable(False, False)
    
    # توسيط النافذة
    x = (login_root.winfo_screenwidth() // 2) - (400 // 2)
    y = (login_root.winfo_screenheight() // 2) - (300 // 2)
    login_root.geometry(f"400x300+{x}+{y}")
    
    # إطار رئيسي
    main_frame = tk.Frame(login_root, bg='#f0f8ff')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # العنوان
    title_label = tk.Label(main_frame, text="🏥 نظام إدارة المستندات المحاسبية",
                          font=("Arial", 14, "bold"), bg='#f0f8ff', fg='#2c3e50')
    title_label.pack(pady=(0, 10))
    
    subtitle_label = tk.Label(main_frame, text="وزارة الصحة - التأمين الصحي",
                             font=("Arial", 10), bg='#f0f8ff', fg='#7f8c8d')
    subtitle_label.pack(pady=(0, 30))
    
    # حقول الإدخال
    tk.Label(main_frame, text="اسم المستخدم:", font=("Arial", 11), 
             bg='#f0f8ff', fg='#34495e').pack(anchor=tk.W, pady=(0, 5))
    
    username_entry = tk.Entry(main_frame, font=("Arial", 12), width=25)
    username_entry.pack(pady=(0, 15))
    
    tk.Label(main_frame, text="كلمة المرور:", font=("Arial", 11), 
             bg='#f0f8ff', fg='#34495e').pack(anchor=tk.W, pady=(0, 5))
    
    password_entry = tk.Entry(main_frame, font=("Arial", 12), width=25, show="*")
    password_entry.pack(pady=(0, 25))
    
    # أزرار
    buttons_frame = tk.Frame(main_frame, bg='#f0f8ff')
    buttons_frame.pack(pady=10)
    
    login_btn = tk.Button(buttons_frame, text="🔑 دخول", command=authenticate_user,
                         font=("Arial", 11, "bold"), bg='#27ae60', fg='white',
                         padx=20, pady=5, cursor='hand2')
    login_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=on_cancel,
                          font=("Arial", 11, "bold"), bg='#e74c3c', fg='white',
                          padx=20, pady=5, cursor='hand2')
    cancel_btn.pack(side=tk.LEFT)
    
    # ربط Enter
    login_root.bind('<Return>', lambda e: authenticate_user())
    
    # التركيز على حقل اسم المستخدم
    username_entry.focus()
    
    # تشغيل النافذة
    login_root.mainloop()
    
    return True

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية")
        print("🏥 وزارة الصحة - التأمين الصحي")
        print("=" * 60)
        
        # التأكد من وجود الملفات المطلوبة
        required_files = ['app.py', 'user_manager.py', 'excel_manager.py']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            input("اضغط Enter للخروج...")
            return
        
        print("✅ جميع الملفات المطلوبة موجودة")
        print("🔐 عرض نافذة تسجيل الدخول...")
        
        # عرض نافذة تسجيل الدخول المبسطة
        if not create_simple_login():
            return
        
        print("✅ تم تسجيل الدخول بنجاح")
        print("🔄 تحميل النظام الرئيسي...")
        
        # استيراد وتشغيل النظام الرئيسي
        from user_manager import UserManager
        from excel_manager import ExcelManager
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")
        
        # إنشاء مدير المستخدمين وتسجيل الدخول تلقائياً
        user_manager = UserManager()
        user_manager.authenticate("admin", "admin")
        
        # إنشاء مدير Excel
        excel_manager = ExcelManager()
        
        # إعداد الواجهة الرئيسية مباشرة
        from app import AccountingApp
        
        # إنشاء التطبيق مع تجاوز نافذة تسجيل الدخول
        app = AccountingApp.__new__(AccountingApp)
        app.root = root
        app.user_manager = user_manager
        app.excel = excel_manager
        
        # إعداد الواجهة مباشرة
        app.setup_modern_ui()
        
        # تحديث عنوان النافذة
        user_info = user_manager.get_user_info()
        if user_info:
            root.title(f"نظام إدارة المستندات المحاسبية - {user_info['full_name']} ({user_info['role']})")
        
        # إعداد إغلاق النافذة
        root.protocol("WM_DELETE_WINDOW", app.on_closing)
        
        print("✅ تم تحميل النظام بنجاح")
        print("🎉 النظام جاهز للاستخدام")
        print("=" * 60)
        
        # تشغيل النافذة الرئيسية
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {str(e)}")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("   pip install openpyxl")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
