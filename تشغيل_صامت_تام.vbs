' Silent launcher for Accounting System
' Based on the working launcher logic

Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get application directory
strAppPath = objFSO.GetParentFolderName(WScript.ScriptFullName)
objShell.CurrentDirectory = strAppPath

' Find Python command
Dim pythonCmd
pythonCmd = ""

' Try py command first
On Error Resume Next
Dim result
result = objShell.Run("py -c ""print('test')""", 0, True)
If Err.Number = 0 And result = 0 Then
    pythonCmd = "py"
Else
    ' Try python command
    result = objShell.Run("python -c ""print('test')""", 0, True)
    If Err.Number = 0 And result = 0 Then
        pythonCmd = "python"
    Else
        ' Try specific path
        If objFSO.FileExists("C:\Users\<USER>\Downloads\python-3.13.2-embed-amd64\python.exe") Then
            pythonCmd = "C:\Users\<USER>\Downloads\python-3.13.2-embed-amd64\python.exe"
        End If
    End If
End If
On Error GoTo 0

' If Python found, run the application
If pythonCmd <> "" Then
    ' Install openpyxl if needed (silently)
    objShell.Run pythonCmd & " -m pip install openpyxl", 0, True

    ' Run the application completely hidden
    If objFSO.FileExists("launcher.py") Then
        objShell.Run pythonCmd & " launcher.py", 0, False
    ElseIf objFSO.FileExists("app.py") Then
        objShell.Run pythonCmd & " app.py", 0, False
    End If
End If

' Exit silently
WScript.Quit
