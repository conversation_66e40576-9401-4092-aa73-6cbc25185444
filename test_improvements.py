#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات الجديدة:
1. إغلاق نافذة إضافة المستند تلقائياً
2. إزالة رسالة النجاح
3. تحديث التقرير الإجمالي تلقائياً
4. إصلاح ترحيل الرصيد
"""

import os
import sys

def test_document_addition():
    """اختبار إضافة المستند والإغلاق التلقائي"""
    try:
        print("🧪 اختبار إضافة المستند...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "TEST001"
        account_name = "حساب اختبار"
        balance = 5000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        print(f"✅ تم إنشاء الحساب: {sheet_name}")
        
        # إضافة مستند
        amount = 1500
        doc_num = "DOC001"
        pay_num = "PAY001"
        
        result = excel.add_document(sheet_name, amount, doc_num, pay_num)
        if result:
            print(f"✅ تم إضافة المستند بنجاح")
            
            # التحقق من وجود التقرير الإجمالي
            if 'التقرير الإجمالي' in excel.workbook.sheetnames:
                print(f"✅ تم إنشاء التقرير الإجمالي تلقائياً")
                return True
            else:
                print(f"❌ لم يتم إنشاء التقرير الإجمالي")
                return False
        else:
            print(f"❌ فشل في إضافة المستند")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_balance_transfer():
    """اختبار ترحيل الرصيد بين الأقسام"""
    try:
        print("\n🧪 اختبار ترحيل الرصيد...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "TRANSFER001"
        account_name = "حساب ترحيل"
        balance = 10000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        ws = excel.workbook[sheet_name]
        
        # التحقق من الرصيد الافتتاحي في القسم الأول
        opening_balance = ws['A9'].value
        print(f"📊 الرصيد الافتتاحي في القسم الأول: {opening_balance}")
        
        # التحقق من صيغة ترحيل الرصيد في القسم الثاني
        transfer_formula = ws['D9'].value
        print(f"📊 صيغة ترحيل الرصيد في القسم الثاني: {transfer_formula}")
        
        # التحقق من صيغة ترحيل الرصيد في القسم الثالث
        transfer_formula_3 = ws['G9'].value
        print(f"📊 صيغة ترحيل الرصيد في القسم الثالث: {transfer_formula_3}")
        
        # إضافة مستندات للقسم الأول
        documents = [
            (2000, "DOC001", "PAY001"),
            (1500, "DOC002", "PAY002"),
            (3000, "DOC003", "PAY003")
        ]
        
        for amount, doc_num, pay_num in documents:
            result = excel.add_document(sheet_name, amount, doc_num, pay_num)
            if result:
                print(f"✅ تم إضافة مستند {doc_num}")
            else:
                print(f"❌ فشل في إضافة مستند {doc_num}")
        
        # حفظ وإعادة تحميل لحساب الصيغ
        excel.save_workbook()
        
        # التحقق من المجموع في القسم الأول
        section1_total = ws['A33'].value
        print(f"📊 مجموع القسم الأول: {section1_total}")
        
        # التحقق من أن القسم الثاني يحتوي على صيغة صحيحة
        if transfer_formula == "=A33":
            print(f"✅ صيغة ترحيل الرصيد صحيحة")
            return True
        else:
            print(f"❌ صيغة ترحيل الرصيد خاطئة: {transfer_formula}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار ترحيل الرصيد: {str(e)}")
        return False

def test_summary_report():
    """اختبار التقرير الإجمالي"""
    try:
        print("\n🧪 اختبار التقرير الإجمالي...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء عدة حسابات تجريبية
        test_accounts = [
            ("RPT001", "حساب تقرير 1", 5000),
            ("RPT002", "حساب تقرير 2", 3000),
            ("RPT003", "حساب تقرير 3", 0)  # حساب بدون رصيد
        ]
        
        created_accounts = []
        for account_num, account_name, balance in test_accounts:
            result = excel.create_account_sheet(account_num, account_name, balance)
            if result:
                sheet_name = f"{account_num}-{account_name}"
                created_accounts.append(sheet_name)
                print(f"✅ تم إنشاء الحساب: {sheet_name}")
        
        # إضافة مستندات لبعض الحسابات
        if len(created_accounts) >= 2:
            # إضافة مستندات للحساب الأول
            excel.add_document(created_accounts[0], 1000, "DOC001", "PAY001")
            excel.add_document(created_accounts[0], 2000, "DOC002", "PAY002")
            
            # إضافة مستندات للحساب الثاني
            excel.add_document(created_accounts[1], 500, "DOC003", "PAY003")
        
        # إنشاء التقرير الإجمالي
        result = excel.create_summary_report()
        if result:
            print(f"✅ تم إنشاء التقرير الإجمالي")
            
            # التحقق من وجود التقرير
            if 'التقرير الإجمالي' in excel.workbook.sheetnames:
                ws_report = excel.workbook['التقرير الإجمالي']
                
                # التحقق من العناوين
                headers = []
                for col in range(1, 7):
                    header = ws_report.cell(row=6, column=col).value
                    headers.append(header)
                
                print(f"📊 عناوين التقرير: {headers}")
                
                # التحقق من وجود بيانات الحسابات
                accounts_in_report = 0
                for row in range(7, 20):  # فحص أول 13 صف
                    account_name = ws_report.cell(row=row, column=2).value
                    if account_name:
                        accounts_in_report += 1
                        print(f"   - {account_name}")
                
                print(f"📊 عدد الحسابات في التقرير: {accounts_in_report}")
                
                if accounts_in_report >= len(created_accounts):
                    print(f"✅ التقرير يحتوي على جميع الحسابات")
                    return True
                else:
                    print(f"❌ التقرير لا يحتوي على جميع الحسابات")
                    return False
            else:
                print(f"❌ التقرير الإجمالي غير موجود")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير الإجمالي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التقرير الإجمالي: {str(e)}")
        return False

def test_account_deletion_update():
    """اختبار تحديث التقرير عند حذف الحساب"""
    try:
        print("\n🧪 اختبار تحديث التقرير عند حذف الحساب...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب للحذف
        account_num = "DEL001"
        account_name = "حساب للحذف"
        balance = 2000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب للحذف")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        print(f"✅ تم إنشاء الحساب للحذف: {sheet_name}")
        
        # إنشاء التقرير الإجمالي
        excel.create_summary_report()
        
        # التحقق من وجود الحساب في التقرير
        ws_report = excel.workbook['التقرير الإجمالي']
        account_found_before = False
        
        for row in range(7, 20):
            account_name_in_report = ws_report.cell(row=row, column=2).value
            if account_name_in_report == account_name:
                account_found_before = True
                break
        
        if account_found_before:
            print(f"✅ الحساب موجود في التقرير قبل الحذف")
        else:
            print(f"❌ الحساب غير موجود في التقرير قبل الحذف")
            return False
        
        # حذف الحساب
        excel.workbook.remove(excel.workbook[sheet_name])
        
        # تحديث التقرير
        excel.create_summary_report()
        
        # التحقق من عدم وجود الحساب في التقرير
        ws_report = excel.workbook['التقرير الإجمالي']
        account_found_after = False
        
        for row in range(7, 20):
            account_name_in_report = ws_report.cell(row=row, column=2).value
            if account_name_in_report == account_name:
                account_found_after = True
                break
        
        if not account_found_after:
            print(f"✅ تم حذف الحساب من التقرير بنجاح")
            return True
        else:
            print(f"❌ الحساب لا يزال موجود في التقرير بعد الحذف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار حذف الحساب: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار التحسينات الجديدة")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 4
    
    # اختبار إضافة المستند
    if test_document_addition():
        success_count += 1
    
    # اختبار ترحيل الرصيد
    if test_balance_transfer():
        success_count += 1
    
    # اختبار التقرير الإجمالي
    if test_summary_report():
        success_count += 1
    
    # اختبار تحديث التقرير عند حذف الحساب
    if test_account_deletion_update():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع الاختبارات!")
        print("✅ جميع التحسينات تعمل بشكل صحيح")
        return True
    else:
        print("❌ فشل في بعض الاختبارات")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
