#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تقارير أرصدة الحسابات المحدثة
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_account_balances_window():
    """اختبار نافذة تقارير أرصدة الحسابات"""
    try:
        print("🧪 اختبار نافذة تقارير أرصدة الحسابات المحدثة...")
        
        # إنشاء نافذة رئيسية مؤقتة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # محاكاة كائن parent
        class MockParent:
            def __init__(self):
                self.root = root
        
        parent = MockParent()
        
        # استيراد وإنشاء النافذة
        from account_balances_window import AccountBalancesWindow
        window = AccountBalancesWindow(parent)
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("📋 الميزات المتاحة:")
        print("   • زر تقرير حسابات المقبوضات (محسن)")
        print("   • الزر الثاني (معطل حالياً)")
        print("   • نافذة مساعدة محدثة")
        print("   • تنسيق محسن للواجهة")
        
        # عرض النافذة لفترة قصيرة
        root.after(3000, root.quit)  # إغلاق بعد 3 ثوانٍ
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نافذة تقارير أرصدة الحسابات المحدثة")
    print("=" * 60)
    
    success = test_account_balances_window()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 نجح اختبار النافذة المحدثة!")
        print("\n📝 التحديثات المطبقة:")
        print("✅ تحديث اسم الزر الأول إلى 'تقرير حسابات المقبوضات'")
        print("✅ إضافة دالة تقرير محسنة مع تنسيق متقدم")
        print("✅ إنشاء الزر الثاني (معطل حالياً)")
        print("✅ تحديث نافذة المساعدة")
        print("✅ تحسين الواجهة والرسائل")
    else:
        print("❌ فشل في اختبار النافذة المحدثة")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
