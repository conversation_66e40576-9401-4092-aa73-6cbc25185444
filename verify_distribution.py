#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من جاهزية التوزيع
"""

import os
import sys
import subprocess

def check_python():
    """التحقق من Python"""
    try:
        version = sys.version_info
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        
        if version.major >= 3 and version.minor >= 7:
            return True
        else:
            print("❌ يتطلب Python 3.7 أو أحدث")
            return False
    except Exception as e:
        print(f"❌ خطأ في فحص Python: {str(e)}")
        return False

def check_required_files():
    """التحقق من الملفات المطلوبة"""
    required_files = [
        'launcher.py',
        'app.py',
        'excel_manager.py',
        'document_window.py',
        'search_window.py',
        'manage_accounts.py',
        'requirements.txt',
        'accounting_system.spec',
        'build_executable.bat',
        'setup_build_environment.bat'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    return len(missing_files) == 0, missing_files

def check_packages():
    """التحقق من المكتبات المطلوبة"""
    packages = {
        'openpyxl': 'مكتبة Excel',
        'tkinter': 'واجهة المستخدم',
        'PyInstaller': 'تجميع التطبيق'
    }
    
    missing_packages = []
    
    for package, description in packages.items():
        try:
            if package == 'tkinter':
                import tkinter
                print(f"✅ {package} - {description}")
            elif package == 'openpyxl':
                import openpyxl
                print(f"✅ {package} {openpyxl.__version__} - {description}")
            elif package == 'PyInstaller':
                import PyInstaller
                print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} - غير مثبت")
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages

def check_build_environment():
    """التحقق من بيئة البناء"""
    print("🔍 فحص بيئة البناء...")
    
    # فحص مجلدات البناء
    build_dirs = ['build', 'dist', '__pycache__']
    for dir_name in build_dirs:
        if os.path.exists(dir_name):
            print(f"⚠️ {dir_name} موجود - سيتم حذفه عند البناء")
        else:
            print(f"✅ {dir_name} غير موجود - جيد")
    
    # فحص مساحة القرص
    try:
        import shutil
        total, used, free = shutil.disk_usage('.')
        free_mb = free // (1024 * 1024)
        print(f"💾 المساحة المتاحة: {free_mb} MB")
        
        if free_mb < 500:
            print("⚠️ المساحة قليلة - يُنصح بـ 500 MB على الأقل")
            return False
        else:
            print("✅ المساحة كافية للبناء")
            return True
    except Exception as e:
        print(f"⚠️ لا يمكن فحص المساحة: {str(e)}")
        return True

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("🧪 اختبار الوظائف الأساسية...")
    
    try:
        # اختبار استيراد الوحدات
        from excel_manager import ExcelManager
        print("✅ استيراد ExcelManager")
        
        # اختبار إنشاء مدير Excel
        excel = ExcelManager()
        print("✅ إنشاء ExcelManager")
        
        # اختبار tkinter
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        root.destroy()
        print("✅ اختبار tkinter")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للتحقق"""
    print("🚀 التحقق من جاهزية التوزيع")
    print("=" * 50)
    
    checks = []
    
    # فحص Python
    print("\n📍 فحص Python:")
    checks.append(check_python())
    
    # فحص الملفات المطلوبة
    print("\n📍 فحص الملفات المطلوبة:")
    files_ok, missing_files = check_required_files()
    checks.append(files_ok)
    
    # فحص المكتبات
    print("\n📍 فحص المكتبات المطلوبة:")
    packages_ok, missing_packages = check_packages()
    checks.append(packages_ok)
    
    # فحص بيئة البناء
    print("\n📍 فحص بيئة البناء:")
    checks.append(check_build_environment())
    
    # اختبار الوظائف
    print("\n📍 اختبار الوظائف الأساسية:")
    checks.append(test_basic_functionality())
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج التحقق:")
    
    passed = sum(checks)
    total = len(checks)
    
    print(f"✅ نجح: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 جاهز للتوزيع!")
        print("يمكنك الآن تشغيل: build_executable.bat")
        return True
    else:
        print("\n❌ غير جاهز للتوزيع")
        
        if not files_ok:
            print(f"📁 ملفات مفقودة: {', '.join(missing_files)}")
        
        if not packages_ok:
            print(f"📦 مكتبات مفقودة: {', '.join(missing_packages)}")
            print("شغل: setup_build_environment.bat")
        
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
