@echo off
chcp 65001 >nul
title إنشاء حزمة التوزيع - نظام إدارة المستندات المحاسبية

echo.
echo ========================================================
echo 📦 إنشاء حزمة التوزيع النهائية
echo    نظام إدارة المستندات المحاسبية
echo ========================================================
echo.

REM إنشاء مجلد التوزيع النهائي
set DIST_FOLDER=نظام_المحاسبة_الأردني_v1.0.0
if exist "%DIST_FOLDER%" rmdir /s /q "%DIST_FOLDER%"
mkdir "%DIST_FOLDER%"

echo 📁 إنشاء مجلد التوزيع: %DIST_FOLDER%

REM نسخ الملف التنفيذي
if exist "dist\نظام_إدارة_المستندات_المحاسبية.exe" (
    copy "dist\نظام_إدارة_المستندات_المحاسبية.exe" "%DIST_FOLDER%\"
    echo ✅ تم نسخ الملف التنفيذي
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    pause
    exit /b 1
)

REM نسخ ملفات التوثيق
copy "dist\README.txt" "%DIST_FOLDER%\"
copy "dist\تعليمات_التثبيت.txt" "%DIST_FOLDER%\"
copy "dist\معلومات_الإصدار.txt" "%DIST_FOLDER%\"
copy "dist\تشغيل_النظام.bat" "%DIST_FOLDER%\"
echo ✅ تم نسخ ملفات التوثيق

REM إنشاء ملف معلومات سريع
echo 🏥 نظام إدارة المستندات المحاسبية > "%DIST_FOLDER%\ابدأ_هنا.txt"
echo وزارة الصحة - المملكة الأردنية الهاشمية >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo. >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 🚀 للتشغيل السريع: >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo انقر مرتين على "تشغيل_النظام.bat" >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo أو انقر مرتين على "نظام_إدارة_المستندات_المحاسبية.exe" >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo. >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 📖 للمزيد من المعلومات: >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo اقرأ ملف "README.txt" >> "%DIST_FOLDER%\ابدأ_هنا.txt"

echo ✅ تم إنشاء ملف البداية السريعة

REM عرض معلومات الحزمة
echo.
echo ========================================================
echo ✅ تم إنشاء حزمة التوزيع بنجاح!
echo ========================================================
echo.
echo 📁 مجلد التوزيع: %DIST_FOLDER%
echo 📊 محتويات الحزمة:
dir "%DIST_FOLDER%" /b
echo.
echo 🎯 الحزمة جاهزة للتوزيع والنسخ إلى أجهزة أخرى
echo 💾 يمكنك ضغط المجلد إلى ملف ZIP لسهولة النقل
echo.
echo 📞 للدعم: قسم تقنية المعلومات - وزارة الصحة الأردنية
echo.

pause
