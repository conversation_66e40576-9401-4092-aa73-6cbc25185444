@echo off
chcp 65001 >nul
title إدارة النظام الصامت

:menu
cls
echo ================================================================
echo 🔇 إدارة النظام المحاسبي الصامت
echo ================================================================
echo.
echo 1. تشغيل النظام الصامت
echo 2. فحص حالة النظام
echo 3. إيقاف النظام
echo 4. عرض السجلات
echo 5. خروج
echo.
set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto :start_silent
if "%choice%"=="2" goto :check_status
if "%choice%"=="3" goto :stop_system
if "%choice%"=="4" goto :show_logs
if "%choice%"=="5" goto :exit
goto :menu

:start_silent
echo.
echo 🚀 بدء تشغيل النظام الصامت...
call تشغيل_صامت_نهائي.bat
pause
goto :menu

:check_status
echo.
echo 📊 فحص حالة النظام...
echo.
if exist "silent_status.txt" (
    echo ✅ ملف الحالة موجود:
    type silent_status.txt
) else (
    echo ❌ ملف الحالة غير موجود
)
echo.
echo 📋 العمليات النشطة:
tasklist /FI "IMAGENAME eq python.exe" 2>nul | find "python.exe"
if %errorlevel% neq 0 (
    echo ❌ لا توجد عمليات Python نشطة
)
pause
goto :menu

:stop_system
echo.
echo ⏹️ إيقاف النظام...
taskkill /F /IM python.exe >nul 2>&1
echo ✅ تم إيقاف جميع عمليات Python
pause
goto :menu

:show_logs
echo.
echo 📄 عرض السجلات...
echo.
if exist "silent_launcher.log" (
    echo === آخر 10 أسطر من السجل ===
    powershell "Get-Content 'silent_launcher.log' | Select-Object -Last 10"
) else (
    echo ❌ ملف السجل غير موجود
)
pause
goto :menu

:exit
echo.
echo 👋 شكراً لاستخدام النظام
exit /b 0
