import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from datetime import datetime
import os
from tkinter import messagebox
from excel_manager import ExcelManager

class MultiExcelManager:
    """مدير ملفات Excel متعددة"""
    
    def __init__(self):
        """تهيئة مدير الملفات المتعددة"""
        print("🔧 تهيئة نظام إدارة الملفات المتعددة...")
        
        # تعريف الملفات المطلوبة
        self.files_config = {
            'main': {
                'filename': 'accounting_system.xlsx',
                'display_name': 'نظام المحاسبة الرئيسي',
                'description': 'ملف الحسابات والمستندات الرئيسي'
            },
            'deductions': {
                'filename': 'Accounting system deductions.xlsx',
                'display_name': 'نظام الخصومات المحاسبية',
                'description': 'ملف إدارة الخصومات والاستقطاعات'
            }
        }
        
        # إنشاء مديري الملفات
        self.managers = {}
        self.current_manager_key = 'main'  # الملف النشط افتراضياً
        
        # تهيئة جميع الملفات
        self.initialize_all_files()
        
        print("✅ تم تهيئة نظام إدارة الملفات المتعددة بنجاح")
    
    def initialize_all_files(self):
        """تهيئة جميع ملفات Excel"""
        print("📁 تهيئة جميع ملفات Excel...")
        
        for key, config in self.files_config.items():
            try:
                print(f"🔄 تهيئة {config['display_name']}...")
                
                # إنشاء مدير Excel مخصص لهذا الملف
                manager = CustomExcelManager(config['filename'])
                
                # إضافة معلومات إضافية للمدير
                manager.display_name = config['display_name']
                manager.description = config['description']
                manager.file_key = key
                
                # حفظ المدير
                self.managers[key] = manager
                
                print(f"✅ تم تهيئة {config['display_name']} بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في تهيئة {config['display_name']}: {str(e)}")
                messagebox.showerror("خطأ في التهيئة", 
                                   f"فشل في تهيئة {config['display_name']}:\n{str(e)}")
    
    def get_current_manager(self):
        """الحصول على مدير الملف النشط حالياً"""
        return self.managers.get(self.current_manager_key)
    
    def switch_to_file(self, file_key):
        """التبديل إلى ملف معين"""
        if file_key in self.managers:
            self.current_manager_key = file_key
            print(f"🔄 تم التبديل إلى: {self.files_config[file_key]['display_name']}")
            return True
        else:
            print(f"❌ ملف غير موجود: {file_key}")
            return False
    
    def get_file_list(self):
        """الحصول على قائمة بجميع الملفات المتاحة"""
        file_list = []
        for key, config in self.files_config.items():
            file_info = {
                'key': key,
                'filename': config['filename'],
                'display_name': config['display_name'],
                'description': config['description'],
                'is_current': key == self.current_manager_key,
                'manager': self.managers.get(key)
            }
            file_list.append(file_info)
        return file_list
    
    def save_all_files(self):
        """حفظ جميع الملفات"""
        print("💾 حفظ جميع الملفات...")
        success_count = 0
        
        for key, manager in self.managers.items():
            try:
                if manager.save_workbook():
                    print(f"✅ تم حفظ {self.files_config[key]['display_name']}")
                    success_count += 1
                else:
                    print(f"❌ فشل في حفظ {self.files_config[key]['display_name']}")
            except Exception as e:
                print(f"❌ خطأ في حفظ {self.files_config[key]['display_name']}: {str(e)}")
        
        print(f"📊 تم حفظ {success_count} من {len(self.managers)} ملف")
        return success_count == len(self.managers)
    
    def get_manager_by_key(self, file_key):
        """الحصول على مدير ملف معين"""
        return self.managers.get(file_key)
    
    def create_backup_all_files(self):
        """إنشاء نسخة احتياطية من جميع الملفات"""
        print("🔄 إنشاء نسخة احتياطية من جميع الملفات...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_count = 0
        
        for key, manager in self.managers.items():
            try:
                import shutil
                backup_filename = f"{manager.current_file}.backup_{timestamp}"
                
                if os.path.exists(manager.current_file):
                    shutil.copy2(manager.current_file, backup_filename)
                    print(f"✅ نسخة احتياطية: {backup_filename}")
                    backup_count += 1
                    
            except Exception as e:
                print(f"❌ خطأ في النسخ الاحتياطي لـ {self.files_config[key]['display_name']}: {str(e)}")
        
        print(f"📊 تم إنشاء {backup_count} نسخة احتياطية")
        return backup_count > 0


class CustomExcelManager(ExcelManager):
    """مدير Excel مخصص يدعم أسماء ملفات مختلفة"""
    
    def __init__(self, filename):
        """تهيئة مدير Excel مع اسم ملف مخصص"""
        self.custom_filename = filename
        # استدعاء التهيئة الأساسية مع تجاوز اسم الملف
        super().__init__()
    
    def load_or_create_workbook(self):
        """تحميل الملف الموجود أو إنشاء ملف جديد - نسخة مخصصة"""
        # تحديث اسم الملف قبل التحميل
        self.current_file = self.custom_filename
        self.file_path = self.custom_filename
        
        print(f"🔍 فحص وجود ملف البيانات: {self.current_file}")

        # فحص وجود الملف المخصص لحفظ البيانات
        if os.path.exists(self.current_file):
            print(f"✅ تم العثور على ملف البيانات: {self.current_file}")

            # فحص حجم الملف للتأكد من أنه ليس فارغاً
            file_size = os.path.getsize(self.current_file)
            print(f"📊 حجم الملف: {file_size} بايت")

            if file_size < 1024:  # إذا كان الملف أقل من 1KB فهو على الأرجح تالف
                print("⚠️ الملف صغير جداً أو فارغ - سيتم إنشاء ملف جديد")
                self._backup_and_create_new()
                return

            # محاولة تحميل الملف الموجود
            try:
                print(f"🔄 محاولة تحميل البيانات من الملف...")
                self.workbook = openpyxl.load_workbook(self.current_file)

                # فحص سلامة الملف
                if not self.workbook.sheetnames:
                    print("⚠️ الملف لا يحتوي على أوراق - سيتم إنشاء ملف جديد")
                    self._backup_and_create_new()
                    return

                print(f"✅ تم تحميل الملف بنجاح")
                print(f"📋 عدد الأوراق: {len(self.workbook.sheetnames)}")
                print(f"📝 أسماء الأوراق: {', '.join(self.workbook.sheetnames[:5])}")

                # إضافة ورقة ترحيب إذا لم تكن موجودة
                if "مرحباً" not in self.workbook.sheetnames:
                    self._add_welcome_sheet()

            except openpyxl.utils.exceptions.InvalidFileException as e:
                print(f"❌ الملف تالف أو غير صالح: {str(e)}")
                self._backup_and_create_new()

            except PermissionError as e:
                print(f"❌ لا يمكن الوصول إلى الملف - قد يكون مفتوحاً في برنامج آخر: {str(e)}")
                messagebox.showerror("خطأ في الوصول",
                                   f"لا يمكن فتح الملف {self.current_file}\n\nقد يكون مفتوحاً في Excel أو برنامج آخر.\nالرجاء إغلاق البرنامج وإعادة المحاولة.")
                # إنشاء ملف جديد باسم مختلف
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                base_name = self.current_file.replace('.xlsx', '')
                self.current_file = f"{base_name}_new_{timestamp}.xlsx"
                self.file_path = self.current_file
                print(f"📝 إنشاء ملف جديد باسم: {self.current_file}")
                self.create_new_workbook()

            except Exception as e:
                print(f"❌ خطأ غير متوقع في تحميل الملف: {str(e)}")
                self._backup_and_create_new()
        else:
            # الملف غير موجود - إنشاء ملف جديد
            print(f"📝 لم يتم العثور على ملف البيانات: {self.current_file}")
            print("🆕 سيتم إنشاء ملف جديد لحفظ البيانات")
            self.create_new_workbook()
    
    def create_new_workbook(self):
        """إنشاء ملف Excel جديد - نسخة مخصصة"""
        print(f"📝 إنشاء ملف Excel جديد: {self.current_file}")
        self.workbook = openpyxl.Workbook()

        # إزالة الورقة الافتراضية إذا كانت موجودة
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])

        # إنشاء ورقة ترحيب مخصصة حسب نوع الملف
        self._add_welcome_sheet()

        # حفظ الملف الجديد
        try:
            self.workbook.save(self.current_file)
            print(f"✅ تم إنشاء وحفظ الملف الجديد: {self.current_file}")
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف الجديد: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في حفظ الملف الجديد: {str(e)}")
    
    def _add_welcome_sheet(self):
        """إضافة ورقة ترحيب مخصصة"""
        welcome_sheet = self.workbook.create_sheet("مرحباً")
        
        # تحديد نوع الملف من الاسم
        if "deductions" in self.current_file.lower():
            # ورقة ترحيب للخصومات
            welcome_sheet['A1'] = "مرحباً بك في نظام إدارة الخصومات المحاسبية"
            welcome_sheet['A2'] = "وزارة الصحة الأردنية - قسم الخصومات والاستقطاعات"
            welcome_sheet['A5'] = "لبدء الاستخدام:"
            welcome_sheet['A6'] = "1. اذهب إلى قائمة 'الخصومات' واختر 'إضافة خصم جديد'"
            welcome_sheet['A7'] = "2. أدخل تفاصيل الخصم والمبلغ"
            welcome_sheet['A8'] = "3. ابدأ بإدارة الخصومات والاستقطاعات"
        else:
            # ورقة ترحيب للنظام الرئيسي
            welcome_sheet['A1'] = "مرحباً بك في نظام إدارة المستندات المحاسبية"
            welcome_sheet['A2'] = "وزارة الصحة الأردنية - النظام الرئيسي"
            welcome_sheet['A5'] = "لبدء الاستخدام:"
            welcome_sheet['A6'] = "1. اذهب إلى قائمة 'الحسابات' واختر 'إضافة حساب جديد'"
            welcome_sheet['A7'] = "2. أدخل رقم الحساب واسمه والرصيد الافتتاحي"
            welcome_sheet['A8'] = "3. ابدأ بإضافة المستندات للحساب"
        
        welcome_sheet['A3'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y/%m/%d %H:%M')}"

        # تنسيق الورقة
        title_font = Font(size=14, bold=True)
        welcome_sheet['A1'].font = title_font
        welcome_sheet['A2'].font = Font(size=12, bold=True)
        
        print(f"✅ تم إضافة ورقة الترحيب للملف: {self.current_file}")
