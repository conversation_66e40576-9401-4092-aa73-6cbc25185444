#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لآلية الرصيد المرحل وإضافة الجداول الجديدة
"""

import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_balance_transfer_mechanism():
    """اختبار آلية الرصيد المرحل"""
    print("🧪 اختبار آلية الرصيد المرحل وإضافة الجداول")
    print("=" * 80)
    
    try:
        # حذف الملف إذا كان موجوداً
        if os.path.exists("accounting_system.xlsx"):
            os.remove("accounting_system.xlsx")
            print("🗑️ تم حذف الملف السابق")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        
        # إنشاء ExcelManager جديد
        print("\n📝 إنشاء ExcelManager جديد...")
        excel = ExcelManager()
        
        if excel.workbook:
            print("✅ تم إنشاء الملف بنجاح")
            
            # إنشاء حساب اختبار
            print("\n💼 إنشاء حساب اختبار...")
            account_num = "TRANSFER001"
            account_name = "حساب اختبار الترحيل"
            initial_balance = 10000.0
            
            result = excel.create_account_sheet(account_num, account_name, initial_balance, "official")
            print(f"إنشاء الحساب: {'نجح' if result else 'فشل'}")
            
            if result:
                sheet_name = f"{account_num}-{account_name}"
                
                # المرحلة 1: إضافة مستندات لملء الجدول الأول
                print(f"\n📄 المرحلة 1: ملء الجدول الأول...")
                documents_added = 0
                
                # إضافة مستندات حتى امتلاء الجدول
                for i in range(1, 133):  # 6 أقسام × 22 مستند = 132 مستند
                    doc_result = excel.add_document(sheet_name, 100.0, f"DOC{i:03d}", f"PAY{i:03d}")
                    if doc_result:
                        documents_added += 1
                        if documents_added % 20 == 0:
                            print(f"   تم إضافة {documents_added} مستند...")
                    else:
                        print(f"   توقف عند المستند {i} - الجدول ممتلئ")
                        break
                
                print(f"✅ تم إضافة {documents_added} مستند في الجدول الأول")
                
                # حفظ الملف
                excel.save_workbook()
                
                # تشخيص الحالة الحالية
                print(f"\n🔍 تشخيص الحساب بعد ملء الجدول الأول:")
                excel.diagnose_balance_transfer_mechanism(sheet_name)
                
                # المرحلة 2: محاولة إضافة مستند جديد (يجب أن ينشئ جدول جديد)
                print(f"\n📄 المرحلة 2: إضافة مستند جديد (إنشاء جدول ثاني)...")
                new_doc_result = excel.add_document(sheet_name, 500.0, "NEWDOC001", "NEWPAY001")
                
                if new_doc_result:
                    print("✅ تم إضافة المستند الجديد - تم إنشاء جدول ثاني")
                    
                    # حفظ الملف
                    excel.save_workbook()
                    
                    # تشخيص الحالة بعد إنشاء الجدول الثاني
                    print(f"\n🔍 تشخيص الحساب بعد إنشاء الجدول الثاني:")
                    excel.diagnose_balance_transfer_mechanism(sheet_name)
                    
                    # المرحلة 3: إضافة المزيد من المستندات للجدول الثاني
                    print(f"\n📄 المرحلة 3: إضافة مستندات للجدول الثاني...")
                    for i in range(2, 21):  # إضافة 19 مستند إضافي
                        doc_result = excel.add_document(sheet_name, 50.0, f"NEWDOC{i:03d}", f"NEWPAY{i:03d}")
                        if not doc_result:
                            break
                    
                    # حفظ الملف
                    excel.save_workbook()
                    
                    # تشخيص نهائي
                    print(f"\n🔍 التشخيص النهائي:")
                    excel.diagnose_balance_transfer_mechanism(sheet_name)
                    
                    # اختبار إضافة جدول ثالث يدوياً
                    print(f"\n📄 المرحلة 4: اختبار إضافة جدول ثالث يدوياً...")
                    ws = excel.workbook[sheet_name]
                    manual_table_result = excel._create_new_table(ws, account_num, account_name)
                    
                    if manual_table_result:
                        print("✅ تم إنشاء الجدول الثالث يدوياً")
                        excel.save_workbook()
                        
                        # تشخيص بعد الجدول الثالث
                        print(f"\n🔍 تشخيص بعد إنشاء الجدول الثالث:")
                        excel.diagnose_balance_transfer_mechanism(sheet_name)
                    else:
                        print("❌ فشل في إنشاء الجدول الثالث")
                    
                else:
                    print("❌ فشل في إضافة المستند الجديد")
                
                # ملخص النتائج
                print(f"\n📊 ملخص النتائج:")
                print(f"✅ تم اختبار آلية الرصيد المرحل بنجاح")
                print(f"✅ تم اختبار إنشاء جداول متعددة")
                print(f"✅ تم اختبار حماية البيانات اليدوية")
                print(f"📁 ملف الاختبار: accounting_system.xlsx")
                
                return True
            else:
                print("❌ فشل في إنشاء الحساب")
                return False
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def explain_balance_transfer_mechanism():
    """شرح آلية الرصيد المرحل"""
    print("\n" + "=" * 80)
    print("📚 شرح آلية الرصيد المرحل وإضافة الجداول")
    print("=" * 80)
    
    print("""
🔄 آلية الرصيد المرحل:

1️⃣ الجدول الأول:
   • يبدأ بالرصيد الافتتاحي في الخلية A8
   • يتم إضافة المستندات في الأقسام الستة (A, D, G, J, M, P)
   • كل قسم يحتوي على 22 صف للمستندات (الصفوف 10-31)
   • المجموع الكلي = الرصيد الافتتاحي + مجموع جميع المستندات

2️⃣ الجدول الثاني (عند الامتلاء):
   • يتم إنشاؤه تلقائياً عند امتلاء الجدول الأول
   • الرصيد المرحل = المجموع النهائي من الجدول الأول
   • يبدأ بـ "ما قبله" في العمود الأول
   • الأعمدة الأخرى تحتوي على صيغ ترحيل من العمود السابق

3️⃣ الجداول اللاحقة:
   • نفس آلية الجدول الثاني
   • الرصيد المرحل من آخر مجموع في الجداول السابقة
   • حد أقصى 10 جداول لكل حساب (قابل للتعديل)

🛡️ حماية البيانات اليدوية:
   • فحص دقيق للخلايا الفارغة قبل الكتابة
   • عدم الكتابة على البيانات المدخلة يدوياً
   • حماية من فقدان المستندات المدخلة يدوياً

🔍 آلية الفحص:
   • فحص امتلاء جميع الأقسام الستة
   • فحص المساحة المتاحة في الورقة
   • فحص عدد الجداول الموجودة
   • تأكيد إمكانية إضافة جدول جديد

📊 التشخيص الشامل:
   • عد الجداول الموجودة
   • تحليل كل جدول على حدة
   • حساب الرصيد المرحل لكل جدول
   • التحقق من صحة الترحيل
""")

if __name__ == "__main__":
    print("🚀 بدء اختبار آلية الرصيد المرحل")
    
    # شرح الآلية
    explain_balance_transfer_mechanism()
    
    # تشغيل الاختبار
    test_result = test_balance_transfer_mechanism()
    
    print("\n" + "=" * 80)
    print(f"🏁 نتيجة الاختبار: {'✅ نجح' if test_result else '❌ فشل'}")
    
    if test_result:
        print("\n🎉 تم اختبار آلية الرصيد المرحل بنجاح!")
        print("💡 يمكنك الآن فتح ملف accounting_system.xlsx لمراجعة النتائج.")
    else:
        print("\n⚠️ فشل في الاختبار. يرجى مراجعة رسائل الخطأ أعلاه.")
    
    input("\nاضغط Enter للإغلاق...")
