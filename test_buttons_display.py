#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض الأزرار في الواجهة الرئيسية
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_buttons_display():
    """اختبار عرض الأزرار"""
    try:
        print("🧪 اختبار عرض الأزرار في الواجهة...")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار عرض الأزرار")
        root.geometry("800x600")
        
        # محاكاة مدير المستخدمين
        class MockUserManager:
            def has_permission(self, permission):
                # إعطاء جميع الصلاحيات للاختبار
                return True
            
            def get_user_info(self):
                return {
                    'username': 'test_user',
                    'full_name': 'مستخدم الاختبار',
                    'role': 'admin'
                }
        
        # محاكاة التطبيق
        class MockApp:
            def __init__(self):
                self.root = root
                self.user_manager = MockUserManager()
            
            def add_account(self):
                messagebox.showinfo("اختبار", "زر إضافة حساب يعمل!")
            
            def add_document(self):
                messagebox.showinfo("اختبار", "زر إضافة مستند يعمل!")
            
            def manage_accounts(self):
                messagebox.showinfo("اختبار", "زر إدارة الحسابات يعمل!")
            
            def add_receipts_account(self):
                messagebox.showinfo("اختبار", "زر إضافة حساب مقبوضات يعمل!")
            
            def add_receipts_document(self):
                messagebox.showinfo("اختبار", "زر إضافة مستند مقبوضات يعمل!")
            
            def manage_receipts_accounts(self):
                messagebox.showinfo("اختبار", "زر إدارة حسابات المقبوضات يعمل!")
            
            def search_accounts(self):
                messagebox.showinfo("اختبار", "زر البحث يعمل!")
            
            def show_account_balances_window(self):
                messagebox.showinfo("اختبار", "زر نافذة تقارير الأرصدة يعمل!")
            
            def manage_users(self):
                messagebox.showinfo("اختبار", "زر إدارة المستخدمين يعمل!")
            
            def exit_application(self):
                root.quit()
            
            def add_button_hover_effect(self, button, normal_color, hover_color):
                """إضافة تأثير التمرير على الزر"""
                def on_enter(e):
                    button.config(bg=hover_color)
                
                def on_leave(e):
                    button.config(bg=normal_color)
                
                button.bind("<Enter>", on_enter)
                button.bind("<Leave>", on_leave)
            
            def create_action_buttons(self, parent_frame):
                """إنشاء أزرار العمليات الرئيسية"""
                # قائمة الأزرار مع الصلاحيات
                buttons_config = [
                    # =============================================================================
                    # قسم الحسابات الرئيسية
                    # =============================================================================
                    {
                        'text': '📁 إضافة حساب جديد',
                        'command': self.add_account,
                        'permission': 'add_account',
                        'color': '#3498db',
                        'hover_color': '#2980b9'
                    },
                    {
                        'text': '📝 إضافة مستند',
                        'command': self.add_document,
                        'permission': 'add_document',
                        'color': '#27ae60',
                        'hover_color': '#229954'
                    },
                    {
                        'text': '⚙️ إدارة الحسابات',
                        'command': self.manage_accounts,
                        'permission': 'edit_account',
                        'color': '#e74c3c',
                        'hover_color': '#c0392b'
                    },
                    
                    # =============================================================================
                    # قسم المقبوضات
                    # =============================================================================
                    {
                        'text': '💰 إضافة حساب مقبوضات',
                        'command': self.add_receipts_account,
                        'permission': 'add_account',
                        'color': '#16a085',
                        'hover_color': '#138d75'
                    },
                    {
                        'text': '📄 إضافة مستند مقبوضات',
                        'command': self.add_receipts_document,
                        'permission': 'add_document',
                        'color': '#2ecc71',
                        'hover_color': '#27ae60'
                    },
                    {
                        'text': '⚙️ إدارة حسابات المقبوضات',
                        'command': self.manage_receipts_accounts,
                        'permission': 'edit_account',
                        'color': '#8e44ad',
                        'hover_color': '#7d3c98'
                    },
                    
                    # =============================================================================
                    # قسم البحث والتقارير
                    # =============================================================================
                    {
                        'text': '🔍 بحث في الحسابات',
                        'command': self.search_accounts,
                        'permission': None,  # متاح للجميع
                        'color': '#f39c12',
                        'hover_color': '#e67e22'
                    },
                    {
                        'text': '📊 نافذة تقارير الأرصدة',
                        'command': self.show_account_balances_window,
                        'permission': 'view_reports',
                        'color': '#e67e22',
                        'hover_color': '#d35400'
                    },
                    
                    # =============================================================================
                    # قسم إدارة النظام
                    # =============================================================================
                    {
                        'text': '👥 إدارة المستخدمين',
                        'command': self.manage_users,
                        'permission': 'manage_users',
                        'color': '#34495e',
                        'hover_color': '#2c3e50'
                    },
                    {
                        'text': '🚪 خروج من النظام',
                        'command': self.exit_application,
                        'permission': None,  # متاح للجميع
                        'color': '#95a5a6',
                        'hover_color': '#7f8c8d'
                    }
                ]
                
                # إنشاء الأزرار في شبكة
                row = 0
                col = 0
                max_cols = 3
                
                print(f"📋 إنشاء {len(buttons_config)} زر...")
                
                for i, btn_config in enumerate(buttons_config):
                    # فحص الصلاحية
                    if btn_config['permission'] and not self.user_manager.has_permission(btn_config['permission']):
                        print(f"❌ تم تخطي الزر '{btn_config['text']}' - لا توجد صلاحية")
                        continue
                    
                    print(f"✅ إنشاء الزر {i+1}: {btn_config['text']}")
                    
                    # إنشاء الزر
                    btn = tk.Button(parent_frame,
                                   text=btn_config['text'],
                                   command=btn_config['command'],
                                   font=("Arial", 12, "bold"),
                                   bg=btn_config['color'],
                                   fg='white',
                                   relief='flat',
                                   padx=20,
                                   pady=15,
                                   cursor='hand2',
                                   width=25)
                    
                    btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
                    
                    # إضافة تأثيرات بصرية
                    self.add_button_hover_effect(btn, btn_config['color'], btn_config['hover_color'])
                    
                    # انتقال للعمود التالي
                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1
                
                # تعيين وزن الأعمدة
                for i in range(max_cols):
                    parent_frame.grid_columnconfigure(i, weight=1)
                
                print(f"✅ تم إنشاء جميع الأزرار في {row + 1} صف")
        
        # إنشاء التطبيق المحاكي
        app = MockApp()
        
        # إنشاء الإطار الرئيسي
        main_frame = tk.Frame(root, bg='#f8f9fa')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان الاختبار
        title_label = tk.Label(main_frame, 
                              text="🧪 اختبار عرض الأزرار المحدثة",
                              font=("Arial", 16, "bold"),
                              bg='#f8f9fa',
                              fg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg='#f8f9fa')
        buttons_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الأزرار
        app.create_action_buttons(buttons_frame)
        
        # معلومات الاختبار
        info_label = tk.Label(main_frame,
                             text="انقر على أي زر لاختبار وظيفته",
                             font=("Arial", 10),
                             bg='#f8f9fa',
                             fg='#7f8c8d')
        info_label.pack(pady=(20, 0))
        
        print("✅ تم إنشاء نافذة الاختبار")
        print("🖱️ انقر على الأزرار لاختبارها")
        
        # عرض النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأزرار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_app_integration():
    """اختبار التكامل مع التطبيق الأصلي"""
    try:
        print("\n🔗 اختبار التكامل مع التطبيق الأصلي...")
        
        # محاولة استيراد التطبيق
        sys.path.append('.')
        
        try:
            from app import AccountingApp
            print("✅ تم استيراد التطبيق بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد التطبيق: {str(e)}")
            return False
        
        # فحص وجود الدالة
        if hasattr(AccountingApp, 'create_action_buttons'):
            print("✅ دالة create_action_buttons موجودة")
        else:
            print("❌ دالة create_action_buttons مفقودة")
            return False
        
        # فحص وجود الدالة setup_modern_ui
        if hasattr(AccountingApp, 'setup_modern_ui'):
            print("✅ دالة setup_modern_ui موجودة")
        else:
            print("❌ دالة setup_modern_ui مفقودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار عرض الأزرار")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # اختبار التكامل
    if test_app_integration():
        success_count += 1
    
    # اختبار عرض الأزرار
    if test_buttons_display():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات عرض الأزرار!")
        print("\n💡 إذا لم تظهر الأزرار في التطبيق الأصلي:")
        print("1. تأكد من تسجيل الدخول بمستخدم له صلاحيات")
        print("2. تأكد من استدعاء دالة setup_modern_ui")
        print("3. فحص وجود أخطاء في وحدة التحكم")
        print("4. تأكد من تحديث ملف app.py")
        return True
    else:
        print("❌ فشل في بعض اختبارات عرض الأزرار")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
