#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استمرارية البيانات - نظام إدارة المستندات المحاسبية
"""

import os
import sys
import openpyxl
from datetime import datetime

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_data_persistence():
    """اختبار حفظ واسترجاع البيانات"""
    print("🧪 بدء اختبار استمرارية البيانات")
    print("=" * 60)
    
    try:
        # استيراد ExcelManager
        from excel_manager import ExcelManager
        
        # إنشاء ملف اختبار
        test_file = "test_accounting_system.xlsx"
        
        # حذف الملف إذا كان موجوداً
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🗑️ تم حذف ملف الاختبار السابق: {test_file}")
        
        print("\n📝 المرحلة 1: إنشاء ملف جديد وإضافة بيانات")
        print("-" * 40)
        
        # إنشاء ExcelManager جديد
        excel1 = ExcelManager()
        excel1.current_file = test_file
        excel1.file_path = test_file
        
        # إنشاء حساب اختبار
        account_created = excel1.create_account_sheet("001", "حساب اختبار", 1000.0)
        print(f"✅ إنشاء الحساب: {'نجح' if account_created else 'فشل'}")
        
        # إضافة مستند اختبار
        if account_created:
            doc_added = excel1.add_document("001-حساب اختبار", 500.0, "DOC001", "PAY001")
            print(f"✅ إضافة المستند: {'نجح' if doc_added else 'فشل'}")
        
        # حفظ الملف
        saved = excel1.save_workbook(test_file)
        print(f"✅ حفظ الملف: {'نجح' if saved else 'فشل'}")
        
        # التحقق من وجود الملف
        file_exists = os.path.exists(test_file)
        print(f"✅ وجود الملف: {'نعم' if file_exists else 'لا'}")
        
        if file_exists:
            file_size = os.path.getsize(test_file)
            print(f"📊 حجم الملف: {file_size} بايت")
        
        print("\n🔄 المرحلة 2: إعادة تحميل البيانات")
        print("-" * 40)
        
        # إنشاء ExcelManager جديد لتحميل البيانات
        excel2 = ExcelManager()
        excel2.current_file = test_file
        excel2.file_path = test_file
        excel2.load_or_create_workbook()
        
        # التحقق من الصفحات المحملة
        if excel2.workbook:
            sheets = excel2.workbook.sheetnames
            print(f"📋 الصفحات المحملة: {sheets}")
            
            # التحقق من وجود الحساب
            account_sheet = "001-حساب اختبار"
            if account_sheet in sheets:
                print(f"✅ تم العثور على الحساب: {account_sheet}")
                
                # فحص محتوى الحساب
                ws = excel2.workbook[account_sheet]
                
                # فحص الرصيد الافتتاحي
                opening_balance = ws['A9'].value
                print(f"💰 الرصيد الافتتاحي: {opening_balance}")
                
                # البحث عن المستندات
                documents_found = 0
                for row in range(10, 33):  # البحث في منطقة المستندات
                    for col in range(1, 19, 3):  # كل 3 أعمدة
                        cell_value = ws.cell(row=row, column=col).value
                        if cell_value and isinstance(cell_value, (int, float)) and cell_value > 0:
                            documents_found += 1
                            print(f"📄 مستند موجود: {cell_value}")
                
                print(f"📊 عدد المستندات الموجودة: {documents_found}")
                
            else:
                print(f"❌ لم يتم العثور على الحساب: {account_sheet}")
        else:
            print("❌ فشل في تحميل الملف")
        
        print("\n🧹 تنظيف ملفات الاختبار")
        print("-" * 40)
        
        # حذف ملف الاختبار
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🗑️ تم حذف ملف الاختبار: {test_file}")
        
        print("\n✅ انتهى اختبار استمرارية البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_file():
    """اختبار تحميل ملف موجود"""
    print("\n🔍 اختبار تحميل الملف الموجود")
    print("=" * 60)
    
    main_file = "accounting_system.xlsx"
    
    if os.path.exists(main_file):
        print(f"📁 الملف الرئيسي موجود: {main_file}")
        
        try:
            # محاولة تحميل الملف مباشرة
            workbook = openpyxl.load_workbook(main_file)
            sheets = workbook.sheetnames
            print(f"📋 الصفحات الموجودة: {sheets}")
            
            # عد الحسابات
            account_count = 0
            for sheet_name in sheets:
                if sheet_name not in ['التقارير', 'تقرير المستندات', 'التقرير الإجمالي']:
                    account_count += 1
                    print(f"💼 حساب: {sheet_name}")
            
            print(f"📊 إجمالي الحسابات: {account_count}")
            
            # اختبار ExcelManager
            print("\n🔧 اختبار ExcelManager مع الملف الموجود:")
            from excel_manager import ExcelManager
            excel = ExcelManager()
            
            if excel.workbook:
                loaded_sheets = excel.workbook.sheetnames
                print(f"✅ تم تحميل الصفحات: {loaded_sheets}")
                print(f"📊 عدد الصفحات المحملة: {len(loaded_sheets)}")
            else:
                print("❌ فشل في تحميل الملف عبر ExcelManager")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل الملف: {str(e)}")
            import traceback
            traceback.print_exc()
    else:
        print(f"📁 الملف الرئيسي غير موجود: {main_file}")

if __name__ == "__main__":
    print("🚀 بدء اختبارات استمرارية البيانات")
    print("=" * 80)
    
    # اختبار إنشاء وحفظ واسترجاع البيانات
    test_result = test_data_persistence()
    
    # اختبار الملف الموجود
    test_existing_file()
    
    print("\n" + "=" * 80)
    print(f"🏁 انتهت الاختبارات - النتيجة: {'نجح' if test_result else 'فشل'}")
    
    input("\nاضغط Enter للإغلاق...")
