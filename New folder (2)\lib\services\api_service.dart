import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/account.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:5000';
  
  // تسجيل الدخول
  static Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/login'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'password': password,
        }),
      );

      return jsonDecode(response.body);
    } catch (e) {
      return {'success': false, 'message': 'خطأ في الاتصال: $e'};
    }
  }

  // تسجيل الخروج
  static Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/logout'),
        headers: {'Content-Type': 'application/json'},
      );

      return jsonDecode(response.body);
    } catch (e) {
      return {'success': false, 'message': 'خطأ في الاتصال: $e'};
    }
  }

  // الحصول على قائمة الحسابات
  static Future<List<Account>> getAccounts() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/accounts'),
        headers: {'Content-Type': 'application/json'},
      );

      final data = jsonDecode(response.body);
      if (data['success']) {
        return (data['accounts'] as List)
            .map((account) => Account.fromJson(account))
            .toList();
      } else {
        throw Exception(data['message']);
      }
    } catch (e) {
      throw Exception('خطأ في جلب الحسابات: $e');
    }
  }

  // الحصول على تفاصيل حساب
  static Future<AccountDetails> getAccountDetails(int accountId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/account/$accountId/details'),
        headers: {'Content-Type': 'application/json'},
      );

      final data = jsonDecode(response.body);
      if (data['success']) {
        return AccountDetails.fromJson(data);
      } else {
        throw Exception(data['message']);
      }
    } catch (e) {
      throw Exception('خطأ في جلب تفاصيل الحساب: $e');
    }
  }

  // إضافة مستند جديد
  static Future<Map<String, dynamic>> addDocument({
    required int accountNumber,
    required String accountName,
    required double amount,
    required String documentNumber,
    required String paymentNumber,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/document/add'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'account_number': accountNumber,
          'account_name': accountName,
          'amount': amount,
          'document_number': documentNumber,
          'payment_number': paymentNumber,
        }),
      );

      return jsonDecode(response.body);
    } catch (e) {
      return {'success': false, 'message': 'خطأ في الاتصال: $e'};
    }
  }

  // إضافة حساب جديد
  static Future<Map<String, dynamic>> addAccount({
    required int accountNumber,
    required String accountName,
    required double openingBalance,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/account/add'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'account_number': accountNumber,
          'account_name': accountName,
          'opening_balance': openingBalance,
        }),
      );

      return jsonDecode(response.body);
    } catch (e) {
      return {'success': false, 'message': 'خطأ في الاتصال: $e'};
    }
  }

  // البحث في المستندات
  static Future<List<Document>> searchDocuments({
    required String query,
    String searchType = 'all',
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/search?q=$query&type=$searchType'),
        headers: {'Content-Type': 'application/json'},
      );

      final data = jsonDecode(response.body);
      if (data['success']) {
        return (data['results'] as List)
            .map((result) => Document.fromJson(result))
            .toList();
      } else {
        throw Exception(data['message']);
      }
    } catch (e) {
      throw Exception('خطأ في البحث: $e');
    }
  }
}
