import tkinter as tk
from tkinter import ttk, messagebox

class ManageAccountsDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إدارة الحسابات")
        self.excel = excel

        # تكوين النافذة
        self.geometry("800x500")
        self.configure(bg='#f0f0f0')

        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # قائمة الحسابات
        self.create_accounts_list(main_frame)

        # أزرار التحكم
        self.create_control_buttons(main_frame)

        # تحديث القائمة
        self.load_accounts()

    def create_accounts_list(self, parent):
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="الحسابات", padding="5")
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)

        # إنشاء جدول الحسابات
        columns = ('رقم الحساب', 'اسم الحساب', 'الرصيد')
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show='headings')

        # تعيين العناوين
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=150)

        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)

        # وضع العناصر في الإطار
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # تمكين التمدد
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

    def create_control_buttons(self, parent):
        # إطار الأزرار
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=1, column=0, pady=10)

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(pady=5)

        ttk.Button(row1_frame, text="عرض تفاصيل الحساب",
                  command=self.view_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تصدير تفاصيل الحساب",
                  command=self.export_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تعديل الحساب",
                  command=self.edit_account).pack(side=tk.LEFT, padx=5)

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(pady=5)

        ttk.Button(row2_frame, text="حذف الحساب",
                  command=self.delete_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="تحديث القائمة",
                  command=self.load_accounts).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="إغلاق",
                  command=self.destroy).pack(side=tk.LEFT, padx=5)

    def load_accounts(self):
        """تحميل الحسابات في الجدول"""
        # مسح الجدول
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # تحميل الحسابات
        for sheet_name in self.excel.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                try:
                    # استخراج رقم واسم الحساب
                    account_num, account_name = sheet_name.split('-', 1)

                    # الحصول على الرصيد
                    ws = self.excel.workbook[sheet_name]
                    balance = ws['A33'].value or 0

                    # إضافة الصف
                    self.accounts_tree.insert('', tk.END, values=(account_num, account_name, balance))
                except:
                    continue

    def view_account_details(self):
        """عرض تفاصيل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لعرض تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل
        details_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        self.wait_window(details_dialog)

    def export_account_details(self):
        """تصدير تفاصيل الحساب المحدد مباشرة"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتصدير تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل مؤقتاً للحصول على البيانات
        temp_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        temp_dialog.withdraw()  # إخفاء النافذة

        # تصدير التفاصيل مباشرة
        temp_dialog.export_details()

        # إغلاق النافذة المؤقتة
        temp_dialog.destroy()

    def edit_account(self):
        """تعديل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتعديله")
            return

        item = selection[0]
        account_num, account_name, _ = self.accounts_tree.item(item)['values']

        # إنشاء نافذة التعديل
        dialog = AccountEditDialog(self, self.excel, account_num, account_name)
        self.wait_window(dialog)

        # تحديث القائمة
        self.load_accounts()

    def delete_account(self):
        """حذف الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لحذفه")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # نافذة تأكيد بسيطة وموثوقة
        confirm_message = (f"هل أنت متأكد من حذف الحساب؟\n\n"
                          f"رقم الحساب: {account_num}\n"
                          f"اسم الحساب: {account_name}\n"
                          f"الرصيد الحالي: {balance}\n\n"
                          f"تحذير: سيتم حذف الحساب نهائياً مع جميع المستندات!")

        if messagebox.askyesno("تأكيد حذف الحساب", confirm_message):
            try:
                print(f"🗑️ حذف الحساب: {sheet_name}")  # للتشخيص

                # التحقق من وجود الحساب
                if sheet_name not in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", f"الحساب '{sheet_name}' غير موجود")
                    return

                # حذف الصفحة
                self.excel.workbook.remove(self.excel.workbook[sheet_name])
                print(f"✅ تم حذف الصفحة من الملف")  # للتشخيص

                # تحديث التقرير الإجمالي تلقائياً
                print(f"📊 تحديث التقرير الإجمالي بعد حذف الحساب...")
                self.excel.create_summary_report()

                # حفظ التغييرات
                if self.excel.save_workbook():
                    print(f"✅ تم حفظ الملف بنجاح")  # للتشخيص

                    # تحديث القائمة
                    self.load_accounts()

                    messagebox.showinfo("نجاح", f"تم حذف الحساب '{account_name}' بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء حذف الحساب: {str(e)}"
                print(f"❌ {error_msg}")  # للتشخيص
                messagebox.showerror("خطأ", error_msg)

class AccountEditDialog(tk.Toplevel):
    def __init__(self, parent, excel, account_num, account_name):
        super().__init__(parent)
        self.title("تعديل الحساب")
        self.excel = excel
        self.old_sheet_name = f"{account_num}-{account_name}"

        # تكوين النافذة
        self.geometry("400x200")

        # حقول الإدخال
        ttk.Label(self, text="رقم الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_num = ttk.Entry(self)
        self.account_num.insert(0, account_num)
        self.account_num.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(self, text="اسم الحساب:").grid(row=1, column=0, padx=5, pady=5)
        self.account_name = ttk.Entry(self)
        self.account_name.insert(0, account_name)
        self.account_name.grid(row=1, column=1, padx=5, pady=5)

        # أزرار
        ttk.Button(self, text="حفظ",
                  command=self.save_changes).grid(row=2, column=0, columnspan=2, pady=20)

    def save_changes(self):
        """حفظ التغييرات على الحساب"""
        try:
            new_sheet_name = f"{self.account_num.get()}-{self.account_name.get()}"

            if new_sheet_name != self.old_sheet_name:
                # التحقق من عدم وجود حساب بنفس الاسم
                if new_sheet_name in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", "يوجد حساب بنفس الرقم والاسم")
                    return

                # تغيير اسم الصفحة
                sheet = self.excel.workbook[self.old_sheet_name]
                sheet.title = new_sheet_name

                # حفظ التغييرات
                if self.excel.save_workbook():
                    messagebox.showinfo("نجاح", "تم تعديل الحساب بنجاح")
                    self.destroy()
            else:
                self.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل الحساب: {str(e)}")

class DeleteConfirmDialog(tk.Toplevel):
    """نافذة تأكيد حذف الحساب"""
    def __init__(self, parent, account_num, account_name, balance, sheet_name):
        super().__init__(parent)
        self.title("تأكيد حذف الحساب")
        self.account_num = account_num
        self.account_name = account_name
        self.balance = balance
        self.sheet_name = sheet_name
        self.confirmed = False

        # تكوين النافذة
        self.geometry("500x300")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة تحذير
        warning_label = tk.Label(main_frame, text="⚠️", font=("Arial", 48),
                                bg='#f0f0f0', fg='#ff6b6b')
        warning_label.pack(pady=(0, 20))

        # عنوان التحذير
        title_label = tk.Label(main_frame, text="تحذير: حذف الحساب",
                              font=("Arial", 16, "bold"),
                              bg='#f0f0f0', fg='#d63031')
        title_label.pack(pady=(0, 10))

        # معلومات الحساب
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(info_frame, text="معلومات الحساب المراد حذفه:",
                font=("Arial", 12, "bold"), bg='#ffffff').pack(pady=5)

        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"الرصيد الحالي: {self.balance}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10, pady=(0, 5))

        # رسالة التحذير
        warning_text = ("سيتم حذف الحساب نهائياً مع جميع المستندات والبيانات المرتبطة به.\n"
                       "هذا الإجراء لا يمكن التراجع عنه!")
        warning_label = tk.Label(main_frame, text=warning_text,
                                font=("Arial", 11), bg='#f0f0f0', fg='#d63031',
                                wraplength=400, justify=tk.CENTER)
        warning_label.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack()

        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="حذف الحساب",
                              command=self.confirm_delete,
                              bg='#d63031', fg='white',
                              font=("Arial", 11, "bold"),
                              padx=20, pady=5)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              command=self.cancel_delete,
                              bg='#74b9ff', fg='white',
                              font=("Arial", 11),
                              padx=20, pady=5)
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفتاح Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.cancel_delete())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def confirm_delete(self):
        """تأكيد الحذف"""
        self.confirmed = True
        self.destroy()

    def cancel_delete(self):
        """إلغاء الحذف"""
        self.confirmed = False
        self.destroy()

class AccountDetailsDialog(tk.Toplevel):
    """نافذة عرض تفاصيل الحساب المحسنة والشاملة"""
    def __init__(self, parent, excel, sheet_name, account_num, account_name):
        super().__init__(parent)
        self.title(f"📄 تفاصيل الحساب: {account_name}")
        self.excel = excel
        self.sheet_name = sheet_name
        self.account_num = account_num
        self.account_name = account_name

        # بيانات الحساب
        self.account_data = []
        self.filtered_data = []
        self.sort_column = None
        self.sort_reverse = False

        # تكوين النافذة
        self.geometry("1200x800")  # نافذة أكبر لعرض أفضل
        self.configure(bg='#f8f9fa')
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.load_account_data()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة المحسنة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # عنوان النافذة
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        title_label = ttk.Label(title_frame, text=f"📄 تفاصيل الحساب: {self.account_name}",
                               font=('Arial', 16, 'bold'))
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text=f"رقم الحساب: {self.account_num}",
                                  font=('Arial', 12))
        subtitle_label.pack()

        # إطار معلومات الحساب
        self.create_account_info_frame(main_frame)

        # إطار المستندات
        self.create_documents_frame(main_frame)

        # إطار الأزرار
        self.create_buttons_frame(main_frame)

    def create_account_info_frame(self, parent):
        """إنشاء إطار معلومات الحساب"""
        info_frame = ttk.LabelFrame(parent, text="📊 معلومات الحساب", padding="15")
        info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        info_frame.grid_columnconfigure(1, weight=1)
        info_frame.grid_columnconfigure(3, weight=1)

        # الرصيد الافتتاحي
        ttk.Label(info_frame, text="الرصيد الافتتاحي:",
                 font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 10), pady=5)
        self.opening_balance_label = ttk.Label(info_frame, text="جاري التحميل...",
                                             font=('Arial', 10))
        self.opening_balance_label.grid(row=0, column=1, sticky=tk.W, pady=5)

        # عدد المستندات
        ttk.Label(info_frame, text="عدد المستندات:",
                 font=('Arial', 10, 'bold')).grid(row=0, column=2, sticky=tk.W, padx=(20, 10), pady=5)
        self.documents_count_label = ttk.Label(info_frame, text="جاري التحميل...",
                                             font=('Arial', 10))
        self.documents_count_label.grid(row=0, column=3, sticky=tk.W, pady=5)

        # الرصيد الحالي
        ttk.Label(info_frame, text="الرصيد الحالي:",
                 font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=5)
        self.current_balance_label = ttk.Label(info_frame, text="جاري التحميل...",
                                             font=('Arial', 10, 'bold'), foreground='#2e7d32')
        self.current_balance_label.grid(row=1, column=1, sticky=tk.W, pady=5)

        # إجمالي المبالغ
        ttk.Label(info_frame, text="إجمالي المبالغ:",
                 font=('Arial', 10, 'bold')).grid(row=1, column=2, sticky=tk.W, padx=(20, 10), pady=5)
        self.total_amount_label = ttk.Label(info_frame, text="جاري التحميل...",
                                          font=('Arial', 10, 'bold'), foreground='#1976d2')
        self.total_amount_label.grid(row=1, column=3, sticky=tk.W, pady=5)

    def create_documents_frame(self, parent):
        """إنشاء إطار المستندات"""
        documents_frame = ttk.LabelFrame(parent, text="📁 قائمة المستندات", padding="10")
        documents_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        documents_frame.grid_rowconfigure(1, weight=1)
        documents_frame.grid_columnconfigure(0, weight=1)

        # إطار البحث والترتيب
        search_frame = ttk.Frame(documents_frame)
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_frame.grid_columnconfigure(1, weight=1)

        # بحث
        ttk.Label(search_frame, text="🔍 بحث:").grid(row=0, column=0, padx=(0, 10))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))
        search_entry.bind('<KeyRelease>', self.on_search)

        # ترتيب
        ttk.Label(search_frame, text="🔄 ترتيب:").grid(row=0, column=2, padx=(0, 10))
        self.sort_var = tk.StringVar(value="حسب رقم المستند")
        sort_combo = ttk.Combobox(search_frame, textvariable=self.sort_var,
                                 values=["حسب رقم المستند", "حسب رقم التأدية", "حسب المبلغ", "حسب آلية الترحيل"],
                                 state='readonly', width=20)
        sort_combo.grid(row=0, column=3, padx=(0, 10))
        sort_combo.bind('<<ComboboxSelected>>', self.on_sort_change)

        # زر مسح البحث
        ttk.Button(search_frame, text="❌ مسح",
                  command=self.clear_search).grid(row=0, column=4)

        # جدول المستندات (مع رقم التأدية)
        columns = ('رقم المستند', 'رقم التأدية', 'المبلغ', 'الموقع')
        self.documents_tree = ttk.Treeview(documents_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين والعروض
        column_widths = {
            'رقم المستند': 180,
            'رقم التأدية': 150,
            'المبلغ': 130,
            'الموقع': 100
        }

        for col in columns:
            self.documents_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.documents_tree.column(col, width=column_widths[col], anchor='center')
            # تحسين عرض النصوص
            if col in ['رقم المستند', 'رقم التأدية']:
                self.documents_tree.column(col, anchor='w')  # محاذاة يسار للأرقام
            elif col == 'المبلغ':
                self.documents_tree.column(col, anchor='e')  # محاذاة يمين للمبالغ

        # شريط التمرير
        v_scrollbar = ttk.Scrollbar(documents_frame, orient=tk.VERTICAL, command=self.documents_tree.yview)
        self.documents_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(documents_frame, orient=tk.HORIZONTAL, command=self.documents_tree.xview)
        self.documents_tree.configure(xscrollcommand=h_scrollbar.set)

        # وضع العناصر
        self.documents_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=2, column=0, sticky=(tk.W, tk.E))

        # ربط النقر المزدوج
        self.documents_tree.bind('<Double-1>', self.on_document_double_click)

        # إحصائيات العرض
        stats_frame = ttk.Frame(documents_frame)
        stats_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.display_stats_label = ttk.Label(stats_frame, text="جاري التحميل...",
                                            font=('Arial', 10))
        self.display_stats_label.pack(side=tk.LEFT)

    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # أزرار العمليات
        ttk.Button(buttons_frame, text="🖨️ طباعة التقرير",
                  command=self.print_report).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="💾 تصدير Excel",
                  command=self.export_to_excel).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="📄 تصدير PDF",
                  command=self.export_to_pdf).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="🔄 تحديث",
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="❌ إغلاق",
                  command=self.destroy).pack(side=tk.RIGHT)

    # دوال البحث والترتيب
    def on_search(self, event=None):
        """معالج البحث (في رقم المستند ورقم التأدية)"""
        search_term = self.search_var.get().lower()
        if search_term:
            self.filtered_data = [doc for doc in self.account_data
                                if (search_term in doc['doc_number'].lower() or
                                    search_term in doc.get('voucher_num', '').lower())]
        else:
            self.filtered_data = self.account_data.copy()

        self.update_documents_display()

    def clear_search(self):
        """مسح البحث"""
        self.search_var.set("")
        self.filtered_data = self.account_data.copy()
        self.update_documents_display()

    def on_sort_change(self, event=None):
        """معالج تغيير الترتيب"""
        self.apply_sort()
        self.update_documents_display()

    def sort_by_column(self, column):
        """ترتيب حسب عمود"""
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False

        self.apply_sort()
        self.update_documents_display()

    def apply_sort(self):
        """تطبيق الترتيب"""
        sort_type = self.sort_var.get()

        if sort_type == "حسب رقم المستند":
            self.filtered_data.sort(key=lambda x: x['doc_number'], reverse=self.sort_reverse)
        elif sort_type == "حسب رقم التأدية":
            self.filtered_data.sort(key=lambda x: x.get('voucher_num', ''), reverse=self.sort_reverse)
        elif sort_type == "حسب المبلغ":
            self.filtered_data.sort(key=lambda x: x['amount'], reverse=self.sort_reverse)
        elif sort_type == "حسب آلية الترحيل":
            self.filtered_data.sort(key=lambda x: (x['table_number'], x['row_number'], x['section_index']),
                                   reverse=self.sort_reverse)

    def update_documents_display(self):
        """تحديث عرض المستندات"""
        # مسح العرض الحالي
        for item in self.documents_tree.get_children():
            self.documents_tree.delete(item)

        # إضافة المستندات المفلترة (مع رقم التأدية)
        for doc in self.filtered_data:
            self.documents_tree.insert('', 'end', values=(
                doc['doc_number'],
                doc.get('voucher_num', ''),  # رقم التأدية
                f"{doc['amount']:.3f}",
                doc['position']
            ))

        # تحديث إحصائيات العرض
        total_displayed = len(self.filtered_data)
        total_amount_displayed = sum(doc['amount'] for doc in self.filtered_data)

        self.display_stats_label.config(
            text=f"عرض {total_displayed} من {len(self.account_data)} مستند | إجمالي المعروض: {total_amount_displayed:,.3f} دينار"
        )

    def on_document_double_click(self, event):
        """معالج النقر المزدوج على مستند"""
        selection = self.documents_tree.selection()
        if selection:
            item = self.documents_tree.item(selection[0])
            doc_number = item['values'][0]
            voucher_num = item['values'][1]
            amount = item['values'][2]
            position = item['values'][3]
            messagebox.showinfo("تفاصيل المستند",
                              f"رقم المستند: {doc_number}\nرقم التأدية: {voucher_num}\nالمبلغ: {amount}\nالموقع: {position}")

    # دوال التصدير والطباعة
    def print_report(self):
        """طباعة التقرير"""
        try:
            self.generate_html_report(for_print=True)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {str(e)}")

    def export_to_excel(self):
        """تصدير إلى Excel"""
        try:
            from tkinter import filedialog
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill

            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("ملفات Excel", "*.xlsx")],
                title=f"حفظ تقرير {self.account_name}"
            )

            if filename:
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = f"تفاصيل {self.account_name}"

                # عنوان التقرير
                ws['A1'] = f"تفاصيل الحساب: {self.account_name}"
                ws['A1'].font = Font(size=16, bold=True)
                ws.merge_cells('A1:D1')

                # معلومات الحساب
                ws['A3'] = f"رقم الحساب: {self.account_num}"
                ws['A4'] = f"عدد المستندات: {len(self.filtered_data)}"
                ws['A5'] = f"إجمالي المبالغ: {sum(doc['amount'] for doc in self.filtered_data):,.3f}"

                # عناوين الأعمدة (مع رقم التأدية)
                headers = ['رقم المستند', 'رقم التأدية', 'المبلغ', 'الموقع']
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=7, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')

                # بيانات المستندات (مع رقم التأدية)
                for row, doc in enumerate(self.filtered_data, 8):
                    ws.cell(row=row, column=1, value=doc['doc_number'])
                    ws.cell(row=row, column=2, value=doc.get('voucher_num', ''))
                    ws.cell(row=row, column=3, value=doc['amount'])
                    ws.cell(row=row, column=4, value=doc['position'])

                    # تنسيق الخلايا
                    ws.cell(row=row, column=3).number_format = '0.000'  # تنسيق المبلغ

                wb.save(filename)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Excel: {str(e)}")

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        try:
            from tkinter import filedialog
            import webbrowser
            import tempfile
            import os

            filename = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("ملفات HTML", "*.html")],
                title=f"حفظ تقرير {self.account_name}"
            )

            if filename:
                html_content = self.generate_html_report()
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                # فتح في المتصفح
                webbrowser.open(f'file://{os.path.abspath(filename)}')
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{filename}\n\nيمكنك طباعته كـ PDF من المتصفح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_account_data()

    def generate_html_report(self, for_print=False):
        """إنشاء تقرير HTML"""
        from datetime import datetime

        # حساب الإحصائيات
        total_amount = sum(doc['amount'] for doc in self.filtered_data)
        opening_balance = float(self.opening_balance_label.cget('text').split(': ')[1].replace(',', '').replace(' دينار', '')) if ': ' in self.opening_balance_label.cget('text') else 0
        current_balance = opening_balance + total_amount

        # صفوف المستندات (مع رقم التأدية وإصلاح التداخل)
        documents_rows = ""
        for i, doc in enumerate(self.filtered_data, 1):
            # تنظيف البيانات لتجنب التداخل
            doc_number = str(doc['doc_number']).strip()
            voucher_num = str(doc.get('voucher_num', '')).strip()
            amount = f"{doc['amount']:.3f}"
            position = str(doc['position']).strip()

            documents_rows += f"""
                <tr>
                    <td style="text-align: right; padding: 8px; border: 1px solid #ddd;">{doc_number}</td>
                    <td style="text-align: center; padding: 8px; border: 1px solid #ddd;">{voucher_num}</td>
                    <td style="text-align: left; padding: 8px; border: 1px solid #ddd;">{amount}</td>
                    <td style="text-align: center; padding: 8px; border: 1px solid #ddd;">{position}</td>
                </tr>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تفاصيل الحساب: {self.account_name}</title>
            <style>
                body {{ font-family: 'Arial', sans-serif; margin: 20px; background-color: #f8f9fa; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .header h1 {{ color: #2c3e50; margin: 10px 0; }}
                .header h2 {{ color: #34495e; margin: 5px 0; }}
                .header h3 {{ color: #7f8c8d; margin: 5px 0; }}
                .info-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; background: white; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 12px; text-align: right; }}
                .info-table th {{ background-color: #3498db; color: white; font-weight: bold; }}
                .documents-table {{ width: 100%; border-collapse: collapse; background: white; margin-top: 20px; }}
                .documents-table th {{ background-color: #2980b9; color: white; font-weight: bold; padding: 12px; border: 1px solid #ddd; text-align: center; }}
                .documents-table td {{ padding: 8px; border: 1px solid #ddd; }}
                .documents-table tr:nth-child(even) {{ background-color: #f8f9fa; }}
                .documents-table tr:hover {{ background-color: #e3f2fd; }}
                .documents-table tbody tr td:first-child {{ text-align: right; font-weight: bold; }}
                .documents-table tbody tr td:nth-child(2) {{ text-align: center; }}
                .documents-table tbody tr td:nth-child(3) {{ text-align: left; font-weight: bold; color: #1976d2; }}
                .documents-table tbody tr td:last-child {{ text-align: center; font-family: monospace; }}
                @media print {{
                    body {{ margin: 0; background: white; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🇯🇴 المملكة الأردنية الهاشمية</h1>
                <h2>🏥 وزارة الصحة</h2>
                <h3>📄 تفاصيل الحساب: {self.account_name}</h3>
                <p>📅 تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M')}</p>
            </div>

            <table class="info-table">
                <tr><th>🔢 رقم الحساب</th><td>{self.account_num}</td></tr>
                <tr><th>💼 اسم الحساب</th><td>{self.account_name}</td></tr>
                <tr><th>💰 الرصيد الافتتاحي</th><td>{opening_balance:,.3f} دينار</td></tr>
                <tr><th>💵 الرصيد الحالي</th><td>{current_balance:,.3f} دينار</td></tr>
                <tr><th>📁 عدد المستندات</th><td>{len(self.filtered_data)}</td></tr>
                <tr><th>📊 إجمالي المبالغ</th><td>{total_amount:,.3f} دينار</td></tr>
            </table>

            <h3>📁 قائمة المستندات:</h3>
            <table class="documents-table" style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <thead>
                    <tr style="background-color: #2980b9; color: white;">
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">🔢 رقم المستند</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">📝 رقم التأدية</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">💰 المبلغ</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">📍 الموقع</th>
                    </tr>
                </thead>
                <tbody>
                    {documents_rows}
                </tbody>
            </table>

            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()" style="padding: 10px 20px; font-size: 16px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">🖨️ طباعة</button>
                <button onclick="window.close()" style="padding: 10px 20px; font-size: 16px; background: #e74c3c; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">❌ إغلاق</button>
            </div>
        </body>
        </html>
        """

        if for_print:
            import tempfile
            import webbrowser
            import os

            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            webbrowser.open(f'file://{os.path.abspath(temp_file)}')

        return html_content

    def load_account_data(self):
        """تحميل بيانات الحساب مع معالجة شاملة للأخطاء"""
        try:
            print(f"🔍 بدء تحميل بيانات الحساب: {self.sheet_name}")

            # فحص وجود الورقة
            if not hasattr(self.excel, 'workbook') or self.excel.workbook is None:
                error_msg = "لم يتم تحميل ملف Excel بشكل صحيح"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)
                self.destroy()
                return

            print(f"📊 الأوراق المتاحة: {list(self.excel.workbook.sheetnames)}")

            # البحث عن الورقة بطرق مختلفة
            ws = None
            sheet_found = False

            # محاولة 1: الاسم الكامل
            if self.sheet_name in self.excel.workbook.sheetnames:
                ws = self.excel.workbook[self.sheet_name]
                sheet_found = True
                print(f"✅ تم العثور على الورقة: {self.sheet_name}")

            # محاولة 2: رقم الحساب فقط
            elif str(self.account_num) in self.excel.workbook.sheetnames:
                ws = self.excel.workbook[str(self.account_num)]
                sheet_found = True
                print(f"✅ تم العثور على الورقة برقم الحساب: {self.account_num}")

            # محاولة 3: البحث في جميع الأوراق
            else:
                for sheet_name in self.excel.workbook.sheetnames:
                    if str(self.account_num) in sheet_name or self.account_name in sheet_name:
                        ws = self.excel.workbook[sheet_name]
                        sheet_found = True
                        print(f"✅ تم العثور على الورقة بالبحث: {sheet_name}")
                        break

            if not sheet_found or ws is None:
                error_msg = f"لم يتم العثور على ورقة للحساب '{self.account_name}' ({self.account_num})"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)
                self.destroy()
                return

            # تحميل الرصيد الافتتاحي
            opening_balance = 0
            print("💰 فحص الرصيد الافتتاحي...")

            # فحص عدة خلايا محتملة
            balance_cells = ['A8', 'A9', 'B8', 'B9']
            for cell_ref in balance_cells:
                try:
                    cell = ws[cell_ref]
                    if cell.value and isinstance(cell.value, (int, float)):
                        opening_balance = float(cell.value)
                        print(f"✅ الرصيد الافتتاحي من {cell_ref}: {opening_balance:,.2f}")
                        break
                except (ValueError, TypeError) as e:
                    print(f"⚠️ خطأ في قراءة {cell_ref}: {e}")
                    continue

            # تحديث عرض الرصيد الافتتاحي
            if hasattr(self, 'opening_balance_label'):
                self.opening_balance_label.config(text=f"الرصيد الافتتاحي: {opening_balance:,.2f}")
            else:
                print("⚠️ opening_balance_label غير موجود")

            # حساب الرصيد الحالي وإحصائيات المستندات
            documents_count = 0
            total_amount = 0
            documents_list = []

            print("📄 فحص المستندات بآلية الترحيل المحسنة...")

            # البحث في جميع الجداول والأقسام (آلية محسنة وشاملة)
            doc_columns = ['B', 'D', 'F', 'H', 'J', 'L', 'N']
            amount_columns = ['A', 'C', 'E', 'G', 'I', 'K', 'M']
            section_names = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس', 'السابع']

            # قائمة لحفظ جميع المستندات مع ترتيبها
            all_documents = []

            # البحث في جميع الجداول الموجودة
            table_count = 0
            max_row = ws.max_row

            print(f"🔍 فحص شامل لجميع المستندات في الورقة (حتى الصف {max_row})...")

            # البحث عن جميع الجداول في الصفحة (آلية محسنة)
            current_row = 1
            while current_row <= max_row:
                # البحث عن بداية جدول جديد
                if self.is_table_header_at_row(ws, current_row):
                    table_count += 1
                    table_start_row = current_row + 12  # بيانات الجدول تبدأ بعد 12 صف من العنوان
                    table_end_row = table_start_row + 20  # 21 صف بيانات (0-20)

                    print(f"📊 فحص الجدول {table_count} في الصفوف {table_start_row}-{table_end_row}")

                    # البحث في أقسام الجدول (7 أقسام × 21 صف)
                    for section_idx, (doc_col, amount_col) in enumerate(zip(doc_columns, amount_columns)):
                        print(f"🔍 فحص القسم {section_names[section_idx]} (عمود {doc_col})")

                        # البحث في صفوف القسم
                        for data_row in range(table_start_row, table_end_row + 1):
                            if data_row > max_row:
                                break

                            try:
                                doc_value = ws[f'{doc_col}{data_row}'].value
                                amount_value = ws[f'{amount_col}{data_row}'].value

                                # قراءة رقم التأدية من العمود الثالث (بعد عمود رقم المستند)
                                voucher_col = chr(ord(doc_col) + 1)  # العمود التالي بعد رقم المستند
                                voucher_value = ws[f'{voucher_col}{data_row}'].value

                                # فحص وجود مستند صحيح (فلترة محسنة)
                                doc_str = str(doc_value).strip() if doc_value else ""

                                # قائمة القيم المرفوضة
                                invalid_values = [
                                    '', '0', 'ما قبله', 'None', 'null', '#N/A', '#REF!', '#VALUE!',
                                    'المبلغ', 'رقم المستند', 'اسم الحساب', 'المجموع', 'رقم التأدية'
                                ]

                                if (doc_value and
                                    doc_str and
                                    doc_str not in invalid_values and
                                    doc_str.lower() not in [v.lower() for v in invalid_values] and
                                    not doc_str.startswith('=') and
                                    not doc_str.startswith('#') and
                                    len(doc_str) > 0):

                                    # تحويل المبلغ بأمان
                                    try:
                                        amount = float(amount_value) if amount_value else 0.0
                                    except (ValueError, TypeError):
                                        amount = 0.0

                                    # تجاهل المبالغ الصفرية أو السالبة
                                    if amount <= 0:
                                        continue

                                    # تنظيف رقم التأدية
                                    voucher_num = str(voucher_value).strip() if voucher_value else ''
                                    if voucher_num in invalid_values or voucher_num.startswith('='):
                                        voucher_num = ''

                                    document_data = {
                                        'doc_number': str(doc_value).strip(),
                                        'voucher_num': voucher_num,  # رقم التأدية
                                        'amount': amount,
                                        'section': section_names[section_idx],
                                        'table_number': table_count,
                                        'position': f'{amount_col}{data_row}',
                                        'row_number': data_row,  # للترتيب
                                        'section_index': section_idx  # للترتيب
                                    }

                                    all_documents.append(document_data)
                                    documents_count += 1
                                    total_amount += amount

                                    print(f"✅ مستند: {doc_value} - مبلغ: {amount} - جدول: {table_count} - قسم: {section_names[section_idx]} - موقع: {amount_col}{data_row}")

                            except Exception as e:
                                print(f"❌ خطأ في قراءة الخلية {amount_col}{data_row}: {str(e)}")
                                continue

                    # الانتقال إلى البحث عن الجدول التالي
                    current_row = table_end_row + 10
                else:
                    current_row += 1

            # ترتيب المستندات حسب ترتيبها في الصفحة (جدول، ثم صف، ثم قسم)
            all_documents.sort(key=lambda x: (x['table_number'], x['row_number'], x['section_index']))

            # تحويل إلى التنسيق المطلوب للعرض
            for document_data in all_documents:
                documents_list.append((
                    document_data['amount'],
                    document_data['doc_number'],
                    '',  # رقم التأدية (غير متوفر في هذا النظام)
                    f"جدول {document_data['table_number']} - {document_data['section']}"
                ))

            # الرصيد الحالي
            current_balance = opening_balance + total_amount
            print(f"💰 الرصيد الحالي: {current_balance:,.2f}")

            # تحديث عرض البيانات
            if hasattr(self, 'current_balance_label'):
                self.current_balance_label.config(text=f"الرصيد الحالي: {current_balance:,.2f}")
            else:
                print("⚠️ current_balance_label غير موجود")

            # إحصائيات المستندات
            if hasattr(self, 'documents_count_label'):
                self.documents_count_label.config(text=f"عدد المستندات: {documents_count}")
            else:
                print("⚠️ documents_count_label غير موجود")

            if hasattr(self, 'total_amount_label'):
                self.total_amount_label.config(text=f"إجمالي المبالغ: {total_amount:,.2f}")
            else:
                print("⚠️ total_amount_label غير موجود")

            # حفظ البيانات المرتبة
            self.account_data = all_documents
            self.filtered_data = all_documents.copy()

            # تحديث عرض المستندات
            if hasattr(self, 'update_documents_display'):
                self.update_documents_display()
            else:
                # عرض بسيط للمستندات (في حالة عدم وجود الدالة الجديدة)
                if hasattr(self, 'documents_tree'):
                    for item in self.documents_tree.get_children():
                        self.documents_tree.delete(item)

                    for doc in all_documents:
                        self.documents_tree.insert('', 'end', values=(
                            doc['doc_number'],
                            doc.get('voucher_num', ''),  # رقم التأدية
                            f"{doc['amount']:.3f}",
                            doc['position']
                        ))

                    print(f"✅ تم تحميل {len(all_documents)} مستند في الجدول")
                else:
                    print("⚠️ documents_tree غير موجود")

            print(f"✅ تم تحميل بيانات الحساب بنجاح")
            print(f"📄 إجمالي المستندات: {documents_count}")
            print(f"💵 إجمالي المبالغ: {total_amount:,.2f}")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء تحميل بيانات الحساب: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"📄 تفاصيل الخطأ:")
            import traceback
            print(traceback.format_exc())
            messagebox.showerror("خطأ", error_msg)

    def is_table_header_at_row(self, ws, row):
        """فحص وجود عنوان جدول في الصف المحدد"""
        try:
            # فحص وجود عنوان الحساب في الخلية A
            cell_value = ws[f'A{row}'].value
            if cell_value:
                cell_str = str(cell_value).strip()
                # البحث عن عبارات تدل على بداية جدول
                table_indicators = ['حساب', 'اسم الحساب', 'رقم الحساب']
                for indicator in table_indicators:
                    if indicator in cell_str:
                        return True

                # فحص إضافي: إذا كانت الخلية تحتوي على رقم متبوع بنص
                if len(cell_str) > 3 and any(char.isdigit() for char in cell_str) and any(char.isalpha() for char in cell_str):
                    return True

            return False
        except:
            return False

    def center_window(self):
        """توسيط النافذة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def _is_table_header_at_row(self, ws, row):
        """فحص وجود عناوين جدول في صف محدد"""
        try:
            # فحص وجود عناوين الجدول في الصف المحدد
            # عادة تكون العناوين في الصف 5 للجدول الأول

            # فحص الخلايا الرئيسية للعناوين
            headers_to_check = [
                (row, 1),   # A - المبلغ
                (row, 4),   # D - المبلغ
                (row, 7),   # G - المبلغ
            ]

            # فحص وجود عناوين في المواقع المتوقعة
            for header_row, header_col in headers_to_check:
                cell_value = ws.cell(row=header_row, column=header_col).value
                if cell_value and ("المبلغ" in str(cell_value) or "القيمة" in str(cell_value)):
                    return True

            # فحص بديل - فحص وجود أي محتوى في الصف
            for col in range(1, 19):  # A إلى R
                cell_value = ws.cell(row=row, column=col).value
                if cell_value and str(cell_value).strip():
                    # إذا وجد محتوى في هذا الصف، فهو عنوان جدول
                    return True

            return False

        except Exception as e:
            print(f"⚠️ خطأ في فحص عناوين الجدول: {e}")
            return False

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def export_details(self):
        """تصدير تفاصيل الحساب إلى ملف Excel"""
        try:
            from tkinter import filedialog
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from datetime import datetime

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ تفاصيل الحساب",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"تفاصيل_الحساب_{self.account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                return

            # إنشاء ملف Excel جديد
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = f"تفاصيل {self.account_name}"
            ws.sheet_properties.rightToLeft = True

            # الترويسة
            ws.merge_cells('A1:F1')
            ws['A1'] = "المملكة الأردنية الهاشمية - وزارة الصحة"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A2:F2')
            ws['A2'] = f"تفاصيل الحساب: {self.account_name}"
            ws['A2'].font = Font(bold=True, size=12)
            ws['A2'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A3:F3')
            ws['A3'] = f"تاريخ التصدير: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
            ws['A3'].font = Font(size=10)
            ws['A3'].alignment = Alignment(horizontal='center')

            # معلومات الحساب
            row = 5
            ws[f'A{row}'] = "رقم الحساب:"
            ws[f'B{row}'] = self.account_num
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "اسم الحساب:"
            ws[f'B{row}'] = self.account_name
            ws[f'A{row}'].font = Font(bold=True)

            # الحصول على البيانات من النافذة الحالية
            opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
            current_balance = self.current_balance_label.cget("text").split(": ")[1]
            documents_count = self.documents_count_label.cget("text").split(": ")[1]
            total_amount = self.total_amount_label.cget("text").split(": ")[1]

            row += 1
            ws[f'A{row}'] = "الرصيد الافتتاحي:"
            ws[f'B{row}'] = opening_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "الرصيد الحالي:"
            ws[f'B{row}'] = current_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "عدد المستندات:"
            ws[f'B{row}'] = documents_count
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "إجمالي المبالغ:"
            ws[f'B{row}'] = total_amount
            ws[f'A{row}'].font = Font(bold=True)

            # عناوين جدول المستندات
            row += 3
            headers = ['المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col)
                cell.value = header
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(all=Side(style='thin'))

            # بيانات المستندات
            for item in self.documents_tree.get_children():
                row += 1
                values = self.documents_tree.item(item)['values']
                for col, value in enumerate(values, 1):
                    cell = ws.cell(row=row, column=col)
                    cell.value = value
                    cell.border = Border(all=Side(style='thin'))
                    cell.alignment = Alignment(horizontal='center')
                    if col == 1:  # عمود المبلغ
                        cell.number_format = '#,##0.00'

            # تنسيق الأعمدة
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 20
            ws.column_dimensions['C'].width = 15
            ws.column_dimensions['D'].width = 15

            # حفظ الملف
            wb.save(filename)
            messagebox.showinfo("نجاح", f"تم تصدير تفاصيل الحساب إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def print_report(self):
        """طباعة تقرير الحساب"""
        try:
            import tempfile
            import os
            from datetime import datetime

            # إنشاء ملف HTML للطباعة
            html_content = self.generate_print_html()

            # حفظ الملف المؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            import webbrowser
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("طباعة", "تم فتح التقرير في المتصفح للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إعداد الطباعة: {str(e)}")

    def generate_print_html(self):
        """إنشاء محتوى HTML للطباعة"""
        from datetime import datetime

        # الحصول على البيانات
        opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
        current_balance = self.current_balance_label.cget("text").split(": ")[1]
        documents_count = self.documents_count_label.cget("text").split(": ")[1]
        total_amount = self.total_amount_label.cget("text").split(": ")[1]

        # بناء جدول المستندات
        documents_rows = ""
        for item in self.documents_tree.get_children():
            values = self.documents_tree.item(item)['values']
            documents_rows += f"""
            <tr>
                <td>{values[0]}</td>
                <td>{values[1]}</td>
                <td>{values[2]}</td>
                <td>{values[3]}</td>
            </tr>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تفاصيل الحساب - {self.account_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .info-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .info-table th {{ background-color: #f2f2f2; font-weight: bold; }}
                .documents-table {{ width: 100%; border-collapse: collapse; }}
                .documents-table th, .documents-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .documents-table th {{ background-color: #366092; color: white; }}
                @media print {{
                    body {{ margin: 0; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>المملكة الأردنية الهاشمية</h1>
                <h2>وزارة الصحة</h2>
                <h3>تفاصيل الحساب: {self.account_name}</h3>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M')}</p>
            </div>

            <table class="info-table">
                <tr><th>رقم الحساب</th><td>{self.account_num}</td></tr>
                <tr><th>اسم الحساب</th><td>{self.account_name}</td></tr>
                <tr><th>الرصيد الافتتاحي</th><td>{opening_balance}</td></tr>
                <tr><th>الرصيد الحالي</th><td>{current_balance}</td></tr>
                <tr><th>عدد المستندات</th><td>{documents_count}</td></tr>
                <tr><th>إجمالي المبالغ</th><td>{total_amount}</td></tr>
            </table>

            <h3>المستندات:</h3>
            <table class="documents-table">
                <thead>
                    <tr>
                        <th>المبلغ</th>
                        <th>رقم المستند</th>
                        <th>رقم التأدية</th>
                        <th>القسم</th>
                    </tr>
                </thead>
                <tbody>
                    {documents_rows}
                </tbody>
            </table>

            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
        """

        return html_content
