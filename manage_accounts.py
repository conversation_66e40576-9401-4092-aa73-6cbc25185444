import tkinter as tk
from tkinter import ttk, messagebox

class ManageAccountsDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إدارة الحسابات")
        self.excel = excel

        # تكوين النافذة
        self.geometry("800x500")
        self.configure(bg='#f0f0f0')

        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # قائمة الحسابات
        self.create_accounts_list(main_frame)

        # أزرار التحكم
        self.create_control_buttons(main_frame)

        # تحديث القائمة
        self.load_accounts()

    def create_accounts_list(self, parent):
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="الحسابات", padding="5")
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)

        # إنشاء جدول الحسابات
        columns = ('رقم الحساب', 'اسم الحساب', 'الرصيد')
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show='headings')

        # تعيين العناوين
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=150)

        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)

        # وضع العناصر في الإطار
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # تمكين التمدد
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

    def create_control_buttons(self, parent):
        # إطار الأزرار
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=1, column=0, pady=10)

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(pady=5)

        ttk.Button(row1_frame, text="عرض تفاصيل الحساب",
                  command=self.view_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تصدير تفاصيل الحساب",
                  command=self.export_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تعديل الحساب",
                  command=self.edit_account).pack(side=tk.LEFT, padx=5)

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(pady=5)

        ttk.Button(row2_frame, text="حذف الحساب",
                  command=self.delete_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="تحديث القائمة",
                  command=self.load_accounts).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="إغلاق",
                  command=self.destroy).pack(side=tk.LEFT, padx=5)

    def load_accounts(self):
        """تحميل الحسابات في الجدول"""
        # مسح الجدول
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # تحميل الحسابات
        for sheet_name in self.excel.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                try:
                    # استخراج رقم واسم الحساب
                    account_num, account_name = sheet_name.split('-', 1)

                    # الحصول على الرصيد
                    ws = self.excel.workbook[sheet_name]
                    balance = ws['A33'].value or 0

                    # إضافة الصف
                    self.accounts_tree.insert('', tk.END, values=(account_num, account_name, balance))
                except:
                    continue

    def view_account_details(self):
        """عرض تفاصيل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لعرض تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل
        details_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        self.wait_window(details_dialog)

    def export_account_details(self):
        """تصدير تفاصيل الحساب المحدد مباشرة"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتصدير تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل مؤقتاً للحصول على البيانات
        temp_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        temp_dialog.withdraw()  # إخفاء النافذة

        # تصدير التفاصيل مباشرة
        temp_dialog.export_details()

        # إغلاق النافذة المؤقتة
        temp_dialog.destroy()

    def edit_account(self):
        """تعديل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتعديله")
            return

        item = selection[0]
        account_num, account_name, _ = self.accounts_tree.item(item)['values']

        # إنشاء نافذة التعديل
        dialog = AccountEditDialog(self, self.excel, account_num, account_name)
        self.wait_window(dialog)

        # تحديث القائمة
        self.load_accounts()

    def delete_account(self):
        """حذف الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لحذفه")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # نافذة تأكيد بسيطة وموثوقة
        confirm_message = (f"هل أنت متأكد من حذف الحساب؟\n\n"
                          f"رقم الحساب: {account_num}\n"
                          f"اسم الحساب: {account_name}\n"
                          f"الرصيد الحالي: {balance}\n\n"
                          f"تحذير: سيتم حذف الحساب نهائياً مع جميع المستندات!")

        if messagebox.askyesno("تأكيد حذف الحساب", confirm_message):
            try:
                print(f"🗑️ حذف الحساب: {sheet_name}")  # للتشخيص

                # التحقق من وجود الحساب
                if sheet_name not in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", f"الحساب '{sheet_name}' غير موجود")
                    return

                # حذف الصفحة
                self.excel.workbook.remove(self.excel.workbook[sheet_name])
                print(f"✅ تم حذف الصفحة من الملف")  # للتشخيص

                # تحديث التقرير الإجمالي تلقائياً
                print(f"📊 تحديث التقرير الإجمالي بعد حذف الحساب...")
                self.excel.create_summary_report()

                # حفظ التغييرات
                if self.excel.save_workbook():
                    print(f"✅ تم حفظ الملف بنجاح")  # للتشخيص

                    # تحديث القائمة
                    self.load_accounts()

                    messagebox.showinfo("نجاح", f"تم حذف الحساب '{account_name}' بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء حذف الحساب: {str(e)}"
                print(f"❌ {error_msg}")  # للتشخيص
                messagebox.showerror("خطأ", error_msg)

class AccountEditDialog(tk.Toplevel):
    def __init__(self, parent, excel, account_num, account_name):
        super().__init__(parent)
        self.title("تعديل الحساب")
        self.excel = excel
        self.old_sheet_name = f"{account_num}-{account_name}"

        # تكوين النافذة
        self.geometry("400x200")

        # حقول الإدخال
        ttk.Label(self, text="رقم الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_num = ttk.Entry(self)
        self.account_num.insert(0, account_num)
        self.account_num.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(self, text="اسم الحساب:").grid(row=1, column=0, padx=5, pady=5)
        self.account_name = ttk.Entry(self)
        self.account_name.insert(0, account_name)
        self.account_name.grid(row=1, column=1, padx=5, pady=5)

        # أزرار
        ttk.Button(self, text="حفظ",
                  command=self.save_changes).grid(row=2, column=0, columnspan=2, pady=20)

    def save_changes(self):
        """حفظ التغييرات على الحساب"""
        try:
            new_sheet_name = f"{self.account_num.get()}-{self.account_name.get()}"

            if new_sheet_name != self.old_sheet_name:
                # التحقق من عدم وجود حساب بنفس الاسم
                if new_sheet_name in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", "يوجد حساب بنفس الرقم والاسم")
                    return

                # تغيير اسم الصفحة
                sheet = self.excel.workbook[self.old_sheet_name]
                sheet.title = new_sheet_name

                # حفظ التغييرات
                if self.excel.save_workbook():
                    messagebox.showinfo("نجاح", "تم تعديل الحساب بنجاح")
                    self.destroy()
            else:
                self.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل الحساب: {str(e)}")

class DeleteConfirmDialog(tk.Toplevel):
    """نافذة تأكيد حذف الحساب"""
    def __init__(self, parent, account_num, account_name, balance, sheet_name):
        super().__init__(parent)
        self.title("تأكيد حذف الحساب")
        self.account_num = account_num
        self.account_name = account_name
        self.balance = balance
        self.sheet_name = sheet_name
        self.confirmed = False

        # تكوين النافذة
        self.geometry("500x300")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة تحذير
        warning_label = tk.Label(main_frame, text="⚠️", font=("Arial", 48),
                                bg='#f0f0f0', fg='#ff6b6b')
        warning_label.pack(pady=(0, 20))

        # عنوان التحذير
        title_label = tk.Label(main_frame, text="تحذير: حذف الحساب",
                              font=("Arial", 16, "bold"),
                              bg='#f0f0f0', fg='#d63031')
        title_label.pack(pady=(0, 10))

        # معلومات الحساب
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(info_frame, text="معلومات الحساب المراد حذفه:",
                font=("Arial", 12, "bold"), bg='#ffffff').pack(pady=5)

        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"الرصيد الحالي: {self.balance}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10, pady=(0, 5))

        # رسالة التحذير
        warning_text = ("سيتم حذف الحساب نهائياً مع جميع المستندات والبيانات المرتبطة به.\n"
                       "هذا الإجراء لا يمكن التراجع عنه!")
        warning_label = tk.Label(main_frame, text=warning_text,
                                font=("Arial", 11), bg='#f0f0f0', fg='#d63031',
                                wraplength=400, justify=tk.CENTER)
        warning_label.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack()

        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="حذف الحساب",
                              command=self.confirm_delete,
                              bg='#d63031', fg='white',
                              font=("Arial", 11, "bold"),
                              padx=20, pady=5)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              command=self.cancel_delete,
                              bg='#74b9ff', fg='white',
                              font=("Arial", 11),
                              padx=20, pady=5)
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفتاح Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.cancel_delete())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def confirm_delete(self):
        """تأكيد الحذف"""
        self.confirmed = True
        self.destroy()

    def cancel_delete(self):
        """إلغاء الحذف"""
        self.confirmed = False
        self.destroy()

class AccountDetailsDialog(tk.Toplevel):
    """نافذة عرض تفاصيل الحساب"""
    def __init__(self, parent, excel, sheet_name, account_num, account_name):
        super().__init__(parent)
        self.title(f"تفاصيل الحساب: {account_name}")
        self.excel = excel
        self.sheet_name = sheet_name
        self.account_num = account_num
        self.account_name = account_name

        # تكوين النافذة
        self.geometry("700x600")  # زيادة الارتفاع لإظهار الأزرار بوضوح
        self.configure(bg='#f0f0f0')

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.load_account_data()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الحساب الأساسية
        info_frame = tk.LabelFrame(main_frame, text="معلومات الحساب",
                                  font=("Arial", 12, "bold"), bg='#f0f0f0')
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # رقم الحساب
        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # اسم الحساب
        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الافتتاحي
        self.opening_balance_label = tk.Label(info_frame, text="الرصيد الافتتاحي: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.opening_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الحالي
        self.current_balance_label = tk.Label(info_frame, text="الرصيد الحالي: جاري التحميل...",
                                            font=("Arial", 11, "bold"), bg='#f0f0f0')
        self.current_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # إحصائيات المستندات
        stats_frame = tk.LabelFrame(main_frame, text="إحصائيات المستندات",
                                   font=("Arial", 12, "bold"), bg='#f0f0f0')
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        self.documents_count_label = tk.Label(stats_frame, text="عدد المستندات: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.documents_count_label.pack(anchor=tk.W, padx=10, pady=5)

        self.total_amount_label = tk.Label(stats_frame, text="إجمالي المبالغ: جاري التحميل...",
                                         font=("Arial", 11), bg='#f0f0f0')
        self.total_amount_label.pack(anchor=tk.W, padx=10, pady=5)

        # قائمة المستندات
        documents_frame = tk.LabelFrame(main_frame, text="المستندات الأخيرة",
                                       font=("Arial", 12, "bold"), bg='#f0f0f0')
        documents_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # جدول المستندات
        columns = ('المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم')
        self.documents_tree = ttk.Treeview(documents_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.documents_tree.heading(col, text=col)
            self.documents_tree.column(col, width=120)

        # شريط التمرير للمستندات
        docs_scrollbar = ttk.Scrollbar(documents_frame, orient=tk.VERTICAL, command=self.documents_tree.yview)
        self.documents_tree.configure(yscrollcommand=docs_scrollbar.set)

        self.documents_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        docs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=10)

        # زر تصدير التفاصيل
        export_btn = tk.Button(buttons_frame, text="تصدير التفاصيل",
                              command=self.export_details,
                              bg='#00b894', fg='white', font=("Arial", 11),
                              padx=20, pady=5)
        export_btn.pack(side=tk.LEFT, padx=5)

        # زر طباعة التقرير
        print_btn = tk.Button(buttons_frame, text="طباعة التقرير",
                             command=self.print_report,
                             bg='#6c5ce7', fg='white', font=("Arial", 11),
                             padx=20, pady=5)
        print_btn.pack(side=tk.LEFT, padx=5)

        # زر الإغلاق
        close_btn = tk.Button(buttons_frame, text="إغلاق", command=self.destroy,
                             bg='#74b9ff', fg='white', font=("Arial", 11),
                             padx=20, pady=5)
        close_btn.pack(side=tk.LEFT, padx=5)

    def load_account_data(self):
        """تحميل بيانات الحساب مع معالجة شاملة للأخطاء"""
        try:
            print(f"🔍 بدء تحميل بيانات الحساب: {self.sheet_name}")

            # فحص وجود الورقة
            if not hasattr(self.excel, 'workbook') or self.excel.workbook is None:
                error_msg = "لم يتم تحميل ملف Excel بشكل صحيح"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)
                self.destroy()
                return

            print(f"📊 الأوراق المتاحة: {list(self.excel.workbook.sheetnames)}")

            # البحث عن الورقة بطرق مختلفة
            ws = None
            sheet_found = False

            # محاولة 1: الاسم الكامل
            if self.sheet_name in self.excel.workbook.sheetnames:
                ws = self.excel.workbook[self.sheet_name]
                sheet_found = True
                print(f"✅ تم العثور على الورقة: {self.sheet_name}")

            # محاولة 2: رقم الحساب فقط
            elif str(self.account_num) in self.excel.workbook.sheetnames:
                ws = self.excel.workbook[str(self.account_num)]
                sheet_found = True
                print(f"✅ تم العثور على الورقة برقم الحساب: {self.account_num}")

            # محاولة 3: البحث في جميع الأوراق
            else:
                for sheet_name in self.excel.workbook.sheetnames:
                    if str(self.account_num) in sheet_name or self.account_name in sheet_name:
                        ws = self.excel.workbook[sheet_name]
                        sheet_found = True
                        print(f"✅ تم العثور على الورقة بالبحث: {sheet_name}")
                        break

            if not sheet_found or ws is None:
                error_msg = f"لم يتم العثور على ورقة للحساب '{self.account_name}' ({self.account_num})"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)
                self.destroy()
                return

            # تحميل الرصيد الافتتاحي
            opening_balance = 0
            print("💰 فحص الرصيد الافتتاحي...")

            # فحص عدة خلايا محتملة
            balance_cells = ['A8', 'A9', 'B8', 'B9']
            for cell_ref in balance_cells:
                try:
                    cell = ws[cell_ref]
                    if cell.value and isinstance(cell.value, (int, float)):
                        opening_balance = float(cell.value)
                        print(f"✅ الرصيد الافتتاحي من {cell_ref}: {opening_balance:,.2f}")
                        break
                except (ValueError, TypeError) as e:
                    print(f"⚠️ خطأ في قراءة {cell_ref}: {e}")
                    continue

            # تحديث عرض الرصيد الافتتاحي
            if hasattr(self, 'opening_balance_label'):
                self.opening_balance_label.config(text=f"الرصيد الافتتاحي: {opening_balance:,.2f}")
            else:
                print("⚠️ opening_balance_label غير موجود")

            # حساب الرصيد الحالي وإحصائيات المستندات
            documents_count = 0
            total_amount = 0
            documents_list = []

            print("📄 فحص المستندات بآلية الترحيل المحسنة...")

            # البحث في جميع الجداول والأقسام (آلية محسنة وشاملة)
            doc_columns = ['B', 'D', 'F', 'H', 'J', 'L', 'N']
            amount_columns = ['A', 'C', 'E', 'G', 'I', 'K', 'M']
            section_names = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس', 'السابع']

            # قائمة لحفظ جميع المستندات مع ترتيبها
            all_documents = []

            # البحث في جميع الجداول الموجودة
            table_count = 0
            max_row = ws.max_row

            print(f"🔍 فحص شامل لجميع المستندات في الورقة (حتى الصف {max_row})...")

            # البحث عن جميع الجداول في الصفحة (آلية محسنة)
            current_row = 1
            while current_row <= max_row:
                # البحث عن بداية جدول جديد
                if self.is_table_header_at_row(ws, current_row):
                    table_count += 1
                    table_start_row = current_row + 12  # بيانات الجدول تبدأ بعد 12 صف من العنوان
                    table_end_row = table_start_row + 20  # 21 صف بيانات (0-20)

                    print(f"📊 فحص الجدول {table_count} في الصفوف {table_start_row}-{table_end_row}")

                    # البحث في أقسام الجدول (7 أقسام × 21 صف)
                    for section_idx, (doc_col, amount_col) in enumerate(zip(doc_columns, amount_columns)):
                        print(f"🔍 فحص القسم {section_names[section_idx]} (عمود {doc_col})")

                        # البحث في صفوف القسم
                        for data_row in range(table_start_row, table_end_row + 1):
                            if data_row > max_row:
                                break

                            try:
                                doc_value = ws[f'{doc_col}{data_row}'].value
                                amount_value = ws[f'{amount_col}{data_row}'].value

                                # فحص وجود مستند صحيح (فلترة محسنة)
                                doc_str = str(doc_value).strip() if doc_value else ""

                                # قائمة القيم المرفوضة
                                invalid_values = [
                                    '', '0', 'ما قبله', 'None', 'null', '#N/A', '#REF!', '#VALUE!',
                                    'المبلغ', 'رقم المستند', 'اسم الحساب', 'المجموع'
                                ]

                                if (doc_value and
                                    doc_str and
                                    doc_str not in invalid_values and
                                    doc_str.lower() not in [v.lower() for v in invalid_values] and
                                    not doc_str.startswith('=') and
                                    not doc_str.startswith('#') and
                                    len(doc_str) > 0):

                                    # تحويل المبلغ بأمان
                                    try:
                                        amount = float(amount_value) if amount_value else 0.0
                                    except (ValueError, TypeError):
                                        amount = 0.0

                                    # تجاهل المبالغ الصفرية أو السالبة
                                    if amount <= 0:
                                        continue

                                    document_data = {
                                        'doc_number': str(doc_value).strip(),
                                        'amount': amount,
                                        'section': section_names[section_idx],
                                        'table_number': table_count,
                                        'position': f'{amount_col}{data_row}',
                                        'row_number': data_row,  # للترتيب
                                        'section_index': section_idx  # للترتيب
                                    }

                                    all_documents.append(document_data)
                                    documents_count += 1
                                    total_amount += amount

                                    print(f"✅ مستند: {doc_value} - مبلغ: {amount} - جدول: {table_count} - قسم: {section_names[section_idx]} - موقع: {amount_col}{data_row}")

                            except Exception as e:
                                print(f"❌ خطأ في قراءة الخلية {amount_col}{data_row}: {str(e)}")
                                continue

                    # الانتقال إلى البحث عن الجدول التالي
                    current_row = table_end_row + 10
                else:
                    current_row += 1

            # ترتيب المستندات حسب ترتيبها في الصفحة (جدول، ثم صف، ثم قسم)
            all_documents.sort(key=lambda x: (x['table_number'], x['row_number'], x['section_index']))

            # تحويل إلى التنسيق المطلوب للعرض
            for document_data in all_documents:
                documents_list.append((
                    document_data['amount'],
                    document_data['doc_number'],
                    '',  # رقم التأدية (غير متوفر في هذا النظام)
                    f"جدول {document_data['table_number']} - {document_data['section']}"
                ))

            # الرصيد الحالي
            current_balance = opening_balance + total_amount
            print(f"💰 الرصيد الحالي: {current_balance:,.2f}")

            # تحديث عرض البيانات
            if hasattr(self, 'current_balance_label'):
                self.current_balance_label.config(text=f"الرصيد الحالي: {current_balance:,.2f}")
            else:
                print("⚠️ current_balance_label غير موجود")

            # إحصائيات المستندات
            if hasattr(self, 'documents_count_label'):
                self.documents_count_label.config(text=f"عدد المستندات: {documents_count}")
            else:
                print("⚠️ documents_count_label غير موجود")

            if hasattr(self, 'total_amount_label'):
                self.total_amount_label.config(text=f"إجمالي المبالغ: {total_amount:,.2f}")
            else:
                print("⚠️ total_amount_label غير موجود")

            # تحميل المستندات في الجدول
            if hasattr(self, 'documents_tree'):
                # مسح الجدول أولاً
                for item in self.documents_tree.get_children():
                    self.documents_tree.delete(item)

                # إضافة آخر 20 مستند
                for doc in documents_list[-20:]:
                    self.documents_tree.insert('', tk.END, values=doc)

                print(f"✅ تم تحميل {min(20, len(documents_list))} مستند في الجدول")
            else:
                print("⚠️ documents_tree غير موجود")

            print(f"✅ تم تحميل بيانات الحساب بنجاح")
            print(f"📄 إجمالي المستندات: {documents_count}")
            print(f"💵 إجمالي المبالغ: {total_amount:,.2f}")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء تحميل بيانات الحساب: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"📄 تفاصيل الخطأ:")
            import traceback
            print(traceback.format_exc())
            messagebox.showerror("خطأ", error_msg)

    def is_table_header_at_row(self, ws, row):
        """فحص وجود عنوان جدول في الصف المحدد"""
        try:
            # فحص وجود عنوان الحساب في الخلية A
            cell_value = ws[f'A{row}'].value
            if cell_value:
                cell_str = str(cell_value).strip()
                # البحث عن عبارات تدل على بداية جدول
                table_indicators = ['حساب', 'اسم الحساب', 'رقم الحساب']
                for indicator in table_indicators:
                    if indicator in cell_str:
                        return True

                # فحص إضافي: إذا كانت الخلية تحتوي على رقم متبوع بنص
                if len(cell_str) > 3 and any(char.isdigit() for char in cell_str) and any(char.isalpha() for char in cell_str):
                    return True

            return False
        except:
            return False

    def _is_table_header_at_row(self, ws, row):
        """فحص وجود عناوين جدول في صف محدد"""
        try:
            # فحص وجود عناوين الجدول في الصف المحدد
            # عادة تكون العناوين في الصف 5 للجدول الأول

            # فحص الخلايا الرئيسية للعناوين
            headers_to_check = [
                (row, 1),   # A - المبلغ
                (row, 4),   # D - المبلغ
                (row, 7),   # G - المبلغ
            ]

            # فحص وجود عناوين في المواقع المتوقعة
            for header_row, header_col in headers_to_check:
                cell_value = ws.cell(row=header_row, column=header_col).value
                if cell_value and ("المبلغ" in str(cell_value) or "القيمة" in str(cell_value)):
                    return True

            # فحص بديل - فحص وجود أي محتوى في الصف
            for col in range(1, 19):  # A إلى R
                cell_value = ws.cell(row=row, column=col).value
                if cell_value and str(cell_value).strip():
                    # إذا وجد محتوى في هذا الصف، فهو عنوان جدول
                    return True

            return False

        except Exception as e:
            print(f"⚠️ خطأ في فحص عناوين الجدول: {e}")
            return False

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def export_details(self):
        """تصدير تفاصيل الحساب إلى ملف Excel"""
        try:
            from tkinter import filedialog
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from datetime import datetime

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ تفاصيل الحساب",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"تفاصيل_الحساب_{self.account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                return

            # إنشاء ملف Excel جديد
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = f"تفاصيل {self.account_name}"
            ws.sheet_properties.rightToLeft = True

            # الترويسة
            ws.merge_cells('A1:F1')
            ws['A1'] = "المملكة الأردنية الهاشمية - وزارة الصحة"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A2:F2')
            ws['A2'] = f"تفاصيل الحساب: {self.account_name}"
            ws['A2'].font = Font(bold=True, size=12)
            ws['A2'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A3:F3')
            ws['A3'] = f"تاريخ التصدير: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
            ws['A3'].font = Font(size=10)
            ws['A3'].alignment = Alignment(horizontal='center')

            # معلومات الحساب
            row = 5
            ws[f'A{row}'] = "رقم الحساب:"
            ws[f'B{row}'] = self.account_num
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "اسم الحساب:"
            ws[f'B{row}'] = self.account_name
            ws[f'A{row}'].font = Font(bold=True)

            # الحصول على البيانات من النافذة الحالية
            opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
            current_balance = self.current_balance_label.cget("text").split(": ")[1]
            documents_count = self.documents_count_label.cget("text").split(": ")[1]
            total_amount = self.total_amount_label.cget("text").split(": ")[1]

            row += 1
            ws[f'A{row}'] = "الرصيد الافتتاحي:"
            ws[f'B{row}'] = opening_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "الرصيد الحالي:"
            ws[f'B{row}'] = current_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "عدد المستندات:"
            ws[f'B{row}'] = documents_count
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "إجمالي المبالغ:"
            ws[f'B{row}'] = total_amount
            ws[f'A{row}'].font = Font(bold=True)

            # عناوين جدول المستندات
            row += 3
            headers = ['المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col)
                cell.value = header
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(all=Side(style='thin'))

            # بيانات المستندات
            for item in self.documents_tree.get_children():
                row += 1
                values = self.documents_tree.item(item)['values']
                for col, value in enumerate(values, 1):
                    cell = ws.cell(row=row, column=col)
                    cell.value = value
                    cell.border = Border(all=Side(style='thin'))
                    cell.alignment = Alignment(horizontal='center')
                    if col == 1:  # عمود المبلغ
                        cell.number_format = '#,##0.00'

            # تنسيق الأعمدة
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 20
            ws.column_dimensions['C'].width = 15
            ws.column_dimensions['D'].width = 15

            # حفظ الملف
            wb.save(filename)
            messagebox.showinfo("نجاح", f"تم تصدير تفاصيل الحساب إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def print_report(self):
        """طباعة تقرير الحساب"""
        try:
            import tempfile
            import os
            from datetime import datetime

            # إنشاء ملف HTML للطباعة
            html_content = self.generate_print_html()

            # حفظ الملف المؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            import webbrowser
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("طباعة", "تم فتح التقرير في المتصفح للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إعداد الطباعة: {str(e)}")

    def generate_print_html(self):
        """إنشاء محتوى HTML للطباعة"""
        from datetime import datetime

        # الحصول على البيانات
        opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
        current_balance = self.current_balance_label.cget("text").split(": ")[1]
        documents_count = self.documents_count_label.cget("text").split(": ")[1]
        total_amount = self.total_amount_label.cget("text").split(": ")[1]

        # بناء جدول المستندات
        documents_rows = ""
        for item in self.documents_tree.get_children():
            values = self.documents_tree.item(item)['values']
            documents_rows += f"""
            <tr>
                <td>{values[0]}</td>
                <td>{values[1]}</td>
                <td>{values[2]}</td>
                <td>{values[3]}</td>
            </tr>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تفاصيل الحساب - {self.account_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .info-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .info-table th {{ background-color: #f2f2f2; font-weight: bold; }}
                .documents-table {{ width: 100%; border-collapse: collapse; }}
                .documents-table th, .documents-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .documents-table th {{ background-color: #366092; color: white; }}
                @media print {{
                    body {{ margin: 0; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>المملكة الأردنية الهاشمية</h1>
                <h2>وزارة الصحة</h2>
                <h3>تفاصيل الحساب: {self.account_name}</h3>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M')}</p>
            </div>

            <table class="info-table">
                <tr><th>رقم الحساب</th><td>{self.account_num}</td></tr>
                <tr><th>اسم الحساب</th><td>{self.account_name}</td></tr>
                <tr><th>الرصيد الافتتاحي</th><td>{opening_balance}</td></tr>
                <tr><th>الرصيد الحالي</th><td>{current_balance}</td></tr>
                <tr><th>عدد المستندات</th><td>{documents_count}</td></tr>
                <tr><th>إجمالي المبالغ</th><td>{total_amount}</td></tr>
            </table>

            <h3>المستندات:</h3>
            <table class="documents-table">
                <thead>
                    <tr>
                        <th>المبلغ</th>
                        <th>رقم المستند</th>
                        <th>رقم التأدية</th>
                        <th>القسم</th>
                    </tr>
                </thead>
                <tbody>
                    {documents_rows}
                </tbody>
            </table>

            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
        """

        return html_content
