# ===============================================================================
# تخطيط نوافذ التطبيق - نظام إدارة المستندات المحاسبية
# ===============================================================================
# تاريخ الإنشاء: 2025-06-28
# الإصدار: 2.1
# المطور: Augment Agent
# ===============================================================================

# ===============================================================================
# 1. الواجهة الرئيسية (app.py)
# ===============================================================================

## ترتيب الأزرار في الواجهة الرئيسية:
## الشبكة: 3 أعمدة × عدة صفوف

### الصف الأول:
[1,1] 📁 إضافة حساب جديد        | [1,2] 📝 إضافة مستند              | [1,3] ⚙️ إدارة الحسابات

### الصف الثاني:
[2,1] 💰 إضافة حساب مقبوضات      | [2,2] 📄 إضافة مستند مقبوضات      | [2,3] ⚙️ إدارة حسابات المقبوضات

### الصف الثالث:
[3,1] 🔍 بحث في الحسابات         | [3,2] 📊 نافذة تقارير الأرصدة      | [3,3] 👥 إدارة المستخدمين

### الصف الرابع:
[4,1] 🚪 خروج من النظام          | [4,2] [فارغ]                      | [4,3] [فارغ]

## تصنيف الأزرار حسب الوظيفة:

### قسم الحسابات الرئيسية:
- 📁 إضافة حساب جديد (add_account)
- 📝 إضافة مستند (add_document)  
- ⚙️ إدارة الحسابات (manage_accounts)

### قسم المقبوضات:
- 💰 إضافة حساب مقبوضات (add_receipts_account)
- 📄 إضافة مستند مقبوضات (add_receipts_document)
- ⚙️ إدارة حسابات المقبوضات (manage_receipts_accounts)

### قسم البحث والتقارير:
- 🔍 بحث في الحسابات (search_accounts)
- 📊 نافذة تقارير الأرصدة (show_account_balances_window)

### قسم إدارة النظام:
- 👥 إدارة المستخدمين (manage_users)
- 🚪 خروج من النظام (exit_application)

## ألوان الأزرار:
- الحسابات الرئيسية: أزرق (#3498db), أخضر (#27ae60), أحمر (#e74c3c)
- المقبوضات: تركوازي (#16a085), أخضر فاتح (#2ecc71), بنفسجي (#8e44ad)
- البحث والتقارير: برتقالي (#f39c12), برتقالي داكن (#e67e22)
- إدارة النظام: رمادي داكن (#34495e), رمادي فاتح (#95a5a6)

# ===============================================================================
# 2. نافذة إضافة حساب جديد (add_account_window.py)
# ===============================================================================

## الملف المستهدف: accounting_system.xlsx
## الوظيفة: إضافة حساب جديد في النظام الرئيسي

### حقول الإدخال:
- اسم الحساب (نص)
- الرصيد الافتتاحي (رقم)
- ملاحظات (نص اختياري)

### الأزرار:
- ✅ حفظ الحساب
- ❌ إلغاء

### موقع البيانات:
- ورقة جديدة باسم الحساب
- الرصيد الافتتاحي في الخلية A7
- تنسيق الجدول: 6 أقسام × 3 أعمدة لكل قسم

# ===============================================================================
# 3. نافذة إضافة مستند (document_window.py)
# ===============================================================================

## الملف المستهدف: accounting_system.xlsx
## الوظيفة: إضافة مستند في حساب موجود

### حقول الإدخال:
- اختيار الحساب (قائمة منسدلة)
- المبلغ (رقم)
- رقم المستند (نص)
- التاريخ (تاريخ)
- البيان (نص)

### الأزرار:
- ✅ حفظ المستند
- ❌ إلغاء

### موقع البيانات:
- في ورقة الحساب المحدد
- أول خلية فارغة في الأقسام الستة
- تحديث تلقائي للرصيد

# ===============================================================================
# 4. نافذة إدارة الحسابات (manage_accounts_window.py)
# ===============================================================================

## الملف المستهدف: accounting_system.xlsx
## الوظيفة: عرض وإدارة جميع الحسابات

### المكونات:
- جدول عرض الحسابات
- أزرار التحكم (تعديل، حذف، عرض التفاصيل)
- شريط البحث

### الأزرار:
- 👁️ عرض تفاصيل الحساب
- ✏️ تعديل الحساب
- 🗑️ حذف الحساب
- 🔄 تحديث القائمة

# ===============================================================================
# 5. نافذة إضافة حساب مقبوضات (receipts_account_window.py)
# ===============================================================================

## الملف المستهدف: Accounting system deductions.xlsx
## الوظيفة: إضافة حساب مقبوضات جديد

### حقول الإدخال:
- اسم الحساب (نص)
- نوع المقبوضات (قائمة)
- الرصيد الافتتاحي (رقم)

### الأزرار:
- ✅ حفظ الحساب
- ❌ إلغاء

### موقع البيانات:
- ورقة جديدة باسم الحساب
- تنسيق خاص بالمقبوضات

# ===============================================================================
# 6. نافذة إضافة مستند مقبوضات (receipts_document_window.py)
# ===============================================================================

## الملف المستهدف: Accounting system deductions.xlsx
## الوظيفة: إضافة مستند مقبوضات

### حقول الإدخال:
- اختيار الحساب (قائمة منسدلة)
- المبلغ (رقم)
- رقم المستند/التأدية (نص)
- التاريخ (تاريخ)
- البيان (نص)

### الأزرار:
- ✅ حفظ المستند
- ❌ إلغاء

### موقع البيانات:
- في ورقة حساب المقبوضات المحدد
- تحديث تلقائي للرصيد

# ===============================================================================
# 7. نافذة إدارة حسابات المقبوضات (manage_receipts_accounts_window.py)
# ===============================================================================

## الملف المستهدف: Accounting system deductions.xlsx
## الوظيفة: إدارة حسابات المقبوضات

### المكونات:
- جدول عرض حسابات المقبوضات
- أزرار التحكم
- إحصائيات سريعة

### الأزرار:
- 👁️ عرض تفاصيل الحساب
- ✏️ تعديل الحساب
- 🗑️ حذف الحساب
- 📊 عرض التقارير

# ===============================================================================
# 8. نافذة البحث (search_window.py)
# ===============================================================================

## الملفات المستهدفة: جميع ملفات Excel
## الوظيفة: البحث في الحسابات والمستندات

### حقول البحث:
- نص البحث (نص)
- نوع البحث (حسابات/مستندات/الكل)
- نطاق البحث (النظام الرئيسي/المقبوضات/الكل)

### الأزرار:
- 🔍 بحث
- 🔄 مسح النتائج
- 📄 تصدير النتائج

### عرض النتائج:
- جدول النتائج
- تفاصيل كل نتيجة
- روابط للانتقال للحساب

# ===============================================================================
# 9. نافذة تقارير الأرصدة (account_balances_window.py)
# ===============================================================================

## الوظيفة: إنشاء تقارير الأرصدة المحسنة

### الأزرار الرئيسية:
1. 💰 تقرير حسابات المقبوضات
2. 📦 تقرير حسابات المواد

### تقرير حسابات المقبوضات:
- الملف المستهدف: Accounting system deductions.xlsx
- اسم الورقة: "تقرير حسابات المقبوضات"
- اللون الأساسي: أزرق (#3498DB)
- المحتوى: جميع حسابات المقبوضات مع أرصدتها

### تقرير حسابات المواد:
- الملف المستهدف: accounting_system.xlsx
- اسم الورقة: "تقرير حسابات المواد"
- اللون الأساسي: أخضر (#27AE60)
- المحتوى: جميع حسابات النظام الرئيسي مع أرصدتها

### مكونات التقرير:
- رأس احترافي مع شعار الوزارة
- جدول منسق بألوان وحدود
- عناوين بالرموز التعبيرية
- النسب المئوية لكل حساب
- إجمالي نهائي
- معلومات إحصائية مفصلة
- تاريخ ووقت التحديث

### الأزرار الإضافية:
- ❓ مساعدة
- ❌ إغلاق

# ===============================================================================
# 10. نافذة إدارة المستخدمين (user_management_window.py)
# ===============================================================================

## الملف المستهدف: users.json
## الوظيفة: إدارة مستخدمي النظام

### المكونات:
- جدول المستخدمين
- حقول إضافة/تعديل المستخدم
- إدارة الصلاحيات

### حقول المستخدم:
- اسم المستخدم (نص)
- كلمة المرور (نص مخفي)
- الاسم الكامل (نص)
- الصلاحيات (قائمة اختيار متعددة)

### الصلاحيات المتاحة:
- add_account: إضافة حسابات
- edit_account: تعديل حسابات
- delete_account: حذف حسابات
- add_document: إضافة مستندات
- edit_document: تعديل مستندات
- delete_document: حذف مستندات
- view_reports: عرض التقارير
- manage_users: إدارة المستخدمين

### الأزرار:
- ➕ إضافة مستخدم
- ✏️ تعديل مستخدم
- 🗑️ حذف مستخدم
- 🔄 تحديث القائمة
- 💾 حفظ التغييرات

# ===============================================================================
# 11. نافذة تفاصيل الحساب (account_details_window.py)
# ===============================================================================

## الوظيفة: عرض تفاصيل حساب محدد

### المكونات:
- معلومات الحساب الأساسية
- جدول المستندات
- إحصائيات الحساب

### معلومات الحساب:
- اسم الحساب
- الرصيد الافتتاحي
- الرصيد الحالي
- عدد المستندات
- تاريخ آخر تحديث

### جدول المستندات:
- التاريخ
- رقم المستند
- المبلغ
- البيان
- الرصيد التراكمي

### الأزرار:
- ✏️ تعديل الحساب
- ➕ إضافة مستند
- 📊 تقرير الحساب
- 🖨️ طباعة
- ❌ إغلاق

# ===============================================================================
# 12. نافذة تسجيل الدخول (login_window.py)
# ===============================================================================

## الوظيفة: تسجيل دخول المستخدمين

### حقول الإدخال:
- اسم المستخدم (نص)
- كلمة المرور (نص مخفي)

### الأزرار:
- 🔐 تسجيل الدخول
- ❌ إلغاء

### المعلومات المعروضة:
- شعار النظام
- اسم النظام
- معلومات الإصدار

# ===============================================================================
# 13. نافذة تغيير كلمة المرور (change_password_window.py)
# ===============================================================================

## الوظيفة: تغيير كلمة مرور المستخدم الحالي

### حقول الإدخال:
- كلمة المرور الحالية (نص مخفي)
- كلمة المرور الجديدة (نص مخفي)
- تأكيد كلمة المرور (نص مخفي)

### الأزرار:
- ✅ تغيير كلمة المرور
- ❌ إلغاء

# ===============================================================================
# هيكل الملفات والبيانات
# ===============================================================================

## ملفات Excel:

### 1. accounting_system.xlsx (النظام الرئيسي):
- الورقة الرئيسية: "مرحباً"
- أوراق الحسابات: كل حساب في ورقة منفصلة
- ورقة التقارير: "تقرير حسابات المواد"
- تنسيق الحساب:
  * الخلية A7: الرصيد الافتتاحي
  * الصفوف 8-32: المستندات
  * 6 أقسام × 3 أعمدة (مبلغ، رقم مستند، تاريخ)

### 2. Accounting system deductions.xlsx (المقبوضات):
- الورقة الرئيسية: "مرحباً"
- أوراق حسابات المقبوضات
- ورقة التقارير: "تقرير حسابات المقبوضات"
- تنسيق خاص بالمقبوضات

## ملفات JSON:

### 1. users.json (المستخدمون):
```json
{
  "username": {
    "password": "hashed_password",
    "full_name": "الاسم الكامل",
    "permissions": ["permission1", "permission2"]
  }
}
```

### 2. config.json (الإعدادات):
```json
{
  "app_name": "نظام إدارة المستندات المحاسبية",
  "version": "2.1",
  "default_permissions": [],
  "backup_enabled": true
}
```

# ===============================================================================
# قواعد التسمية والتنظيم
# ===============================================================================

## أسماء الملفات:
- الملفات الرئيسية: app.py
- نوافذ النظام: [name]_window.py
- الأدوات المساعدة: [name]_manager.py
- ملفات البيانات: [name].xlsx, [name].json

## أسماء الدوال:
- إضافة: add_[item]
- تعديل: edit_[item]
- حذف: delete_[item]
- عرض: show_[item]
- إدارة: manage_[items]
- إنشاء: create_[item]

## أسماء المتغيرات:
- النوافذ: [name]_window
- البيانات: [name]_data
- الإعدادات: [name]_config
- الملفات: [name]_file

## ألوان النظام:
- الأزرق: #3498db (الحسابات الرئيسية)
- الأخضر: #27ae60 (المستندات)
- الأحمر: #e74c3c (الحذف والتحذيرات)
- البرتقالي: #f39c12 (البحث)
- البنفسجي: #8e44ad (المقبوضات)
- الرمادي: #95a5a6 (الإلغاء والخروج)

# ===============================================================================
# إرشادات التطوير والتحديث
# ===============================================================================

## عند إضافة نافذة جديدة:
1. إنشاء ملف [name]_window.py
2. إضافة الاستيراد في app.py
3. إضافة الدالة في app.py
4. إضافة الزر في buttons_config
5. إضافة مفتاح النافذة في open_windows
6. تحديث هذا الملف

## عند تعديل نافذة موجودة:
1. تحديث الملف المقابل
2. تحديث الدوال المرتبطة
3. تحديث هذا الملف
4. اختبار التغييرات

## عند إضافة ميزة جديدة:
1. تحديد الملفات المتأثرة
2. تحديث الصلاحيات إن لزم الأمر
3. تحديث واجهة المستخدم
4. تحديث هذا الملف
5. إنشاء ملف اختبار

## نصائح الصيانة:
- مراجعة هذا الملف عند كل تحديث
- الحفاظ على تسمية متسقة
- توثيق أي تغييرات جديدة
- اختبار جميع النوافذ بعد التحديث

# ===============================================================================
# معلومات الإصدار
# ===============================================================================

## الإصدار الحالي: 2.1
## تاريخ آخر تحديث: 2025-06-28
## التحديثات الأخيرة:
- حذف زر "تقرير أرصدة الحسابات" المكرر
- إعادة ترتيب أزرار الواجهة الرئيسية
- تنظيم الأزرار في أقسام منطقية
- تفعيل الزر الثاني في نافذة تقارير الأرصدة
- إضافة تقرير حسابات المواد المحسن

## المطور: Augment Agent
## البريد الإلكتروني: <EMAIL>
## الموقع: https://augment.com

# ===============================================================================
# نهاية الملف
# ===============================================================================
