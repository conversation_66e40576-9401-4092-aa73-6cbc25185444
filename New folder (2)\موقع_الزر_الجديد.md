# 📍 موقع الزر الجديد - نافذة تقارير الأرصدة

## 🎯 الزر المضاف

تم إضافة زر جديد في الواجهة الرئيسية للتطبيق:

**اسم الزر**: `📊 نافذة تقارير الأرصدة`

---

## 📍 موقع الزر في الواجهة

### **في الواجهة الرئيسية:**

```
┌─────────────────────────────────────────────────────────────────────┐
│                    نظام إدارة المستندات المحاسبية                    │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │ 📁 إضافة حساب   │  │ 📝 إضافة مستند  │  │ 🔍 بحث في       │     │
│  │    جديد         │  │                 │  │   الحسابات      │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
│                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │ 📊 تقرير أرصدة  │  │ ⚙️ إدارة        │  │ 💰 إضافة حساب   │     │
│  │   الحسابات      │  │   الحسابات      │  │   مقبوضات       │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
│                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │ 📄 إضافة مستند  │  │ 📊 نافذة تقارير │  │ ⚙️ إدارة حسابات │     │
│  │   مقبوضات       │  │   الأرصدة ⭐    │  │   المقبوضات     │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
│                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐                         │
│  │ 👥 إدارة        │  │ 🚪 خروج من      │                         │
│  │   المستخدمين    │  │   النظام        │                         │
│  └─────────────────┘  └─────────────────┘                         │
└─────────────────────────────────────────────────────────────────────┘
```

**⭐ الزر الجديد**: `📊 نافذة تقارير الأرصدة`

---

## 🔧 التفاصيل التقنية

### **في ملف `app.py`:**

#### **1. تكوين الزر:**
```python
{
    'text': '📊 نافذة تقارير الأرصدة',
    'command': self.show_account_balances_window,
    'permission': 'view_reports',
    'color': '#e67e22',
    'hover_color': '#d35400'
}
```

#### **2. الدالة المرتبطة:**
```python
def show_account_balances_window(self):
    """فتح نافذة تقارير أرصدة الحسابات"""
    if self.check_permission('view_reports'):
        # فحص إذا كانت النافذة مفتوحة بالفعل
        if self.is_window_open('account_balances'):
            if self.focus_existing_window('account_balances'):
                messagebox.showinfo("تنبيه", "نافذة تقارير الأرصدة مفتوحة بالفعل")
                return

        try:
            # فتح نافذة جديدة
            from account_balances_window import AccountBalancesWindow
            window = AccountBalancesWindow(self)
            self.register_window('account_balances', window)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة تقارير الأرصدة: {str(e)}")
```

#### **3. مفتاح النافذة:**
```python
self.open_windows = {
    # ... النوافذ الأخرى
    'account_balances': None  # نافذة تقارير أرصدة الحسابات
}
```

---

## 🎯 وظيفة الزر

عند النقر على الزر `📊 نافذة تقارير الأرصدة`:

### **1. يتم فتح نافذة تحتوي على:**

```
┌─────────────────────────────────────────────────────────┐
│                📊 تقارير أرصدة الحسابات                │
│                                                         │
│            اختر نوع التقرير المطلوب إنشاؤه             │
│                                                         │
│  ┌─────────────────────────┐  ┌─────────────────────────┐ │
│  │ 💰 تقرير حسابات        │  │ 📄 الزر الثاني         │ │
│  │    المقبوضات           │  │   (معطل حالياً)        │ │
│  │     (محسن)             │  │                         │ │
│  └─────────────────────────┘  └─────────────────────────┘ │
│                                                         │
│  ┌─────────────── معلومات التقارير ──────────────────┐   │
│  │ 🔹 تقرير حسابات المقبوضات: يتم إنشاؤه في ملف    │   │
│  │    'Accounting system deductions.xlsx'            │   │
│  │    جدول منسق يحتوي على أسماء الحسابات وأرصدتها  │   │
│  │                                                   │   │
│  │ 🔹 الزر الثاني: معطل حالياً - سيتم إضافة وظيفته │   │
│  │    لاحقاً مخصص لميزة مستقبلية في النظام        │   │
│  └───────────────────────────────────────────────────┘   │
│                                                         │
│  ❓ مساعدة                              ❌ إغلاق      │
└─────────────────────────────────────────────────────────┘
```

### **2. الأزرار المتاحة في النافذة:**

#### **الزر الأول**: `💰 تقرير حسابات المقبوضات`
- **الوظيفة**: إنشاء تقرير محسن لحسابات المقبوضات
- **الملف**: `Accounting system deductions.xlsx`
- **المميزات**:
  - جدول منسق بألوان وحدود محسنة
  - حساب النسب المئوية لكل حساب
  - إجمالي نهائي مع معلومات إحصائية
  - تحديث تلقائي للبيانات

#### **الزر الثاني**: `📄 الزر الثاني`
- **الحالة**: معطل حالياً (`state='disabled'`)
- **الوظيفة**: مخصص لميزة مستقبلية
- **الرسالة**: "هذه الميزة قيد التطوير وستكون متاحة قريباً"

---

## 🔐 الصلاحيات المطلوبة

**للوصول للزر**: `view_reports`

- المستخدمون الذين لديهم صلاحية عرض التقارير فقط يمكنهم رؤية واستخدام هذا الزر
- إذا لم تكن لديك الصلاحية، لن يظهر الزر في الواجهة

---

## 🧪 اختبار الزر

### **1. تشغيل ملف الاختبار:**
```bash
python test_new_button.py
```

### **2. الاختبار اليدوي:**
1. تشغيل التطبيق: `python app.py`
2. تسجيل الدخول بحساب له صلاحية `view_reports`
3. البحث عن الزر `📊 نافذة تقارير الأرصدة` في الواجهة الرئيسية
4. النقر على الزر
5. التحقق من فتح النافذة الصحيحة

---

## 🎨 خصائص الزر

### **المظهر:**
- **اللون الأساسي**: `#e67e22` (برتقالي)
- **لون التمرير**: `#d35400` (برتقالي داكن)
- **الأيقونة**: 📊 (رمز الرسم البياني)
- **النص**: "نافذة تقارير الأرصدة"

### **السلوك:**
- **منع التكرار**: لا يمكن فتح أكثر من نافذة واحدة
- **التركيز**: إذا كانت النافذة مفتوحة، يتم التركيز عليها
- **معالجة الأخطاء**: رسائل خطأ واضحة في حالة فشل الفتح

---

## 📋 ملخص التحديثات

### **الملفات المحدثة:**

1. **`app.py`**:
   - ✅ إضافة تكوين الزر في `buttons_config`
   - ✅ إضافة دالة `show_account_balances_window()`
   - ✅ إضافة مفتاح `'account_balances'` في `open_windows`

2. **`account_balances_window.py`** (محدث مسبقاً):
   - ✅ الزر الأول: تقرير حسابات المقبوضات (محسن)
   - ✅ الزر الثاني: فارغ حالياً (معطل)

3. **`test_new_button.py`** (جديد):
   - ✅ ملف اختبار للزر الجديد

---

## 🎉 النتيجة النهائية

الآن يمكن للمستخدمين الوصول إلى نافذة تقارير أرصدة الحسابات مباشرة من الواجهة الرئيسية عبر:

**الواجهة الرئيسية → 📊 نافذة تقارير الأرصدة**

وستفتح النافذة التي تحتوي على:
- **الزر الأول**: تقرير حسابات المقبوضات (محسن وجاهز للاستخدام)
- **الزر الثاني**: معطل حالياً (جاهز للتطوير المستقبلي)

---

**📅 تاريخ الإضافة**: 2025-06-28  
**🔧 الإصدار**: 1.0  
**👨‍💻 المطور**: Augment Agent
