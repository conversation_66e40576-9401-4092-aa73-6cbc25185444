#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة المستندات
"""

import os
import sys

def test_add_document():
    """اختبار إضافة مستند جديد"""
    try:
        print("🧪 اختبار إضافة مستند جديد...")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        
        # إنشاء مدير Excel
        excel = ExcelManager()
        print("✅ تم إنشاء ExcelManager")
        
        # إنشاء حساب تجريبي أولاً
        account_num = "TEST001"
        account_name = "حساب اختبار المستندات"
        initial_balance = 5000
        
        print(f"📝 إنشاء حساب تجريبي: {account_num} - {account_name}")
        result = excel.create_account_sheet(account_num, account_name, initial_balance)
        
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        print("✅ تم إنشاء الحساب التجريبي")
        
        # اختبار إضافة مستند
        sheet_name = f"{account_num}-{account_name}"
        amount = 1500.50
        doc_num = "DOC001"
        pay_num = "PAY001"
        
        print(f"📄 إضافة مستند:")
        print(f"   الحساب: {sheet_name}")
        print(f"   المبلغ: {amount}")
        print(f"   رقم المستند: {doc_num}")
        print(f"   رقم التأدية: {pay_num}")
        
        result = excel.add_document(sheet_name, amount, doc_num, pay_num)
        
        if result:
            print("✅ تم إضافة المستند بنجاح!")
            
            # التحقق من وجود المستند في الملف
            ws = excel.workbook[sheet_name]
            
            # فحص الخلية A10 (أول خلية للمستندات في القسم الأول)
            amount_cell = ws['A10'].value
            doc_cell = ws['B10'].value
            pay_cell = ws['C10'].value
            
            print(f"💰 المبلغ المحفوظ: {amount_cell}")
            print(f"📄 رقم المستند المحفوظ: {doc_cell}")
            print(f"🧾 رقم التأدية المحفوظ: {pay_cell}")
            
            # التحقق من صحة البيانات
            if (amount_cell == amount and 
                str(doc_cell) == str(doc_num) and 
                str(pay_cell) == str(pay_num)):
                print("✅ جميع البيانات محفوظة بشكل صحيح")
                return True
            else:
                print("❌ البيانات المحفوظة غير صحيحة")
                return False
        else:
            print("❌ فشل في إضافة المستند")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_multiple_documents():
    """اختبار إضافة عدة مستندات"""
    try:
        print("\n🧪 اختبار إضافة عدة مستندات...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "MULTI001"
        account_name = "حساب متعدد المستندات"
        
        print(f"📝 إنشاء حساب: {account_num} - {account_name}")
        result = excel.create_account_sheet(account_num, account_name, 10000)
        
        if not result:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        
        # إضافة عدة مستندات
        documents = [
            (1000, "DOC001", "PAY001"),
            (2000, "DOC002", "PAY002"),
            (1500, "DOC003", "PAY003"),
            (3000, "DOC004", "PAY004"),
            (500, "DOC005", "PAY005")
        ]
        
        success_count = 0
        for i, (amount, doc_num, pay_num) in enumerate(documents, 1):
            print(f"📄 إضافة المستند {i}: {amount} - {doc_num} - {pay_num}")
            
            result = excel.add_document(sheet_name, amount, doc_num, pay_num)
            if result:
                print(f"✅ تم إضافة المستند {i}")
                success_count += 1
            else:
                print(f"❌ فشل في إضافة المستند {i}")
        
        print(f"📊 تم إضافة {success_count} من أصل {len(documents)} مستندات")
        
        if success_count == len(documents):
            print("✅ تم إضافة جميع المستندات بنجاح")
            return True
        else:
            print("⚠️ لم يتم إضافة جميع المستندات")
            return success_count > 0
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المستندات المتعددة: {str(e)}")
        return False

def test_invalid_account():
    """اختبار إضافة مستند لحساب غير موجود"""
    try:
        print("\n🧪 اختبار إضافة مستند لحساب غير موجود...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # محاولة إضافة مستند لحساب غير موجود
        fake_account = "FAKE001-حساب وهمي"
        
        print(f"📄 محاولة إضافة مستند للحساب الوهمي: {fake_account}")
        
        result = excel.add_document(fake_account, 1000, "DOC999", "PAY999")
        
        if not result:
            print("✅ تم رفض المستند للحساب غير الموجود بنجاح")
            return True
        else:
            print("❌ تم قبول المستند للحساب غير الموجود (خطأ)")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحساب الوهمي: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار إضافة المستندات")
    print("=" * 50)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 3
    
    # اختبار إضافة مستند واحد
    if test_add_document():
        success_count += 1
    
    # اختبار إضافة عدة مستندات
    if test_multiple_documents():
        success_count += 1
    
    # اختبار حساب غير موجود
    if test_invalid_account():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات إضافة المستندات!")
        return True
    else:
        print("❌ فشل في بعض اختبارات إضافة المستندات")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
