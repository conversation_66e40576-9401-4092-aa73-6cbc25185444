#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التنسيق الجديد لصفحة الحساب
"""

import os
import sys
from excel_manager import ExcelManager

def test_new_format():
    """اختبار التنسيق الجديد"""
    print("🚀 اختبار التنسيق الجديد لصفحة الحساب")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "test_new_format.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    try:
        # إنشاء مدير Excel
        excel = ExcelManager(test_file)
        
        # إنشاء حساب تجريبي بالتنسيق الجديد
        account_num = "1001"
        account_name = "حساب الأدوية والمستلزمات الطبية"
        initial_balance = 5000.000
        
        print(f"📝 إنشاء حساب تجريبي:")
        print(f"   رقم الحساب: {account_num}")
        print(f"   اسم الحساب: {account_name}")
        print(f"   الرصيد الافتتاحي: {initial_balance}")
        
        # إنشاء الحساب بالتنسيق الجديد
        result = excel.create_account_sheet(account_num, account_name, initial_balance, "official")
        
        if result:
            print("✅ تم إنشاء الحساب بنجاح")
            
            # إضافة بعض المستندات التجريبية
            sheet_name = f"{account_num}-{account_name}"
            
            print("📄 إضافة مستندات تجريبية...")
            
            # مستند 1
            excel.add_document(sheet_name, 1500.500, "DOC001", "PAY001")
            print("   ✅ تم إضافة المستند الأول")
            
            # مستند 2
            excel.add_document(sheet_name, 2300.750, "DOC002", "PAY002")
            print("   ✅ تم إضافة المستند الثاني")
            
            # مستند 3
            excel.add_document(sheet_name, 800.250, "DOC003", "PAY003")
            print("   ✅ تم إضافة المستند الثالث")
            
            # حفظ الملف
            if excel.save_workbook():
                print(f"💾 تم حفظ الملف: {test_file}")
                print("\n🎉 تم إنجاز الاختبار بنجاح!")
                print(f"📂 يمكنك فتح الملف '{test_file}' لمراجعة التنسيق الجديد")
                return True
            else:
                print("❌ فشل في حفظ الملف")
                return False
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    success = test_new_format()
    
    if success:
        print("\n" + "="*60)
        print("✅ تم إنجاز الاختبار بنجاح")
        print("📋 التنسيق الجديد يتضمن:")
        print("   • وزارة / الدائرة : وزارة الصحة")
        print("   • الشهر = تلقائي")
        print("   • مركز : التأمين الصحي")
        print("   • 6 أقسام أفقية مع أسماء الحسابات")
        print("   • رقم الحساب 1/214")
        print("   • عناوين الأعمدة: فلس/دينار، مستند الصرف، رقم التأدية")
        print("   • الرصيد الافتتاحي وصيغ SUM للترحيل")
        print("   • صف المجاميع مع صيغ SUM")
        print("   • التاريخ = التاريخ الحالي=تلقائي")
        print("   • اسم وتوقيع المحاسب")
        print("="*60)
    else:
        print("\n❌ فشل الاختبار")
        sys.exit(1)

if __name__ == "__main__":
    main()
