#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع حفظ معلومات تسجيل الدخول والدخول التلقائي
"""

import sys
import traceback
import json
import os
from datetime import datetime

# ملف حفظ معلومات تسجيل الدخول
LOGIN_SAVE_FILE = "saved_login.json"

def save_login_info(username, password):
    """حفظ معلومات تسجيل الدخول"""
    try:
        login_data = {
            "username": username,
            "password": password,
            "last_login": datetime.now().isoformat(),
            "auto_login": True
        }
        
        with open(LOGIN_SAVE_FILE, 'w', encoding='utf-8') as f:
            json.dump(login_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ معلومات تسجيل الدخول في: {LOGIN_SAVE_FILE}")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ معلومات تسجيل الدخول: {e}")
        return False

def load_login_info():
    """تحميل معلومات تسجيل الدخول المحفوظة"""
    try:
        if os.path.exists(LOGIN_SAVE_FILE):
            with open(LOGIN_SAVE_FILE, 'r', encoding='utf-8') as f:
                login_data = json.load(f)
            
            if login_data.get("auto_login", False):
                print(f"🔓 تم العثور على معلومات تسجيل دخول محفوظة للمستخدم: {login_data['username']}")
                return login_data
        
        return None
    except Exception as e:
        print(f"❌ خطأ في تحميل معلومات تسجيل الدخول: {e}")
        return None

def create_auto_login_app(root):
    """إنشاء التطبيق مع تسجيل دخول تلقائي"""
    try:
        from app import AccountingApp
        from user_manager import UserManager
        
        print("🔓 إنشاء التطبيق مع تسجيل دخول تلقائي...")
        
        # تحميل معلومات تسجيل الدخول المحفوظة
        saved_login = load_login_info()
        
        if saved_login:
            print(f"👤 استخدام معلومات تسجيل الدخول المحفوظة: {saved_login['username']}")
            
            # إنشاء التطبيق
            app = AccountingApp(root)
            
            # محاولة تسجيل الدخول التلقائي
            if hasattr(app, 'user_manager'):
                # تعيين المستخدم الحالي مباشرة
                app.user_manager.current_user = {
                    'username': saved_login['username'],
                    'role': 'admin',  # افتراضي
                    'permissions': ['add_account', 'delete_account', 'add_document', 'view_reports', 'manage_users']
                }
                
                # إغلاق نافذة تسجيل الدخول إذا كانت مفتوحة
                if hasattr(app, 'login_window') and app.login_window:
                    try:
                        if hasattr(app.login_window, 'root'):
                            app.login_window.root.destroy()
                        print("✅ تم إغلاق نافذة تسجيل الدخول")
                    except:
                        pass
                
                # إظهار النافذة الرئيسية
                app.root.deiconify()
                
                # إعداد الواجهة
                if hasattr(app, 'setup_modern_ui'):
                    app.setup_modern_ui()
                
                # بدء التحديث التلقائي
                if hasattr(app, 'start_auto_refresh'):
                    try:
                        app.start_auto_refresh()
                    except:
                        pass
                
                # تحديث عنوان النافذة
                app.root.title(f"نظام إدارة المستندات المحاسبية - وزارة الصحة (المستخدم: {saved_login['username']})")
                
                print(f"✅ تم تسجيل الدخول تلقائياً باسم: {saved_login['username']}")
                return app
        
        # إذا لم توجد معلومات محفوظة، إنشاء التطبيق عادي
        print("ℹ️ لا توجد معلومات تسجيل دخول محفوظة، سيتم عرض نافذة تسجيل الدخول")
        app = AccountingApp(root)
        
        # حفظ معلومات تسجيل الدخول عند النجاح
        original_on_login_success = app.on_login_success
        
        def enhanced_on_login_success():
            # حفظ معلومات تسجيل الدخول
            if hasattr(app, 'user_manager') and app.user_manager.current_user:
                username = app.user_manager.current_user.get('username', 'admin')
                save_login_info(username, 'saved')  # كلمة مرور رمزية
            
            # استدعاء الدالة الأصلية
            original_on_login_success()
        
        app.on_login_success = enhanced_on_login_success
        
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        traceback.print_exc()
        raise

def main():
    """الدالة الرئيسية"""
    
    print("=" * 80)
    print("🚀 نظام إدارة المستندات المحاسبية - تشغيل مع حفظ تسجيل الدخول")
    print("=" * 80)
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔐 سيتم حفظ معلومات تسجيل الدخول للمرات القادمة")
    print("=" * 80)
    
    try:
        print("📦 استيراد الوحدات...")
        
        import tkinter as tk
        print("✅ tkinter - OK")
        
        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        
        # إنشاء التطبيق مع تسجيل دخول تلقائي
        print("🏗️ إنشاء التطبيق...")
        app = create_auto_login_app(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # تشغيل التطبيق
        print("▶️ بدء تشغيل التطبيق...")
        print("✅ التطبيق يعمل الآن!")
        print("💡 معلومات تسجيل الدخول ستُحفظ تلقائياً للمرات القادمة")
        print("-" * 80)
        
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود جميع الملفات المطلوبة")
        
    except FileNotFoundError as e:
        print(f"❌ ملف غير موجود: {e}")
        print("💡 تأكد من وجود ملف Excel: accounting_system.xlsx")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        print("📋 تفاصيل الخطأ:")
        traceback.print_exc()
        
    finally:
        print(f"\n🔚 انتهاء تشغيل التطبيق - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

if __name__ == "__main__":
    main()
