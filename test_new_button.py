#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الزر الجديد في الواجهة الرئيسية
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_new_button():
    """اختبار الزر الجديد لنافذة تقارير الأرصدة"""
    try:
        print("🧪 اختبار الزر الجديد في الواجهة الرئيسية...")
        
        # محاكاة كائن التطبيق
        class MockApp:
            def __init__(self):
                self.root = tk.Tk()
                self.root.withdraw()  # إخفاء النافذة الرئيسية
                
                # محاكاة نظام إدارة المستخدمين
                self.user_manager = MockUserManager()
                
                # محاكاة نظام إدارة النوافذ
                self.open_windows = {'account_balances': None}
            
            def check_permission(self, permission):
                return True  # السماح بجميع الصلاحيات للاختبار
            
            def is_window_open(self, window_key):
                return self.open_windows.get(window_key) is not None
            
            def focus_existing_window(self, window_key):
                return False  # لا توجد نافذة مفتوحة
            
            def register_window(self, window_key, window):
                self.open_windows[window_key] = window
                print(f"✅ تم تسجيل النافذة: {window_key}")
        
        class MockUserManager:
            def has_permission(self, permission):
                return True  # السماح بجميع الصلاحيات للاختبار
        
        # إنشاء التطبيق المحاكي
        app = MockApp()
        
        # اختبار الدالة الجديدة
        print("🔧 اختبار دالة show_account_balances_window...")
        
        # استيراد الدالة من app.py
        sys.path.append('.')
        from app import AccountingApp
        
        # إنشاء كائن حقيقي للاختبار
        real_app = AccountingApp.__new__(AccountingApp)
        real_app.root = app.root
        real_app.user_manager = app.user_manager
        real_app.open_windows = app.open_windows
        
        # اختبار الدالة
        try:
            real_app.show_account_balances_window()
            print("✅ تم استدعاء الدالة بنجاح")
            
            # التحقق من فتح النافذة
            if real_app.open_windows.get('account_balances'):
                print("✅ تم فتح نافذة تقارير الأرصدة بنجاح")
                
                # إغلاق النافذة بعد 3 ثوانٍ
                app.root.after(3000, app.root.quit)
                app.root.deiconify()  # إظهار النافذة للاختبار
                app.root.mainloop()
                
                return True
            else:
                print("❌ فشل في فتح النافذة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في استدعاء الدالة: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_button_config():
    """اختبار تكوين الزر في قائمة الأزرار"""
    try:
        print("\n🔍 فحص تكوين الزر في app.py...")
        
        # قراءة ملف app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن تكوين الزر
        if "'text': '📊 نافذة تقارير الأرصدة'" in content:
            print("✅ تم العثور على تكوين الزر")
        else:
            print("❌ لم يتم العثور على تكوين الزر")
            return False
        
        # البحث عن الدالة
        if "def show_account_balances_window(self):" in content:
            print("✅ تم العثور على دالة فتح النافذة")
        else:
            print("❌ لم يتم العثور على دالة فتح النافذة")
            return False
        
        # البحث عن مفتاح النافذة
        if "'account_balances': None" in content:
            print("✅ تم العثور على مفتاح النافذة في قائمة النوافذ")
        else:
            print("❌ لم يتم العثور على مفتاح النافذة")
            return False
        
        print("✅ جميع التكوينات موجودة بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص التكوين: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار الزر الجديد")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # اختبار تكوين الزر
    if test_button_config():
        success_count += 1
    
    # اختبار عمل الزر
    if test_new_button():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات الزر الجديد!")
        print("\n📍 موقع الزر:")
        print("   الواجهة الرئيسية → 📊 نافذة تقارير الأرصدة")
        print("\n🎯 وظيفة الزر:")
        print("   يفتح نافذة تحتوي على زرين:")
        print("   1. 💰 تقرير حسابات المقبوضات (محسن)")
        print("   2. 📄 الزر الثاني (معطل حالياً)")
        return True
    else:
        print("❌ فشل في بعض اختبارات الزر الجديد")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
