#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط للتطبيق مع تسجيل الأخطاء
"""

import sys
import traceback
from datetime import datetime
import os

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    print("=" * 80)
    print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية")
    print("=" * 80)
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        print("📦 استيراد الوحدات...")
        
        import tkinter as tk
        print("✅ تم استيراد tkinter بنجاح")
        
        from app import AccountingApp
        print("✅ تم استيراد AccountingApp بنجاح")
        
        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        
        # إنشاء التطبيق
        print("🏗️ إنشاء كائن التطبيق...")
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # تشغيل التطبيق
        print("▶️ بدء تشغيل حلقة الأحداث الرئيسية...")
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("📋 التطبيق يعمل الآن...")
        print("-" * 80)
        
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"❌ خطأ في الاستيراد: {str(e)}"
        print(error_msg)
        print("💡 تأكد من وجود جميع الملفات المطلوبة:")
        print("   - app.py")
        print("   - excel_manager.py") 
        print("   - manage_accounts.py")
        print("   - document_window.py")
        print("   - search_window.py")
        print("   - user_manager.py")
        
    except FileNotFoundError as e:
        error_msg = f"❌ ملف غير موجود: {str(e)}"
        print(error_msg)
        print("💡 تأكد من وجود ملف Excel: accounting_system.xlsx")
        
    except Exception as e:
        error_msg = f"❌ خطأ عام: {str(e)}"
        print(error_msg)
        print("تفاصيل الخطأ:")
        print(traceback.format_exc())
        
    finally:
        print(f"\n🔚 انتهاء تشغيل التطبيق - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

if __name__ == "__main__":
    main()
