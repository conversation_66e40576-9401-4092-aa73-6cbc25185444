#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تفاصيل الحساب مع معالجة مفصلة للأخطاء
"""

import sys
import traceback
import tkinter as tk
from tkinter import messagebox

def test_account_details():
    """اختبار نافذة تفاصيل الحساب"""
    
    print("🧪 اختبار نافذة تفاصيل الحساب")
    print("=" * 50)
    
    try:
        # استيراد الوحدات المطلوبة
        print("📦 استيراد الوحدات...")
        
        from excel_manager import ExcelManager
        print("✅ ExcelManager - OK")
        
        from manage_accounts import AccountDetailsDialog
        print("✅ AccountDetailsDialog - OK")
        
        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        root.title("اختبار نافذة تفاصيل الحساب")
        root.geometry("400x300")
        
        # إنشاء مدير Excel
        print("📊 إنشاء مدير Excel...")
        excel_manager = ExcelManager()
        
        # تحميل ملف Excel
        print("📁 تحميل ملف Excel...")
        if excel_manager.load_workbook():
            print("✅ تم تحميل ملف Excel بنجاح")
            
            # الحصول على قائمة الحسابات
            accounts = excel_manager.get_accounts_list()
            print(f"📋 تم العثور على {len(accounts)} حساب")
            
            if accounts:
                # اختيار أول حساب للاختبار
                account_num, account_name, balance = accounts[0]
                sheet_name = f"{account_num}-{account_name}"
                
                print(f"🔍 اختبار الحساب: {account_name} ({account_num})")
                print(f"📄 اسم الورقة: {sheet_name}")
                
                # إنشاء دالة لفتح نافذة التفاصيل
                def open_details():
                    try:
                        print("🚀 فتح نافذة تفاصيل الحساب...")
                        
                        # التحقق من وجود الورقة
                        if sheet_name in excel_manager.workbook.sheetnames:
                            print(f"✅ الورقة '{sheet_name}' موجودة")
                            
                            # إنشاء نافذة التفاصيل
                            details_dialog = AccountDetailsDialog(
                                root, excel_manager, sheet_name, account_num, account_name
                            )\n                            print("✅ تم إنشاء نافذة التفاصيل بنجاح")
                            
                        else:
                            print(f"❌ الورقة '{sheet_name}' غير موجودة")
                            print("📋 الأوراق المتاحة:")
                            for sheet in excel_manager.workbook.sheetnames:
                                print(f"   - {sheet}")
                            
                            # جرب بدون اسم الحساب
                            simple_sheet_name = str(account_num)
                            if simple_sheet_name in excel_manager.workbook.sheetnames:
                                print(f"✅ وجدت الورقة البديلة: {simple_sheet_name}")
                                details_dialog = AccountDetailsDialog(
                                    root, excel_manager, simple_sheet_name, account_num, account_name
                                )
                                print("✅ تم إنشاء نافذة التفاصيل بالاسم البديل")
                            else:
                                messagebox.showerror("خطأ", f"لم يتم العثور على ورقة للحساب {account_num}")
                        
                    except Exception as e:
                        error_msg = f"خطأ في فتح نافذة التفاصيل: {str(e)}"
                        print(f"🚨 {error_msg}")
                        print(f"📋 التفاصيل:\n{traceback.format_exc()}")
                        
                        # اقتراح حلول
                        print("💡 اقتراحات للحل:")
                        if "AttributeError" in str(e):
                            print("   - تحقق من أن جميع العناصر تم إنشاؤها في create_widgets()")
                            print("   - تأكد من أن المراجع للعناصر صحيحة")
                        elif "KeyError" in str(e):
                            print("   - تحقق من وجود الورقة في ملف Excel")
                            print("   - تأكد من صحة اسم الورقة")
                        elif "Excel" in str(e):
                            print("   - تأكد من أن ملف Excel مفتوح ومتاح")
                            print("   - أغلق ملف Excel إذا كان مفتوحاً في برنامج آخر")
                        
                        messagebox.showerror("خطأ", error_msg)
                
                # إنشاء واجهة الاختبار
                print("🎨 إنشاء واجهة الاختبار...")
                
                # عنوان
                title_label = tk.Label(root, text="اختبار نافذة تفاصيل الحساب", 
                                     font=("Arial", 16, "bold"))
                title_label.pack(pady=20)
                
                # معلومات الحساب
                info_frame = tk.Frame(root)
                info_frame.pack(pady=10)
                
                tk.Label(info_frame, text=f"رقم الحساب: {account_num}", 
                        font=("Arial", 12)).pack()
                tk.Label(info_frame, text=f"اسم الحساب: {account_name}", 
                        font=("Arial", 12)).pack()
                tk.Label(info_frame, text=f"الرصيد: {balance:,.2f}", 
                        font=("Arial", 12)).pack()
                
                # زر فتح التفاصيل
                open_btn = tk.Button(root, text="فتح نافذة التفاصيل", 
                                   command=open_details,
                                   bg='#00b894', fg='white', 
                                   font=("Arial", 14, "bold"),
                                   padx=30, pady=10)
                open_btn.pack(pady=20)
                
                # معلومات إضافية
                info_text = tk.Text(root, height=8, width=50)
                info_text.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
                
                info_content = f\"\"\"معلومات الاختبار:
- عدد الحسابات المتاحة: {len(accounts)}
- اسم الورقة المتوقع: {sheet_name}
- الأوراق المتاحة في Excel: {len(excel_manager.workbook.sheetnames)}

راقب وحدة التحكم لرؤية تفاصيل الاختبار والأخطاء.\"\"\"\n                \n                info_text.insert(tk.END, info_content)
                info_text.config(state=tk.DISABLED)
                
                print("✅ تم إعداد واجهة الاختبار")
                print("🚀 بدء تشغيل الاختبار...")
                print("-" * 50)
                
                # تشغيل التطبيق
                root.mainloop()
                
            else:
                print("❌ لا توجد حسابات في ملف Excel")
                messagebox.showerror("خطأ", "لا توجد حسابات في ملف Excel")
        else:
            print("❌ فشل في تحميل ملف Excel")
            messagebox.showerror("خطأ", "فشل في تحميل ملف Excel")
    
    except ImportError as e:
        error_msg = f"خطأ في الاستيراد: {str(e)}"
        print(f"🚨 {error_msg}")
        print("💡 تأكد من وجود الملفات المطلوبة:")
        print("   - excel_manager.py")
        print("   - manage_accounts.py")
        
    except Exception as e:
        error_msg = f"خطأ عام: {str(e)}"
        print(f"🚨 {error_msg}")
        print(f"📋 التفاصيل:\n{traceback.format_exc()}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نافذة تفاصيل الحساب")
    print("=" * 50)
    
    try:
        test_account_details()
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n🚨 خطأ في الاختبار: {str(e)}")
        print(f"📋 التفاصيل:\n{traceback.format_exc()}")
    finally:
        print(f"\n🔚 انتهاء الاختبار")

if __name__ == "__main__":
    main()
