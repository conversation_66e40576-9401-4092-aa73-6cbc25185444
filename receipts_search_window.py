import tkinter as tk
from tkinter import ttk, messagebox
import openpyxl
import os
from datetime import datetime

class ReceiptsSearchWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("🔍 البحث في المقبوضات")
        self.parent = parent

        # تكوين النافذة
        self.geometry("1000x700")
        self.configure(bg='#f0f8ff')
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent.root)
        self.grab_set()

        # متغيرات البحث
        self.search_type_var = tk.StringVar(value="رقم المستند")
        self.search_value_var = tk.StringVar()
        self.selected_account_var = tk.StringVar(value="جميع الحسابات")

        # قائمة النتائج والحسابات
        self.search_results = []
        self.available_accounts = []

        # تحميل قائمة الحسابات
        self.load_accounts_list()

        # إنشاء الواجهة
        self.create_interface()

        # توسيط النافذة
        self.center_window()

        # ربط حدث الإغلاق
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_accounts_list(self):
        """تحميل قائمة الحسابات من ملف المقبوضات"""
        try:
            deductions_file = "Accounting system deductions.xlsx"
            if not os.path.exists(deductions_file):
                self.available_accounts = ["جميع الحسابات"]
                return

            workbook = openpyxl.load_workbook(deductions_file)
            accounts = ["جميع الحسابات"]

            for sheet_name in workbook.sheetnames:
                if sheet_name != "مرحباً":  # تجاهل ورقة الترحيب
                    accounts.append(sheet_name)

            workbook.close()
            self.available_accounts = accounts

        except Exception as e:
            print(f"خطأ في تحميل قائمة الحسابات: {str(e)}")
            self.available_accounts = ["جميع الحسابات"]

    def refresh_accounts_list(self):
        """تحديث قائمة الحسابات"""
        self.load_accounts_list()
        if hasattr(self, 'account_combo'):
            self.account_combo['values'] = self.available_accounts
            # إعادة تعيين القيمة إذا لم تعد موجودة
            current_value = self.selected_account_var.get()
            if current_value not in self.available_accounts:
                self.selected_account_var.set("جميع الحسابات")
        messagebox.showinfo("تم", f"تم تحديث قائمة الحسابات. عدد الحسابات: {len(self.available_accounts)-1}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # عنوان النافذة
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        title_label = ttk.Label(title_frame,
                               text="🔍 البحث في المقبوضات",
                               font=('Arial', 16, 'bold'))
        title_label.pack()

        subtitle_label = ttk.Label(title_frame,
                                  text="البحث عن المستندات في ملف حسابات المقبوضات",
                                  font=('Arial', 10))
        subtitle_label.pack()

        # إطار البحث
        self.create_search_frame(main_frame)

        # إطار النتائج
        self.create_results_frame(main_frame)

        # إطار الأزرار
        self.create_buttons_frame(main_frame)

    def create_search_frame(self, parent):
        """إنشاء إطار البحث"""
        search_frame = ttk.LabelFrame(parent, text="🔎 معايير البحث", padding="15")
        search_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        search_frame.grid_columnconfigure(1, weight=1)
        search_frame.grid_columnconfigure(3, weight=1)
        search_frame.grid_columnconfigure(5, weight=1)

        # الصف الأول: نوع البحث وقيمة البحث
        # نوع البحث
        ttk.Label(search_frame, text="نوع البحث:",
                 font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=(0, 10), pady=5, sticky=tk.W)

        search_type_combo = ttk.Combobox(search_frame,
                                        textvariable=self.search_type_var,
                                        values=["رقم المستند", "قيمة المبلغ", "اسم الحساب"],
                                        state='readonly',
                                        font=('Arial', 10),
                                        width=15)
        search_type_combo.grid(row=0, column=1, padx=(0, 20), pady=5, sticky=(tk.W, tk.E))

        # قيمة البحث
        ttk.Label(search_frame, text="قيمة البحث:",
                 font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=(0, 10), pady=5, sticky=tk.W)

        search_entry = ttk.Entry(search_frame,
                                textvariable=self.search_value_var,
                                font=('Arial', 10))
        search_entry.grid(row=0, column=3, padx=(0, 20), pady=5, sticky=(tk.W, tk.E))

        # ربط Enter للبحث
        search_entry.bind('<Return>', lambda e: self.perform_search())

        # زر البحث
        search_btn = ttk.Button(search_frame,
                               text="🔍 بحث",
                               command=self.perform_search,
                               style='Accent.TButton')
        search_btn.grid(row=0, column=4, padx=(10, 0), pady=5)

        # زر مسح
        clear_btn = ttk.Button(search_frame,
                              text="🗑️ مسح",
                              command=self.clear_search)
        clear_btn.grid(row=0, column=5, padx=(10, 0), pady=5)

        # الصف الثاني: اختيار الحساب
        ttk.Label(search_frame, text="الحساب:",
                 font=('Arial', 10, 'bold')).grid(row=1, column=0, padx=(0, 10), pady=(10, 5), sticky=tk.W)

        self.account_combo = ttk.Combobox(search_frame,
                                         textvariable=self.selected_account_var,
                                         values=self.available_accounts,
                                         state='readonly',
                                         font=('Arial', 10),
                                         width=30)
        self.account_combo.grid(row=1, column=1, columnspan=2, padx=(0, 20), pady=(10, 5), sticky=(tk.W, tk.E))

        # زر تحديث قائمة الحسابات
        refresh_accounts_btn = ttk.Button(search_frame,
                                         text="🔄 تحديث الحسابات",
                                         command=self.refresh_accounts_list)
        refresh_accounts_btn.grid(row=1, column=3, columnspan=2, padx=(10, 0), pady=(10, 5))

    def create_results_frame(self, parent):
        """إنشاء إطار النتائج"""
        results_frame = ttk.LabelFrame(parent, text="📋 نتائج البحث", padding="10")
        results_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(0, weight=1)

        # إنشاء Treeview
        columns = ('الحساب', 'رقم المستند', 'المبلغ', 'الموقع', 'القسم', 'الجدول')
        self.tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين والعرض
        column_widths = {
            'الحساب': 200,
            'رقم المستند': 100,
            'المبلغ': 100,
            'الموقع': 80,
            'القسم': 100,
            'الجدول': 80
        }

        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER)
            self.tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # وضع العناصر
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # ربط النقر المزدوج
        self.tree.bind('<Double-1>', self.on_item_double_click)

        # ربط النقر الأيمن لقائمة السياق
        self.tree.bind('<Button-3>', self.show_context_menu)

        # إنشاء قائمة السياق
        self.create_context_menu()

    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="✏️ تعديل المستند", command=self.edit_document)
        self.context_menu.add_command(label="🗑️ حذف المستند", command=self.delete_document)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 نسخ رقم المستند", command=self.copy_document_number)
        self.context_menu.add_command(label="📄 عرض تفاصيل الحساب", command=self.show_account_details)

    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # إحصائيات البحث
        self.stats_label = ttk.Label(buttons_frame,
                                    text="جاهز للبحث...",
                                    font=('Arial', 10))
        self.stats_label.pack(side=tk.LEFT)

        # أزرار العمليات
        btn_frame = ttk.Frame(buttons_frame)
        btn_frame.pack(side=tk.RIGHT)

        ttk.Button(btn_frame,
                  text="✏️ تعديل المحدد",
                  command=self.edit_document).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(btn_frame,
                  text="🗑️ حذف المحدد",
                  command=self.delete_document).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(btn_frame,
                  text="🔄 تحديث",
                  command=self.refresh_search).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(btn_frame,
                  text="❌ إغلاق",
                  command=self.on_closing).pack(side=tk.LEFT)

    def perform_search(self):
        """تنفيذ عملية البحث"""
        search_type = self.search_type_var.get()
        search_value = self.search_value_var.get().strip()

        if not search_value:
            messagebox.showwarning("تنبيه", "الرجاء إدخال قيمة للبحث")
            return

        # مسح النتائج السابقة
        for item in self.tree.get_children():
            self.tree.delete(item)

        self.search_results = []

        try:
            # البحث في ملف المقبوضات
            results = self.search_in_receipts_file(search_type, search_value)

            if results:
                for result in results:
                    # إضافة النتيجة للجدول
                    item_id = self.tree.insert('', 'end', values=(
                        result['account'],
                        result['document_num'],
                        f"{result['amount']:.3f}",
                        result['position'],
                        result['section'],
                        result['table']
                    ))

                    # حفظ البيانات الكاملة
                    self.search_results.append({
                        'item_id': item_id,
                        'data': result
                    })

                # تحديث الإحصائيات
                self.stats_label.config(text=f"تم العثور على {len(results)} نتيجة")

                # تحديد أول عنصر
                if self.tree.get_children():
                    self.tree.selection_set(self.tree.get_children()[0])
                    self.tree.focus(self.tree.get_children()[0])
            else:
                self.stats_label.config(text="لم يتم العثور على نتائج")
                messagebox.showinfo("نتائج البحث", "لم يتم العثور على نتائج مطابقة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
            self.stats_label.config(text="خطأ في البحث")

    def search_in_receipts_file(self, search_type, search_value):
        """البحث في ملف المقبوضات"""
        results = []
        deductions_file = "Accounting system deductions.xlsx"

        if not os.path.exists(deductions_file):
            messagebox.showerror("خطأ", f"ملف المقبوضات غير موجود: {deductions_file}")
            return results

        try:
            workbook = openpyxl.load_workbook(deductions_file)
            selected_account = self.selected_account_var.get()

            # تحديد الحسابات المراد البحث فيها
            if selected_account == "جميع الحسابات":
                # البحث في جميع الحسابات
                sheets_to_search = [name for name in workbook.sheetnames if name != "مرحباً"]
            else:
                # البحث في حساب محدد
                if selected_account in workbook.sheetnames:
                    sheets_to_search = [selected_account]
                else:
                    messagebox.showerror("خطأ", f"الحساب '{selected_account}' غير موجود")
                    return results

            # البحث في الحسابات المحددة
            for sheet_name in sheets_to_search:
                ws = workbook[sheet_name]
                sheet_results = self.search_in_sheet(ws, sheet_name, search_type, search_value)
                results.extend(sheet_results)

            workbook.close()

        except Exception as e:
            raise Exception(f"خطأ في قراءة ملف المقبوضات: {str(e)}")

        return results

    def search_in_sheet(self, ws, sheet_name, search_type, search_value):
        """البحث الشامل في ورقة معينة - جميع الجداول والأقسام"""
        results = []

        # أعمدة البيانات (المبلغ، رقم المستند) لكل قسم
        sections_columns = [
            ('A', 'B'),  # القسم الأول
            ('C', 'D'),  # القسم الثاني
            ('E', 'F'),  # القسم الثالث
            ('G', 'H'),  # القسم الرابع
            ('I', 'J'),  # القسم الخامس
            ('K', 'L'),  # القسم السادس
            ('M', 'N')   # القسم السابع
        ]

        section_names = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس', 'السابع']

        try:
            print(f"🔍 بدء البحث في الحساب: {sheet_name}")

            # البحث الشامل في جميع الخلايا من الصف 1 إلى آخر صف
            max_row = ws.max_row
            table_num = 1

            # البحث في جميع الصفوف
            for row in range(1, max_row + 1):
                # البحث في جميع الأقسام (الأعمدة)
                for section_idx, (amount_col, doc_col) in enumerate(sections_columns):
                    section_name = section_names[section_idx]

                    try:
                        amount_cell = ws[f'{amount_col}{row}']
                        doc_cell = ws[f'{doc_col}{row}']

                        amount_value = amount_cell.value
                        doc_value = doc_cell.value

                        # تجاهل الخلايا الفارغة والعناوين
                        if not amount_value and not doc_value:
                            continue

                        # تجاهل العناوين والنصوص الوصفية
                        if self.is_header_or_label(amount_value, doc_value):
                            continue

                        # تطبيق معايير البحث
                        match_found = False

                        if search_type == "رقم المستند" and doc_value:
                            if str(doc_value).strip() == str(search_value).strip():
                                match_found = True
                                print(f"✅ عثر على مستند: {doc_value} في {amount_col}{row}")

                        elif search_type == "قيمة المبلغ" and amount_value:
                            try:
                                if abs(float(amount_value) - float(search_value)) < 0.001:  # مقارنة بدقة عالية
                                    match_found = True
                                    print(f"✅ عثر على مبلغ: {amount_value} في {amount_col}{row}")
                            except (ValueError, TypeError):
                                if str(amount_value).strip() == str(search_value).strip():
                                    match_found = True
                                    print(f"✅ عثر على مبلغ (نص): {amount_value} في {amount_col}{row}")

                        elif search_type == "اسم الحساب":
                            if search_value.lower() in sheet_name.lower():
                                match_found = True
                                print(f"✅ عثر على حساب: {sheet_name}")

                        if match_found:
                            # تحديد رقم الجدول بناءً على الصف
                            table_num = self.determine_table_number(row)

                            result = {
                                'account': sheet_name,
                                'document_num': str(doc_value) if doc_value else '',
                                'amount': self.safe_float_conversion(amount_value),
                                'position': f'{amount_col}{row}',
                                'doc_position': f'{doc_col}{row}',
                                'section': section_name,
                                'table': table_num,
                                'row': row,
                                'amount_col': amount_col,
                                'doc_col': doc_col
                            }

                            results.append(result)
                            print(f"✨ أضيفت نتيجة: {doc_value} - {amount_value} في {amount_col}{row}")

                    except Exception as cell_error:
                        # تجاهل أخطاء الخلايا الفردية
                        continue

            print(f"🏁 انتهى البحث في {sheet_name}. عدد النتائج: {len(results)}")

        except Exception as e:
            print(f"❗ خطأ في البحث في الورقة {sheet_name}: {str(e)}")

        return results

    def is_header_or_label(self, amount_value, doc_value):
        """فحص إذا كانت القيم عبارة عن عناوين أو نصوص وصفية"""
        try:
            # قائمة الكلمات المفتاحية للعناوين
            header_keywords = [
                'المبلغ', 'مبلغ', 'رقم', 'مستند', 'المستند',
                'الحساب', 'حساب', 'الرصيد', 'رصيد',
                'القسم', 'قسم', 'الجدول', 'جدول',
                'الأول', 'الثاني', 'الثالث', 'الرابع',
                'الخامس', 'السادس', 'السابع',
                'مجموع', 'المجموع', 'إجمالي', 'الإجمالي',
                'total', 'sum', 'amount', 'document', 'number'
            ]

            # فحص قيمة المبلغ
            if amount_value:
                amount_str = str(amount_value).strip().lower()
                for keyword in header_keywords:
                    if keyword in amount_str:
                        return True

            # فحص قيمة رقم المستند
            if doc_value:
                doc_str = str(doc_value).strip().lower()
                for keyword in header_keywords:
                    if keyword in doc_str:
                        return True

            return False

        except:
            return False

    def determine_table_number(self, row):
        """تحديد رقم الجدول بناءً على رقم الصف"""
        try:
            # افتراض أن كل جدول يحتوي على حوالي 40 صف
            # الجدول الأول يبدأ من الصف 12 تقريباً
            if row <= 50:
                return 1
            elif row <= 90:
                return 2
            elif row <= 130:
                return 3
            elif row <= 170:
                return 4
            elif row <= 210:
                return 5
            else:
                return ((row - 12) // 40) + 1
        except:
            return 1

    def safe_float_conversion(self, value):
        """تحويل آمن للقيم العددية"""
        try:
            if value is None:
                return 0.0

            # إذا كانت رقماً بالفعل
            if isinstance(value, (int, float)):
                return float(value)

            # إذا كانت نصاً
            value_str = str(value).strip()
            if not value_str:
                return 0.0

            # إزالة الفواصل والرموز غير الرقمية
            cleaned_value = ''.join(c for c in value_str if c.isdigit() or c in '.-')

            if cleaned_value:
                return float(cleaned_value)
            else:
                return 0.0

        except (ValueError, TypeError):
            return 0.0

    def is_table_header_row(self, ws, row):
        """فحص إذا كان هذا صف عناوين جدول"""
        try:
            # فحص وجود عناوين الجدول في الأعمدة الرئيسية
            cell_a = ws[f'A{row}'].value
            cell_b = ws[f'B{row}'].value
            cell_c = ws[f'C{row}'].value
            cell_d = ws[f'D{row}'].value

            # فحص إذا كانت الخلايا تحتوي على عناوين مثل "المبلغ" أو "رقم المستند"
            if (cell_a and ("مبلغ" in str(cell_a) or "المبلغ" in str(cell_a))) or \
               (cell_b and ("مستند" in str(cell_b) or "رقم" in str(cell_b))) or \
               (cell_c and ("مبلغ" in str(cell_c) or "المبلغ" in str(cell_c))) or \
               (cell_d and ("مستند" in str(cell_d) or "رقم" in str(cell_d))):
                return True

            # فحص بديل: إذا كان هناك بيانات في عدة أعمدة متتالية
            if cell_a and cell_b and cell_c and cell_d:
                return True

            return False
        except:
            return False

    def clear_search(self):
        """مسح البحث"""
        self.search_value_var.set("")
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.search_results = []
        self.stats_label.config(text="جاهز للبحث...")

    def refresh_search(self):
        """تحديث البحث"""
        if self.search_value_var.get().strip():
            self.perform_search()

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        # تحديد العنصر المنقور عليه
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.tree.focus(item)
            self.context_menu.post(event.x_root, event.y_root)

    def on_item_double_click(self, event):
        """معالج النقر المزدوج"""
        self.edit_document()

    def get_selected_result(self):
        """الحصول على النتيجة المحددة"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء تحديد مستند من القائمة")
            return None

        item_id = selection[0]

        # البحث عن البيانات المقترنة
        for result in self.search_results:
            if result['item_id'] == item_id:
                return result['data']

        return None

    def edit_document(self):
        """تعديل المستند المحدد"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        # إنشاء نافذة التعديل
        self.show_edit_dialog(selected_data)

    def show_edit_dialog(self, data):
        """عرض نافذة تعديل المستند"""
        edit_window = tk.Toplevel(self)
        edit_window.title("✏️ تعديل المستند")

        # حجم أكبر للنافذة لعرض جميع العناصر
        edit_window.geometry("500x450")
        edit_window.minsize(450, 400)  # حد أدنى للحجم
        edit_window.configure(bg='#f0f8ff')
        edit_window.transient(self)
        edit_window.grab_set()

        # جعل النافذة قابلة لتغيير الحجم
        edit_window.resizable(True, True)

        # توسيط النافذة بعد تحديد الحجم
        edit_window.update_idletasks()
        width = edit_window.winfo_reqwidth()
        height = edit_window.winfo_reqheight()
        x = (edit_window.winfo_screenwidth() // 2) - (width // 2)
        y = (edit_window.winfo_screenheight() // 2) - (height // 2)
        edit_window.geometry(f"{width}x{height}+{x}+{y}")

        # الإطار الرئيسي مع توزيع محسن
        main_frame = ttk.Frame(edit_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # تكوين الشبكة للتوزيع الصحيح
        main_frame.grid_rowconfigure(3, weight=1)  # الصف الأخير يتوسع
        main_frame.grid_columnconfigure(0, weight=1)

        # عنوان بحجم أكبر وأوضح
        title_label = ttk.Label(main_frame, text="✏️ تعديل بيانات المستند",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20), sticky='ew')

        # معلومات الحساب
        info_frame = ttk.LabelFrame(main_frame, text="📁 معلومات الحساب", padding="15")
        info_frame.grid(row=1, column=0, sticky='ew', pady=(0, 15))

        ttk.Label(info_frame, text=f"الحساب: {data['account']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"الموقع: {data['position']} - {data['section']} - الجدول {data['table']}").pack(anchor=tk.W)

        # حقول التعديل
        edit_frame = ttk.LabelFrame(main_frame, text="📝 البيانات الجديدة", padding="15")
        edit_frame.grid(row=2, column=0, sticky='ew', pady=(0, 15))

        # إطار رقم المستند
        doc_frame = ttk.Frame(edit_frame)
        doc_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(doc_frame, text="📄 رقم المستند:",
                 font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        doc_var = tk.StringVar(value=str(data['document_num']) if data['document_num'] else "")
        doc_entry = ttk.Entry(doc_frame, textvariable=doc_var,
                             font=('Arial', 11), width=30)
        doc_entry.pack(fill=tk.X, pady=(5, 0))

        # إطار المبلغ
        amount_frame = ttk.Frame(edit_frame)
        amount_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(amount_frame, text="💰 قيمة المبلغ:",
                 font=('Arial', 10, 'bold')).pack(anchor=tk.W)

        # إطار فرعي للمبلغ مع وحدة القياس
        amount_input_frame = ttk.Frame(amount_frame)
        amount_input_frame.pack(fill=tk.X, pady=(5, 0))

        amount_var = tk.StringVar(value=f"{data['amount']:.3f}" if data['amount'] else "0.000")
        amount_entry = ttk.Entry(amount_input_frame, textvariable=amount_var,
                               font=('Arial', 11), width=20)
        amount_entry.pack(side=tk.LEFT)

        ttk.Label(amount_input_frame, text="دينار",
                 font=('Arial', 10)).pack(side=tk.LEFT, padx=(10, 0))

        # تلميح للمستخدم
        hint_label = ttk.Label(edit_frame,
                              text="💡 تلميح: يمكنك تعديل رقم المستند وقيمة المبلغ",
                              font=('Arial', 9),
                              foreground='gray')
        hint_label.pack(anchor=tk.W, pady=(10, 0))

        # تركيز على حقل رقم المستند
        doc_entry.focus_set()
        doc_entry.select_range(0, tk.END)

        # أزرار في الأسفل
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, sticky='ew', pady=(20, 0))

        # توزيع الأزرار في الوسط
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)

        def save_changes():
            try:
                # الحصول على القيم الجديدة
                new_doc = doc_var.get().strip()
                new_amount_str = amount_var.get().strip()

                # فحص صحة البيانات
                if not new_doc:
                    messagebox.showwarning("تنبيه", "الرجاء إدخال رقم المستند")
                    doc_entry.focus_set()
                    return

                if not new_amount_str:
                    messagebox.showwarning("تنبيه", "الرجاء إدخال قيمة المبلغ")
                    amount_entry.focus_set()
                    return

                # تحويل المبلغ إلى رقم
                try:
                    new_amount = float(new_amount_str)
                    if new_amount < 0:
                        messagebox.showwarning("تنبيه", "قيمة المبلغ يجب أن تكون موجبة")
                        amount_entry.focus_set()
                        return
                except ValueError:
                    messagebox.showerror("خطأ", "الرجاء إدخال قيمة رقمية صحيحة للمبلغ")
                    amount_entry.focus_set()
                    amount_entry.select_range(0, tk.END)
                    return

                # عرض معلومات التحديث
                old_doc = data['document_num']
                old_amount = data['amount']

                changes_text = f"هل أنت متأكد من حفظ التغييرات؟\n\n"
                changes_text += f"رقم المستند:\n"
                changes_text += f"  من: {old_doc} → إلى: {new_doc}\n\n"
                changes_text += f"قيمة المبلغ:\n"
                changes_text += f"  من: {old_amount:.3f} → إلى: {new_amount:.3f}"

                if not messagebox.askyesno("تأكيد التحديث", changes_text):
                    return

                # تنفيذ التحديث
                print(f"🔄 بدء تحديث المستند...")
                print(f"📄 رقم المستند: {old_doc} → {new_doc}")
                print(f"💰 قيمة المبلغ: {old_amount:.3f} → {new_amount:.3f}")

                if self.update_document_in_file(data, new_doc, new_amount):
                    messagebox.showinfo("✅ نجح",
                                       f"تم تحديث المستند بنجاح!\n\n"
                                       f"رقم المستند: {new_doc}\n"
                                       f"قيمة المبلغ: {new_amount:.3f} دينار")
                    edit_window.destroy()
                    self.refresh_search()  # تحديث النتائج
                else:
                    messagebox.showerror("❌ خطأ", "فشل في تحديث المستند")

            except Exception as e:
                print(f"❗ خطأ في حفظ التغييرات: {str(e)}")
                messagebox.showerror("خطأ", f"خطأ غير متوقع في التحديث:\n{str(e)}")

        # أزرار بحجم أكبر وموزعة في الوسط
        save_btn = ttk.Button(buttons_frame, text="💾 حفظ التغييرات",
                             command=save_changes,
                             style='Accent.TButton')
        save_btn.grid(row=0, column=0, padx=(0, 10), pady=10, sticky='ew')

        cancel_btn = ttk.Button(buttons_frame, text="❌ إلغاء",
                               command=edit_window.destroy)
        cancel_btn.grid(row=0, column=1, padx=(10, 0), pady=10, sticky='ew')

        # جعل زر الحفظ هو الافتراضي
        save_btn.focus_set()
        edit_window.bind('<Return>', lambda e: save_changes())
        edit_window.bind('<Escape>', lambda e: edit_window.destroy())

    def update_document_in_file(self, data, new_doc, new_amount):
        """تحديث المستند في الملف مع الحفاظ على التنسيق"""
        try:
            deductions_file = "Accounting system deductions.xlsx"

            # فحص إمكانية الوصول للملف
            if not self.check_file_access(deductions_file):
                return False

            workbook = openpyxl.load_workbook(deductions_file)

            if data['account'] not in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{data['account']}' غير موجود")
                return False

            ws = workbook[data['account']]

            print(f"🔄 بدء تحديث المستند في {data['account']}")
            print(f"📍 الموقع: {data['position']} - {data['doc_position']}")
            print(f"🔄 القيم الجديدة: {new_doc} - {new_amount}")

            # تحديث البيانات مع الحفاظ على التنسيق
            self.update_cell_with_formatting(ws, data['position'], new_amount, 'amount')
            self.update_cell_with_formatting(ws, data['doc_position'], new_doc, 'document')

            # حفظ الملف
            workbook.save(deductions_file)
            workbook.close()

            print(f"✅ تم تحديث المستند بنجاح")
            return True

        except Exception as e:
            print(f"❗ خطأ في تحديث المستند: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في تحديث المستند: {str(e)}")
            return False

    def delete_document(self):
        """حذف المستند المحدد"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف المستند؟\n\n"
                              f"الحساب: {selected_data['account']}\n"
                              f"رقم المستند: {selected_data['document_num']}\n"
                              f"المبلغ: {selected_data['amount']:.3f}\n"
                              f"الموقع: {selected_data['position']}"):

            if self.delete_document_from_file(selected_data):
                messagebox.showinfo("نجح", "تم حذف المستند بنجاح")
                self.refresh_search()  # تحديث النتائج
            else:
                messagebox.showerror("خطأ", "فشل في حذف المستند")

    def delete_document_from_file(self, data):
        """حذف المستند من الملف مع إزاحة البيانات تلقائياً"""
        try:
            deductions_file = "Accounting system deductions.xlsx"

            # فحص إمكانية الوصول للملف
            if not self.check_file_access(deductions_file):
                return False

            workbook = openpyxl.load_workbook(deductions_file)

            if data['account'] not in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{data['account']}' غير موجود")
                return False

            ws = workbook[data['account']]

            print(f"🗑️ بدء حذف المستند من {data['account']}")
            print(f"📍 الموقع: {data['position']} - {data['doc_position']}")
            print(f"📄 البيانات: {data['document_num']} - {data['amount']}")

            # حذف المستند مع إزاحة البيانات
            success = self.delete_and_shift_data(ws, data)

            if success:
                # حفظ الملف
                workbook.save(deductions_file)
                workbook.close()

                print(f"✅ تم حذف المستند وإزاحة البيانات بنجاح")
                return True
            else:
                workbook.close()
                return False

        except Exception as e:
            print(f"❗ خطأ في حذف المستند: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في حذف المستند: {str(e)}")
            return False

    def copy_document_number(self):
        """نسخ رقم المستند"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        self.clipboard_clear()
        self.clipboard_append(str(selected_data['document_num']))
        messagebox.showinfo("تم", f"تم نسخ رقم المستند: {selected_data['document_num']}")

    def show_account_details(self):
        """عرض تفاصيل الحساب"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        # يمكن تطوير هذه الدالة لاحقاً لعرض تفاصيل أكثر
        messagebox.showinfo("تفاصيل الحساب",
                           f"اسم الحساب: {selected_data['account']}\n"
                           f"الموقع: {selected_data['position']}\n"
                           f"القسم: {selected_data['section']}\n"
                           f"الجدول: {selected_data['table']}")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """معالج إغلاق النافذة"""
        try:
            # إزالة النافذة من قائمة النوافذ المفتوحة
            if hasattr(self.parent, 'close_window'):
                self.parent.close_window('receipts_search', self)
        except:
            pass

        self.destroy()

    # =====================================================================================
    # الدوال المساعدة الجديدة للتعديل والحذف المتقدم
    # =====================================================================================

    def check_file_access(self, file_path):
        """فحص إمكانية الوصول للملف للكتابة"""
        try:
            # محاولة فتح الملف للكتابة
            with open(file_path, "r+b"):
                pass
            return True
        except PermissionError:
            messagebox.showerror("خطأ",
                               f"الملف مفتوح في برنامج آخر.\n"
                               f"يرجى إغلاق Excel أو أي برنامج آخر يستخدم الملف.")
            return False
        except FileNotFoundError:
            messagebox.showerror("خطأ", f"الملف غير موجود: {file_path}")
            return False
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الوصول للملف: {str(e)}")
            return False

    def update_cell_with_formatting(self, ws, cell_address, value, cell_type):
        """تحديث خلية مع الحفاظ على التنسيق"""
        try:
            from openpyxl.styles import Font, Alignment, PatternFill, Border

            cell = ws[cell_address]

            # إنشاء نسخ من التنسيق الحالي لتجنب مشكلة StyleProxy
            try:
                original_font = Font(
                    name=cell.font.name,
                    size=cell.font.size,
                    bold=cell.font.bold,
                    italic=cell.font.italic,
                    color=cell.font.color
                )
            except:
                original_font = Font()

            try:
                original_alignment = Alignment(
                    horizontal=cell.alignment.horizontal,
                    vertical=cell.alignment.vertical,
                    wrap_text=cell.alignment.wrap_text
                )
            except:
                original_alignment = Alignment()

            try:
                original_fill = PatternFill(
                    fill_type=cell.fill.fill_type,
                    start_color=cell.fill.start_color,
                    end_color=cell.fill.end_color
                )
            except:
                original_fill = PatternFill()

            try:
                original_border = Border(
                    left=cell.border.left,
                    right=cell.border.right,
                    top=cell.border.top,
                    bottom=cell.border.bottom
                )
            except:
                original_border = Border()

            # تحديث القيمة
            if cell_type == 'amount':
                cell.value = float(value) if value else None
                cell.number_format = '0.000'
            else:
                cell.value = str(value) if value else None
                cell.number_format = 'General'

            # استعادة التنسيق الأصلي
            cell.font = original_font
            cell.alignment = original_alignment
            cell.fill = original_fill
            cell.border = original_border

            print(f"✨ تم تحديث الخلية {cell_address} بالقيمة: {value}")

        except Exception as e:
            print(f"❗ خطأ في تحديث الخلية {cell_address}: {str(e)}")
            # في حالة فشل التنسيق، قم بتحديث القيمة فقط
            try:
                cell = ws[cell_address]
                if cell_type == 'amount':
                    cell.value = float(value) if value else None
                    cell.number_format = '0.000'
                else:
                    cell.value = str(value) if value else None
                    cell.number_format = 'General'
                print(f"✅ تم تحديث الخلية {cell_address} بالقيمة فقط: {value}")
            except Exception as e2:
                print(f"❗ خطأ نهائي في تحديث الخلية {cell_address}: {str(e2)}")
                raise e2

    def delete_and_shift_data(self, ws, data):
        """حذف المستند وإزاحة البيانات تلقائياً"""
        try:
            amount_col = data['amount_col']
            doc_col = data['doc_col']
            delete_row = data['row']

            print(f"🔄 بدء عملية الإزاحة في العمودين {amount_col} و {doc_col}")

            # العثور على جميع البيانات في هذا القسم بعد الصف المحذوف
            section_data = self.get_section_data(ws, amount_col, doc_col, delete_row)

            # حذف البيانات المحددة
            ws[f'{amount_col}{delete_row}'] = None
            ws[f'{doc_col}{delete_row}'] = None

            # إزاحة البيانات لأعلى
            self.shift_data_up(ws, amount_col, doc_col, delete_row, section_data)

            print(f"✅ تمت عملية الإزاحة بنجاح")
            return True

        except Exception as e:
            print(f"❗ خطأ في عملية الإزاحة: {str(e)}")
            return False

    def get_section_data(self, ws, amount_col, doc_col, start_row):
        """جمع جميع البيانات في القسم بعد الصف المحدد"""
        section_data = []

        try:
            # البحث في جميع الصفوف بعد الصف المحذوف
            for row in range(start_row + 1, ws.max_row + 1):
                amount_value = ws[f'{amount_col}{row}'].value
                doc_value = ws[f'{doc_col}{row}'].value

                # إذا وجدت بيانات، أضفها للقائمة
                if amount_value is not None or doc_value is not None:
                    # تجاهل العناوين
                    if not self.is_header_or_label(amount_value, doc_value):
                        section_data.append({
                            'row': row,
                            'amount': amount_value,
                            'document': doc_value
                        })

            print(f"📁 تم جمع {len(section_data)} عنصر بيانات للإزاحة")
            return section_data

        except Exception as e:
            print(f"❗ خطأ في جمع بيانات القسم: {str(e)}")
            return []

    def shift_data_up(self, ws, amount_col, doc_col, start_row, section_data):
        """إزاحة البيانات لأعلى لملء الفراغ"""
        try:
            current_row = start_row

            # نقل كل عنصر بيانات صف واحد لأعلى
            for data_item in section_data:
                # نسخ البيانات مع التنسيق
                self.copy_cell_with_formatting(ws, f'{amount_col}{data_item["row"]}', f'{amount_col}{current_row}')
                self.copy_cell_with_formatting(ws, f'{doc_col}{data_item["row"]}', f'{doc_col}{current_row}')

                # مسح الخلية الأصلية
                ws[f'{amount_col}{data_item["row"]}'] = None
                ws[f'{doc_col}{data_item["row"]}'] = None

                current_row += 1

                print(f"➡️ تم نقل البيانات من الصف {data_item['row']} إلى الصف {current_row-1}")

            print(f"✅ تمت عملية الإزاحة بنجاح")

        except Exception as e:
            print(f"❗ خطأ في عملية الإزاحة: {str(e)}")

    def copy_cell_with_formatting(self, ws, source_address, target_address):
        """نسخ خلية مع جميع التنسيقات"""
        try:
            source_cell = ws[source_address]
            target_cell = ws[target_address]

            # نسخ القيمة
            target_cell.value = source_cell.value

            # نسخ التنسيق
            if source_cell.has_style:
                target_cell.font = source_cell.font
                target_cell.border = source_cell.border
                target_cell.fill = source_cell.fill
                target_cell.number_format = source_cell.number_format
                target_cell.protection = source_cell.protection
                target_cell.alignment = source_cell.alignment

        except Exception as e:
            print(f"❗ خطأ في نسخ الخلية: {str(e)}")
