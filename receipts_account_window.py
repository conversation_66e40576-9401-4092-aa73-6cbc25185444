import tkinter as tk
from tkinter import ttk, messagebox
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from datetime import datetime
import os

class AddReceiptsAccountWindow(tk.Toplevel):
    """نافذة إضافة حساب مقبوضات"""

    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("إضافة حساب مقبوضات")
        self.parent = parent

        # تكوين النافذة
        self.geometry("500x650")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent.root)
        self.grab_set()

        # متغيرات النموذج
        self.account_num_var = tk.StringVar()
        self.account_name_var = tk.StringVar()
        self.initial_balance_var = tk.StringVar(value="0")
        self.format_var = tk.StringVar(value="receipts")
        self.format_var = tk.StringVar(value="receipts")

        # إنشاء الواجهة
        self.create_interface()

        # توسيط النافذة
        self.center_window()

        # ربط إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # عنوان النافذة
        title_label = ttk.Label(main_frame,
                               text="💰 إضافة حساب مقبوضات",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))

        # إطار بيانات الحساب
        data_frame = ttk.LabelFrame(main_frame,
                                   text="📋 بيانات الحساب",
                                   padding="20")
        data_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        data_frame.columnconfigure(1, weight=1)

        # رقم الحساب
        ttk.Label(data_frame, text="🔢 رقم الحساب:", font=('Arial', 10, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 10), padx=(0, 10))

        account_num_entry = ttk.Entry(data_frame, textvariable=self.account_num_var,
                                     font=('Arial', 12), width=30)
        account_num_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        account_num_entry.focus()

        # اسم الحساب
        ttk.Label(data_frame, text="📝 اسم الحساب:", font=('Arial', 10, 'bold')).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 10), padx=(0, 10))

        account_name_entry = ttk.Entry(data_frame, textvariable=self.account_name_var,
                                      font=('Arial', 12), width=30)
        account_name_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10))

        # الرصيد الافتتاحي
        ttk.Label(data_frame, text="💰 الرصيد الافتتاحي:", font=('Arial', 10, 'bold')).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 10), padx=(0, 10))

        balance_entry = ttk.Entry(data_frame, textvariable=self.initial_balance_var,
                                 font=('Arial', 12), width=30)
        balance_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 10))

        # إعداد التنقل بزر الإدخال
        self.setup_enter_navigation(account_num_entry, account_name_entry, balance_entry)

        # ملاحظة حول التنسيق
        note_frame = ttk.LabelFrame(main_frame,
                                   text="📝 ملاحظة مهمة",
                                   padding="15")
        note_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        note_text = (
            "ℹ️ سيتم إضافة الحساب إلى ملف الخصومات\n"
            "📂 Accounting system deductions.xlsx\n"
            "🎨 بتنسيق خاص لجدول تصنيف المقبوضات"
        )

        ttk.Label(note_frame, text=note_text,
                 font=('Arial', 9),
                 foreground='#2c3e50',
                 justify=tk.CENTER).grid(row=0, column=0)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # زر الإضافة
        add_btn = ttk.Button(buttons_frame,
                            text="➕ إضافة الحساب",
                            command=self.add_account,
                            style='Accent.TButton')
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر المسح
        clear_btn = ttk.Button(buttons_frame,
                              text="🗑️ مسح الحقول",
                              command=self.clear_fields)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر المساعدة
        help_btn = ttk.Button(buttons_frame,
                             text="❓ مساعدة",
                             command=self.show_help)
        help_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # زر الإغلاق
        close_btn = ttk.Button(buttons_frame,
                              text="❌ إغلاق",
                              command=self.on_closing)
        close_btn.pack(side=tk.RIGHT)

        # إعداد التنقل بزر الإدخال
        self.setup_enter_navigation(account_num_entry, account_name_entry, balance_entry)

        # تركيز على أول حقل
        account_num_entry.focus()

    def setup_enter_navigation(self, *entries):
        """إعداد التنقل بين الحقول بزر الإدخال"""
        def focus_next_widget(event, current_index):
            if current_index < len(entries) - 1:
                entries[current_index + 1].focus()
            else:
                # في آخر حقل، تنفيذ عملية الحفظ
                self.add_account()
            return "break"

        for i, entry in enumerate(entries):
            entry.bind('<Return>', lambda event, idx=i: focus_next_widget(event, idx))

    def add_account(self):
        """إضافة حساب جديد إلى ملف الخصومات"""
        try:
            # فحص البيانات
            account_num = self.account_num_var.get().strip()
            account_name = self.account_name_var.get().strip()
            initial_balance = self.initial_balance_var.get().strip()

            if not account_num:
                messagebox.showerror("خطأ", "يجب إدخال رقم الحساب")
                return

            if not account_name:
                messagebox.showerror("خطأ", "يجب إدخال اسم الحساب")
                return

            # تحويل الرصيد إلى رقم
            try:
                balance = float(initial_balance) if initial_balance else 0
            except ValueError:
                messagebox.showerror("خطأ", "الرصيد الافتتاحي يجب أن يكون رقماً")
                return

            # فحص وجود ملف الخصومات
            deductions_file = "Accounting system deductions.xlsx"
            if not os.path.exists(deductions_file):
                messagebox.showerror("خطأ", f"ملف الخصومات غير موجود: {deductions_file}")
                return

            # فحص إمكانية الوصول للملف
            if not self.check_file_access(deductions_file):
                return

            # إنشاء الحساب بالتنسيق الجديد
            success = self.create_receipts_account_sheet(deductions_file, account_num, account_name, balance)

            if success:
                messagebox.showinfo("نجاح", f"تم إضافة حساب '{account_name}' بنجاح إلى ملف الخصومات")
                self.clear_fields()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الحساب: {str(e)}")

    def create_receipts_account_sheet(self, file_path, account_num, account_name, initial_balance):
        """إنشاء صفحة حساب بتنسيق المقبوضات"""
        try:
            # تحميل الملف
            workbook = openpyxl.load_workbook(file_path)

            # إنشاء اسم الورقة
            sheet_name = f"{account_num} - {account_name}"

            # فحص وجود الورقة
            if sheet_name in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{sheet_name}' موجود بالفعل")
                return False

            # إنشاء ورقة جديدة
            ws = workbook.create_sheet(sheet_name)
            ws.sheet_properties.rightToLeft = True

            # إعداد التنسيق الجديد
            self.setup_receipts_format(ws, account_num, account_name, initial_balance)

            # حفظ الملف
            workbook.save(file_path)

            return True

        except PermissionError as e:
            error_msg = (
                f"لا يمكن حفظ الملف: {file_path}\n\n"
                "السبب المحتمل:\n"
                "• الملف مفتوح في Excel أو برنامج آخر\n"
                "• لا توجد صلاحية للكتابة\n\n"
                "الحلول:\n"
                "• أغلق الملف في Excel وحاول مرة أخرى\n"
                "• تأكد من صلاحيات الملف"
            )
            messagebox.showerror("خطأ في الوصول للملف", error_msg)
            print(f"خطأ في الوصول: {str(e)}")
            return False

        except FileNotFoundError as e:
            error_msg = (
                f"لم يتم العثور على الملف: {file_path}\n\n"
                "الحل:\n"
                "• تأكد من وجود الملف\n"
                "• أعد تشغيل النظام لإنشاء الملف"
            )
            messagebox.showerror("ملف غير موجود", error_msg)
            print(f"ملف غير موجود: {str(e)}")
            return False

        except Exception as e:
            error_msg = (
                f"حدث خطأ غير متوقع عند إنشاء الحساب:\n\n"
                f"تفاصيل الخطأ: {str(e)}\n\n"
                "الحلول:\n"
                "• تأكد من صحة بيانات الحساب\n"
                "• حاول مرة أخرى بعد قليل\n"
                "• أعد تشغيل النظام"
            )
            messagebox.showerror("خطأ في إنشاء الحساب", error_msg)
            print(f"خطأ في إنشاء صفحة الحساب: {str(e)}")
            return False

    def setup_receipts_format(self, ws, account_num, account_name, initial_balance):
        """إعداد تنسيق حساب المقبوضات - التنسيق المحدث مع المجاميع التلقائية"""
        # إعداد التنسيقات المحسنة (بدون ألوان)
        title_font = Font(size=14, bold=True)  # عناوين رئيسية
        header_font = Font(size=11, bold=True)  # عناوين فرعية
        totals_font = Font(size=11, bold=True)  # إجماليات
        normal_font = Font(size=10)

        # محاذاة وتنسيق (من اليمين إلى اليسار)
        center_alignment = Alignment(horizontal='center', vertical='center')
        right_alignment = Alignment(horizontal='right', vertical='center')

        # الحدود المختلفة
        thick_border = Border(
            left=Side(style='thick', color='000000'),
            right=Side(style='thick', color='000000'),
            top=Side(style='thick', color='000000'),
            bottom=Side(style='thick', color='000000')
        )

        medium_border = Border(
            left=Side(style='medium', color='000000'),
            right=Side(style='medium', color='000000'),
            top=Side(style='medium', color='000000'),
            bottom=Side(style='medium', color='000000')
        )

        thin_border = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )

        # بدون ألوان - فقط حدود وتنسيق

        # إعداد اتجاه الورقة من اليمين إلى اليسار
        ws.sheet_properties.rightToLeft = True

        # الصف 6: رأس الوزارة مع تنسيق جميل
        ws['A6'] = 'الوزارة / الدائرة'
        ws['A6'].font = header_font
        ws['A6'].border = thick_border
        ws['A6'].alignment = center_alignment

        ws['B6'] = 'الصحة'
        ws['B6'].font = header_font
        ws['B6'].border = thick_border
        ws['B6'].alignment = center_alignment

        ws['C6'] = 'التأمين الصحي'
        ws['C6'].font = header_font
        ws['C6'].border = thick_border
        ws['C6'].alignment = center_alignment

        # الصف 7: عنوان دفتر الأستاذ
        ws.merge_cells('A7:N7')
        ws['A7'] = 'دفتر أستاذ الحسابات الوسيطة المساعد'
        ws['A7'].font = title_font
        ws['A7'].border = thick_border
        ws['A7'].alignment = center_alignment

        # الصف 8: شهر وسنة وجدول تصنيف المقبوضات
        ws['A8'] = 'شهر'
        ws['A8'].font = header_font
        ws['A8'].border = medium_border
        ws['A8'].alignment = center_alignment

        ws['C8'] = 'سنة'
        ws['C8'].font = header_font

        ws['C8'].border = medium_border
        ws['C8'].alignment = center_alignment

        ws.merge_cells('F8:N8')
        ws['F8'] = 'جدول تصنيف المقبوضات'
        ws['F8'].font = title_font

        ws['F8'].border = thick_border
        ws['F8'].alignment = center_alignment

        # الصف 10: اسم الحساب مع خلفية زرقاء فاتحة
        ws.merge_cells('A10:N10')
        ws['A10'] = f'اسم الحساب: {account_name}'
        ws['A10'].font = header_font
        ws['A10'].border = thick_border
        ws['A10'].alignment = center_alignment

        # الصف 11: عناوين الأعمدة مع خلفية زرقاء فاتحة
        # تعريف الأعمدة: A,B | C,D | E,F | G,H | I,J | K,L | M,N
        columns_pairs = [
            ('A', 'B'), ('C', 'D'), ('E', 'F'), ('G', 'H'),
            ('I', 'J'), ('K', 'L'), ('M', 'N')
        ]

        for amount_col, doc_col in columns_pairs:
            # عمود المبلغ
            ws[f'{amount_col}11'] = 'المبلغ'
            ws[f'{amount_col}11'].font = header_font
            ws[f'{amount_col}11'].border = thick_border
            ws[f'{amount_col}11'].alignment = center_alignment

            # عمود رقم المستند
            ws[f'{doc_col}11'] = 'رقم المستند'
            ws[f'{doc_col}11'].font = header_font
            ws[f'{doc_col}11'].border = thick_border
            ws[f'{doc_col}11'].alignment = center_alignment

        # الصف 12: صف الرصيد المرحل (ما قبله) مع تنسيق ثلاث خانات عشرية
        ws['A12'] = '0'
        ws['A12'].number_format = '0.000'
        ws['B12'] = 'ما قبله'

        # إضافة صيغ المجاميع للأعمدة الأخرى (ترحيل تلقائي) مع تنسيق ثلاث خانات عشرية
        ws['C12'] = '=A34'
        ws['C12'].number_format = '0.000'
        ws['D12'] = ''
        ws['E12'] = '=C34'
        ws['E12'].number_format = '0.000'
        ws['F12'] = 'ما قبله'
        ws['G12'] = '=E34'
        ws['G12'].number_format = '0.000'
        ws['H12'] = 'ما قبله'
        ws['I12'] = '=G34'
        ws['I12'].number_format = '0.000'
        ws['J12'] = 'ما قبله'
        ws['K12'] = '=I34'
        ws['K12'].number_format = '0.000'
        ws['L12'] = 'ما قبله'
        ws['M12'] = '=K34'
        ws['M12'].number_format = '0.000'
        ws['N12'] = 'ما قبله'

        # إضافة 20 صف للبيانات (من 13 إلى 33) مع تنسيق ثلاث خانات عشرية
        for row in range(13, 34):
            for amount_col, doc_col in columns_pairs:
                # إضافة حدود للخلايا
                ws[f'{amount_col}{row}'].border = thin_border
                ws[f'{doc_col}{row}'].border = thin_border
                ws[f'{amount_col}{row}'].alignment = center_alignment
                ws[f'{doc_col}{row}'].alignment = center_alignment

                # تنسيق عمود المبلغ بثلاث خانات عشرية
                ws[f'{amount_col}{row}'].number_format = '0.000'

        # الصف 34: صف المجاميع الجديد
        ws['A34'] = '=SUM(A12:A33)'
        ws['B34'] = '=COUNTA(B13:B33)'
        ws['C34'] = '=SUM(C12:C33)'
        ws['D34'] = '=COUNTA(D13:D33)'
        ws['E34'] = '=SUM(E12:E33)'
        ws['F34'] = '=COUNTA(F13:F33)'
        ws['G34'] = '=SUM(G12:G33)'
        ws['H34'] = '=COUNTA(H13:H33)'
        ws['I34'] = '=SUM(I12:I33)'
        ws['J34'] = '=COUNTA(J13:J33)'
        ws['K34'] = '=SUM(K12:K33)'
        ws['L34'] = '=COUNTA(L13:L33)'
        ws['M34'] = '=SUM(M12:M33)'
        ws['N34'] = '=COUNTA(N13:N33)'

        # تنسيق صف المجاميع بحدود غامقة وثلاث خانات عشرية
        for amount_col, doc_col in columns_pairs:
            # تنسيق عمود المبلغ (الإجمالي)
            ws[f'{amount_col}34'].font = totals_font
            ws[f'{amount_col}34'].border = thick_border
            ws[f'{amount_col}34'].alignment = center_alignment
            ws[f'{amount_col}34'].number_format = '0.000'  # ثلاث خانات عشرية

            # تنسيق عمود عدد المستندات (COUNTA)
            ws[f'{doc_col}34'].font = totals_font
            ws[f'{doc_col}34'].border = thick_border
            ws[f'{doc_col}34'].alignment = center_alignment

        # الصف 36: التاريخ والتوقيع مع تنسيق جميل
        ws['A36'] = 'التاريخ:'
        ws['A36'].font = header_font

        ws['A36'].border = medium_border
        ws['A36'].alignment = center_alignment

        ws.merge_cells('I36:N36')
        ws['I36'] = 'اسم وتوقيع المحاسب'
        ws['I36'].font = header_font

        ws['I36'].border = medium_border
        ws['I36'].alignment = center_alignment

        # إضافة معلومات الحساب في الأعلى مع تنسيق جميل
        ws['A1'] = f'رقم الحساب: {account_num}'
        ws['A1'].font = Font(size=12, bold=True, color='000080')  # أزرق غامق
        ws['A1'].fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
        ws['A1'].border = medium_border

        ws['A2'] = f'اسم الحساب: {account_name}'
        ws['A2'].font = Font(size=12, bold=True, color='000080')
        ws['A2'].fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
        ws['A2'].border = medium_border

        ws['A3'] = f'الرصيد الافتتاحي: {initial_balance}'
        ws['A3'].font = Font(size=12, bold=True, color='000080')
        ws['A3'].fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
        ws['A3'].border = medium_border

        # تعيين عرض الأعمدة لتحسين العرض
        ws.column_dimensions['A'].width = 12
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 12
        ws.column_dimensions['D'].width = 12
        ws.column_dimensions['E'].width = 12
        ws.column_dimensions['F'].width = 12
        ws.column_dimensions['G'].width = 12
        ws.column_dimensions['H'].width = 12
        ws.column_dimensions['I'].width = 12
        ws.column_dimensions['J'].width = 12
        ws.column_dimensions['K'].width = 12
        ws.column_dimensions['L'].width = 12
        ws.column_dimensions['M'].width = 12
        ws.column_dimensions['N'].width = 12

    def check_file_access(self, file_path):
        """فحص إمكانية الوصول للملف"""
        try:
            # محاولة فتح الملف للقراءة والكتابة
            with open(file_path, 'r+b') as f:
                pass
            return True

        except PermissionError:
            error_msg = (
                f"الملف مفتوح في برنامج آخر!\n\n"
                f"الملف: {file_path}\n\n"
                "الحل:\n"
                "• أغلق الملف في Excel\n"
                "• أغلق أي برنامج يستخدم الملف\n"
                "• حاول مرة أخرى"
            )
            messagebox.showerror("ملف مفتوح في برنامج آخر", error_msg)
            return False

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فحص الملف: {str(e)}")
            return False

    def clear_fields(self):
        """مسح جميع الحقول"""
        self.account_num_var.set("")
        self.account_name_var.set("")
        self.initial_balance_var.set("0")

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = (
            "📚 مساعدة - نافذة إضافة حساب مقبوضات\n\n"
            "🔹 هذه النافذة لإضافة حسابات إلى ملف الخصومات\n"
            "🔹 التنسيق يتضمن 7 أعمدة لأسماء الحسابات\n"
            "🔹 رأس الوزارة وتخطيط دفتر الأستاذ\n"
            "🔹 صيغ خلايا محددة للإجماليات\n\n"
            "💡 للمساعدة الإضافية، تواصل مع المطور"
        )
        messagebox.showinfo("مساعدة", help_text)

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def setup_enter_navigation(self, *entries):
        """إعداد التنقل بزر الإدخال"""
        def focus_next_widget(event):
            event.widget.tk_focusNext().focus()
            return "break"

        for entry in entries:
            entry.bind('<Return>', focus_next_widget)

        # ربط آخر حقل بزر الإضافة
        if entries:
            entries[-1].bind('<Return>', lambda e: self.add_account())

    def on_closing(self):
        """معالج إغلاق النافذة"""
        self.destroy()
