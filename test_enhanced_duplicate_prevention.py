#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار آلية منع التكرار المحسنة في نافذة إضافة المستند
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def test_enhanced_duplicate_prevention():
    """اختبار شامل لآلية منع التكرار المحسنة"""
    try:
        print("\n" + "=" * 80)
        print("🧪 اختبار آلية منع التكرار المحسنة في نافذة إضافة المستند")
        print("=" * 80)
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        print("✅ تم إنشاء ExcelManager بنجاح")
        
        # اختبار الدوال الجديدة
        print("\n📋 اختبار دوال فحص التكرار الجديدة:")
        
        # 1. اختبار فحص تكرار رقم المستند عالمياً
        print("1️⃣ اختبار فحص تكرار رقم المستند عالمياً...")
        test_doc_num = "TEST001"
        duplicate_account = excel.check_document_number_globally(test_doc_num)
        if duplicate_account:
            print(f"   ⚠️ رقم المستند {test_doc_num} موجود في الحساب: {duplicate_account}")
        else:
            print(f"   ✅ رقم المستند {test_doc_num} غير مكرر")
        
        # 2. اختبار فحص تكرار رقم التأدية محلياً
        accounts = excel.get_all_accounts()
        if accounts:
            test_account = accounts[0]['sheet_name']
            test_pay_num = "PAY001"
            print(f"2️⃣ اختبار فحص تكرار رقم التأدية في الحساب: {test_account}")
            is_duplicate = excel.check_payment_number_in_account(test_account, test_pay_num)
            if is_duplicate:
                print(f"   ⚠️ رقم التأدية {test_pay_num} مكرر في نفس الحساب")
            else:
                print(f"   ✅ رقم التأدية {test_pay_num} غير مكرر في هذا الحساب")
        
        print("\n🎉 تم اختبار دوال فحص التكرار بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_document_window_with_duplicate_prevention():
    """اختبار نافذة إضافة المستند مع آلية منع التكرار"""
    try:
        print("\n" + "=" * 80)
        print("🧪 اختبار نافذة إضافة المستند مع آلية منع التكرار المحسنة")
        print("=" * 80)
        
        # إنشاء نافذة رئيسية للاختبار
        root = tk.Tk()
        root.title("اختبار نافذة إضافة المستند المحسنة")
        root.geometry("500x400")
        root.configure(bg='#f8f9fa')
        
        # إنشاء excel_manager
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء كائن وهمي للتطبيق الرئيسي
        class MockApp:
            def __init__(self):
                self.root = root
                self.excel = excel
        
        mock_app = MockApp()
        
        # إنشاء إطار للمحتوى
        main_frame = tk.Frame(root, bg='#f8f9fa')
        main_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(main_frame, text="🧪 اختبار نافذة إضافة المستند المحسنة",
                              font=('Arial', 14, 'bold'), bg='#f8f9fa')
        title_label.pack(pady=(0, 20))
        
        # معلومات القواعد الجديدة
        rules_text = """
📋 قواعد منع التكرار الجديدة:

🚫 رقم المستند:
   • لا يجوز تكراره في جميع الحسابات (عالمياً)
   • فحص شامل لجميع الحسابات

🚫 رقم التأدية:
   • لا يجوز تكراره في نفس الحساب (محلياً)
   • يمكن تكراره في حسابات مختلفة

✅ رسائل خطأ واضحة ومفصلة
✅ فحص تلقائي قبل إضافة المستند
        """
        
        rules_label = tk.Label(main_frame, text=rules_text,
                              font=('Arial', 10), bg='#f8f9fa', fg='#333',
                              justify=tk.LEFT)
        rules_label.pack(pady=(0, 20))
        
        # زر اختبار نافذة إضافة المستند
        def test_add_document_window():
            try:
                from document_window import AddDocumentWindow
                AddDocumentWindow(mock_app)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إضافة المستند: {str(e)}")
        
        tk.Button(main_frame, text="📄 اختبار نافذة إضافة المستند المحسنة",
                 command=test_add_document_window,
                 font=('Arial', 12), bg='#007bff', fg='white',
                 padx=20, pady=10).pack(pady=10, fill=tk.X)
        
        # زر اختبار دوال فحص التكرار
        def test_duplicate_functions():
            try:
                test_enhanced_duplicate_prevention()
                messagebox.showinfo("نجاح", "تم اختبار دوال فحص التكرار بنجاح!\nراجع وحدة التحكم للتفاصيل.")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في اختبار دوال فحص التكرار: {str(e)}")
        
        tk.Button(main_frame, text="🔍 اختبار دوال فحص التكرار",
                 command=test_duplicate_functions,
                 font=('Arial', 12), bg='#28a745', fg='white',
                 padx=20, pady=10).pack(pady=10, fill=tk.X)
        
        # زر إغلاق
        tk.Button(main_frame, text="❌ إغلاق",
                 command=root.destroy,
                 font=('Arial', 12), bg='#dc3545', fg='white',
                 padx=20, pady=10).pack(pady=(20, 0), fill=tk.X)
        
        # معلومات إضافية
        info_text = """
🎯 كيفية الاختبار:
1. افتح نافذة إضافة المستند
2. جرب إدخال رقم مستند موجود مسبقاً
3. جرب إدخال رقم تأدية مكرر في نفس الحساب
4. لاحظ رسائل الخطأ المفصلة والواضحة

💡 نصائح:
• استخدم أرقام مستندات وتأدية مختلفة للاختبار
• جرب الحسابات المختلفة لفهم الفرق بين القواعد
• راجع وحدة التحكم لرسائل التشخيص التفصيلية
        """
        
        info_label = tk.Label(main_frame, text=info_text,
                             font=('Arial', 9), bg='#f8f9fa', fg='#666',
                             justify=tk.LEFT)
        info_label.pack(pady=(10, 0))
        
        print("\n🖥️ عرض نافذة الاختبار...")
        print("   يمكنك الآن اختبار آلية منع التكرار المحسنة")
        print("   أغلق النافذة عند الانتهاء من الاختبار")
        
        # تشغيل النافذة
        root.mainloop()
        
        print("\n🎉 تم اختبار نافذة إضافة المستند المحسنة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_duplicate_rules():
    """عرض توضيحي لقواعد منع التكرار"""
    try:
        print("\n" + "=" * 80)
        print("📚 عرض توضيحي لقواعد منع التكرار الجديدة")
        print("=" * 80)
        
        print("""
🎯 القواعد الجديدة لمنع التكرار:

1️⃣ رقم المستند (عالمياً):
   ❌ لا يجوز تكرار رقم المستند في أي حساب
   📍 مثال: إذا كان رقم المستند "DOC001" موجود في الحساب "A"
           فلا يمكن استخدامه في الحساب "B" أو أي حساب آخر

2️⃣ رقم التأدية (محلياً):
   ❌ لا يجوز تكرار رقم التأدية في نفس الحساب
   ✅ يمكن تكرار رقم التأدية في حسابات مختلفة
   📍 مثال: رقم التأدية "PAY001" يمكن أن يكون في الحساب "A" والحساب "B"
           ولكن لا يمكن أن يكون مكرر في نفس الحساب "A"

🔍 آلية الفحص:
   • فحص تلقائي عند إدخال البيانات
   • رسائل خطأ واضحة ومفصلة
   • تحديد مكان التكرار بدقة
   • منع إضافة المستند في حالة التكرار

💡 الفوائد:
   ✅ منع الأخطاء المحاسبية
   ✅ ضمان دقة البيانات
   ✅ تحسين تجربة المستخدم
   ✅ رسائل توجيهية واضحة
        """)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبارات آلية منع التكرار المحسنة")
    
    # عرض القواعد الجديدة
    demonstrate_duplicate_rules()
    
    # تشغيل اختبار الدوال الجديدة
    functions_test_result = test_enhanced_duplicate_prevention()
    
    # تشغيل اختبار النافذة المحسنة
    ui_test_result = test_document_window_with_duplicate_prevention()
    
    # النتيجة النهائية
    if functions_test_result and ui_test_result:
        print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("✅ آلية منع التكرار المحسنة تعمل بشكل صحيح")
        print("✅ نافذة إضافة المستند محدثة ومحسنة")
    else:
        print("\n❌ فشل في بعض الاختبارات")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
