@echo off
chcp 65001 >nul
title Test Document Addition - Accounting System

echo ========================================
echo    Test Document Addition
echo    Accounting System
echo ========================================
echo.

echo Running document addition tests...
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found, running tests...
    echo.
    
    REM Run the document test
    %PYTHON_PATH% test_add_document.py
    
    echo.
    echo Test completed.
    
) else (
    echo ERROR: Python not found at expected location
    echo Expected: %PYTHON_PATH%
    echo.
    echo Please check Python installation
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul
