# تطبيق Flutter لنظام إدارة المستندات المحاسبية

## وصف التطبيق
تطبيق Flutter حديث ومتجاوب لنظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية

## الميزات الرئيسية

### ✅ الوظائف المتاحة:
- **تسجيل الدخول الآمن** مع حفظ الجلسة
- **إدارة الحسابات** - عرض وإنشاء الحسابات
- **إدارة المستندات** - إضافة وعرض المستندات
- **تفاصيل الحساب** - عرض شامل لبيانات كل حساب
- **البحث المتقدم** - البحث في جميع المستندات
- **واجهة عربية** - دعم كامل للغة العربية واتجاه RTL

### 🎨 التصميم:
- **Material Design 3** - تصميم حديث ومتجاوب
- **ألوان متناسقة** - نظام ألوان احترافي
- **أيقونات Font Awesome** - أيقونات واضحة وجميلة
- **تجربة مستخدم ممتازة** - سهولة في الاستخدام

## التثبيت والتشغيل

### 1. تحميل Flutter
```bash
# تحميل Flutter من الموقع الرسمي
https://flutter.dev/docs/get-started/install

# التحقق من التثبيت
flutter doctor
```

### 2. تشغيل التطبيق
```bash
# الطريقة السهلة - تشغيل ملف الباتش
run_flutter_app.bat

# أو تشغيل يدوي
flutter pub get
flutter run -d web-server --web-port 8080
```

### 3. الوصول للتطبيق
- افتح المتصفح واذهب إلى: `http://localhost:8080`
- معلومات تسجيل الدخول: `admin / admin`

## بنية المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
├── providers/               # إدارة الحالة
├── screens/                 # الشاشات
├── services/                # الخدمات
├── utils/                   # الأدوات المساعدة
└── widgets/                 # الويدجتس المخصصة
```

## الاستخدام

### 1. تسجيل الدخول: `admin / admin`
### 2. إدارة الحسابات والمستندات
### 3. البحث في المستندات
### 4. عرض التفاصيل والإحصائيات

**تم تطوير التطبيق بـ ❤️ باستخدام Flutter**
