#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بناء الملف التنفيذي المستقل
Standalone Executable Builder
نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية
"""

import os
import sys
import subprocess
import shutil
import json
from datetime import datetime
import tempfile

class StandaloneBuilder:
    def __init__(self):
        self.project_name = "نظام_إدارة_المستندات_المحاسبية"
        self.version = "2.0.0"
        self.output_dir = "dist_standalone"
        self.build_dir = "build_temp"
        self.spec_file = "accounting_system.spec"

        # ملفات مطلوبة للتحقق
        self.required_files = [
            'launcher.py',
            'app.py',
            'excel_manager.py',
            'requirements.txt'
        ]

        # مجلدات للتنظيف
        self.cleanup_dirs = ['build', 'dist', '__pycache__']

    def print_header(self):
        """طباعة رأس البرنامج"""
        print("\n" + "="*70)
        print("🏗️ بناء الملف التنفيذي المستقل")
        print("   Standalone Executable Builder")
        print("🏥 نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية")
        print(f"📦 الإصدار: {self.version}")
        print("="*70)

    def check_requirements(self):
        """فحص المتطلبات الأساسية"""
        print("\n🔍 فحص المتطلبات الأساسية...")

        # فحص Python
        if sys.version_info < (3, 7):
            print("❌ يتطلب Python 3.7 أو أحدث")
            return False
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

        # فحص PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller متوفر")
        except ImportError:
            print("❌ PyInstaller غير مثبت")
            print("💡 تثبيت PyInstaller: pip install pyinstaller")
            return False

        # فحص الملفات المطلوبة
        missing_files = []
        for file in self.required_files:
            if os.path.exists(file):
                print(f"✅ {file}")
            else:
                print(f"❌ {file} مفقود")
                missing_files.append(file)

        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            return False

        return True

    def cleanup_previous_builds(self):
        """تنظيف البناءات السابقة"""
        print("\n🧹 تنظيف البناءات السابقة...")

        for dir_name in self.cleanup_dirs:
            if os.path.exists(dir_name):
                try:
                    shutil.rmtree(dir_name)
                    print(f"🗑️ تم حذف {dir_name}")
                except Exception as e:
                    print(f"⚠️ لم يتم حذف {dir_name}: {str(e)}")

        # تنظيف مجلد الإخراج
        if os.path.exists(self.output_dir):
            try:
                shutil.rmtree(self.output_dir)
                print(f"🗑️ تم حذف {self.output_dir}")
            except Exception as e:
                print(f"⚠️ لم يتم حذف {self.output_dir}: {str(e)}")

    def install_dependencies(self):
        """تثبيت المتطلبات"""
        print("\n📦 تثبيت المتطلبات...")

        try:
            # تحديث pip
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'],
                         check=True, capture_output=True)
            print("✅ تم تحديث pip")

            # تثبيت المتطلبات
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
                         check=True, capture_output=True)
            print("✅ تم تثبيت المتطلبات")

            return True

        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت المتطلبات: {e}")
            return False

    def create_version_info(self):
        """إنشاء ملف معلومات الإصدار"""
        print("\n📋 إنشاء معلومات الإصدار...")

        version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2,0,0,0),
    prodvers=(2,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'وزارة الصحة الأردنية - Jordan Ministry of Health'),
        StringStruct(u'FileDescription', u'نظام إدارة المستندات المحاسبية'),
        StringStruct(u'FileVersion', u'{self.version}'),
        StringStruct(u'InternalName', u'AccountingSystem'),
        StringStruct(u'LegalCopyright', u'© 2025 وزارة الصحة الأردنية'),
        StringStruct(u'OriginalFilename', u'{self.project_name}.exe'),
        StringStruct(u'ProductName', u'نظام إدارة المستندات المحاسبية'),
        StringStruct(u'ProductVersion', u'{self.version}')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''

        try:
            with open('version_info.txt', 'w', encoding='utf-8') as f:
                f.write(version_info)
            print("✅ تم إنشاء ملف معلومات الإصدار")
            return True
        except Exception as e:
            print(f"⚠️ لم يتم إنشاء ملف معلومات الإصدار: {str(e)}")
            return False

    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("\n🔨 بناء الملف التنفيذي...")

        # أوامر PyInstaller المبسطة
        pyinstaller_cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            '--onefile',
            '--windowed',
            '--name', self.project_name,
            '--distpath', self.output_dir,
            '--workpath', self.build_dir,
            '--specpath', '.',

            # إضافة ملفات البيانات الأساسية
            '--add-data', 'requirements.txt;.',
            '--add-data', '*.xlsx;.',
            '--add-data', '*.json;.',

            # مكتبات openpyxl الأساسية
            '--hidden-import', 'openpyxl',
            '--hidden-import', 'openpyxl.workbook',
            '--hidden-import', 'openpyxl.worksheet',
            '--hidden-import', 'openpyxl.styles',
            '--hidden-import', 'openpyxl.utils',

            # مكتبات tkinter الأساسية
            '--hidden-import', 'tkinter',
            '--hidden-import', 'tkinter.ttk',
            '--hidden-import', 'tkinter.messagebox',
            '--hidden-import', 'tkinter.filedialog',
            '--hidden-import', 'tkinter.font',

            # مكتبات واجهة المستخدم (اختيارية)
            '--hidden-import', 'ttkthemes',
            '--hidden-import', 'PIL',
            '--hidden-import', 'PIL.Image',
            '--hidden-import', 'PIL.ImageTk',

            # مكتبات التطبيق المحلية
            '--hidden-import', 'excel_manager',
            '--hidden-import', 'document_window',
            '--hidden-import', 'search_window',
            '--hidden-import', 'manage_accounts',
            '--hidden-import', 'user_manager',
            '--hidden-import', 'launcher',

            # استبعاد مكتبات غير ضرورية
            '--exclude-module', 'matplotlib',
            '--exclude-module', 'numpy',
            '--exclude-module', 'pandas',
            '--exclude-module', 'scipy',
        ]\n        \n        # إضافة ملف معلومات الإصدار إذا كان متوفراً\n        if os.path.exists('version_info.txt'):\n            pyinstaller_cmd.extend(['--version-file', 'version_info.txt'])\n            \n        # إضافة أيقونة إذا كانت متوفرة\n        if os.path.exists('icon.ico'):\n            pyinstaller_cmd.extend(['--icon', 'icon.ico'])\n            \n        # إضافة الملف الرئيسي\n        pyinstaller_cmd.append('launcher.py')\n        \n        try:\n            print(\"🔄 تشغيل PyInstaller...\")\n            print(f\"📝 الأمر: {' '.join(pyinstaller_cmd)}\")\n            \n            result = subprocess.run(pyinstaller_cmd, \n                                  capture_output=True, \n                                  text=True, \n                                  check=True)\n            \n            print(\"✅ تم بناء الملف التنفيذي بنجاح\")\n            return True\n            \n        except subprocess.CalledProcessError as e:\n            print(f\"❌ فشل في بناء الملف التنفيذي\")\n            print(f\"📋 الخطأ: {e.stderr}\")\n            return False\n            \n    def copy_additional_files(self):\n        \"\"\"نسخ الملفات الإضافية المطلوبة\"\"\"\n        print(\"\\n📁 نسخ الملفات الإضافية...\")\n        \n        additional_files = [\n            'accounting_system.xlsx',\n            'Accounting system deductions.xlsx',\n            'users.json',\n            'README.md',\n            'requirements.txt'\n        ]\n        \n        for file in additional_files:\n            if os.path.exists(file):\n                try:\n                    dest_path = os.path.join(self.output_dir, file)\n                    shutil.copy2(file, dest_path)\n                    print(f\"✅ تم نسخ {file}\")\n                except Exception as e:\n                    print(f\"⚠️ لم يتم نسخ {file}: {str(e)}\")\n            else:\n                print(f\"⚠️ الملف غير موجود: {file}\")\n                \n    def create_readme(self):\n        \"\"\"إنشاء ملف README للتوزيع\"\"\"\n        print(\"\\n📄 إنشاء ملف README...\")\n        \n        readme_content = f\"\"\"# نظام إدارة المستندات المحاسبية\n## Accounting Documents Management System\n\n### معلومات الإصدار\n- **الإصدار**: {self.version}\n- **تاريخ البناء**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n- **المطور**: وزارة الصحة الأردنية\n\n### متطلبات التشغيل\n- **نظام التشغيل**: Windows 10 أو أحدث\n- **الذاكرة**: 4 GB RAM أو أكثر\n- **مساحة القرص**: 100 MB مساحة فارغة\n\n### طريقة التشغيل\n1. قم بتشغيل الملف: `{self.project_name}.exe`\n2. سيتم فتح نافذة تسجيل الدخول\n3. استخدم بيانات الدخول الافتراضية:\n   - **اسم المستخدم**: admin\n   - **كلمة المرور**: admin\n\n### الملفات المضمنة\n- `{self.project_name}.exe` - الملف التنفيذي الرئيسي\n- `accounting_system.xlsx` - ملف البيانات الرئيسي\n- `Accounting system deductions.xlsx` - ملف الخصومات\n- `users.json` - ملف بيانات المستخدمين\n- `requirements.txt` - قائمة المتطلبات\n\n### المميزات\n- ✅ **مستقل تماماً** - لا يحتاج تثبيت Python أو مكتبات إضافية\n- ✅ **واجهة عربية** - دعم كامل للغة العربية\n- ✅ **إدارة المستخدمين** - نظام صلاحيات متقدم\n- ✅ **معالجة Excel** - تعامل متقدم مع ملفات Excel\n- ✅ **البحث والتصفية** - أدوات بحث قوية\n- ✅ **التقارير** - إنشاء تقارير مفصلة\n\n### الدعم الفني\n- **الموقع**: https://moh.gov.jo\n- **البريد الإلكتروني**: <EMAIL>\n- **الهاتف**: +962-6-5200000\n\n### حقوق الطبع والنشر\n© 2025 وزارة الصحة الأردنية - جميع الحقوق محفوظة\n\n---\n\n## English Version\n\n### System Requirements\n- **OS**: Windows 10 or later\n- **RAM**: 4 GB or more\n- **Disk Space**: 100 MB free space\n\n### How to Run\n1. Run the file: `{self.project_name}.exe`\n2. Login window will appear\n3. Use default credentials:\n   - **Username**: admin\n   - **Password**: admin\n\n### Features\n- ✅ **Fully Standalone** - No need for Python or additional libraries\n- ✅ **Arabic Interface** - Full Arabic language support\n- ✅ **User Management** - Advanced permissions system\n- ✅ **Excel Processing** - Advanced Excel file handling\n- ✅ **Search & Filter** - Powerful search tools\n- ✅ **Reports** - Detailed report generation\n\n### Copyright\n© 2025 Jordan Ministry of Health - All Rights Reserved\n\"\"\"\n        \n        try:\n            readme_path = os.path.join(self.output_dir, 'README_DISTRIBUTION.md')\n            with open(readme_path, 'w', encoding='utf-8') as f:\n                f.write(readme_content)\n            print(\"✅ تم إنشاء ملف README\")\n            return True\n        except Exception as e:\n            print(f\"⚠️ لم يتم إنشاء ملف README: {str(e)}\")\n            return False\n            \n    def create_build_info(self):\n        \"\"\"إنشاء ملف معلومات البناء\"\"\"\n        print(\"\\n📊 إنشاء معلومات البناء...\")\n        \n        build_info = {\n            'project_name': self.project_name,\n            'version': self.version,\n            'build_date': datetime.now().isoformat(),\n            'python_version': f\"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}\",\n            'platform': sys.platform,\n            'builder': 'PyInstaller',\n            'build_type': 'Standalone Executable',\n            'included_files': [],\n            'requirements': []\n        }\n        \n        # قراءة المتطلبات\n        if os.path.exists('requirements.txt'):\n            with open('requirements.txt', 'r', encoding='utf-8') as f:\n                for line in f:\n                    line = line.strip()\n                    if line and not line.startswith('#'):\n                        build_info['requirements'].append(line)\n                        \n        # قائمة الملفات المضمنة\n        if os.path.exists(self.output_dir):\n            for file in os.listdir(self.output_dir):\n                if os.path.isfile(os.path.join(self.output_dir, file)):\n                    build_info['included_files'].append(file)\n                    \n        try:\n            build_info_path = os.path.join(self.output_dir, 'build_info.json')\n            with open(build_info_path, 'w', encoding='utf-8') as f:\n                json.dump(build_info, f, ensure_ascii=False, indent=2)\n            print(\"✅ تم إنشاء ملف معلومات البناء\")\n            return True\n        except Exception as e:\n            print(f\"⚠️ لم يتم إنشاء ملف معلومات البناء: {str(e)}\")\n            return False\n            \n    def verify_build(self):\n        \"\"\"التحقق من نجاح البناء\"\"\"\n        print(\"\\n🔍 التحقق من نجاح البناء...\")\n        \n        exe_path = os.path.join(self.output_dir, f\"{self.project_name}.exe\")\n        \n        if not os.path.exists(exe_path):\n            print(\"❌ الملف التنفيذي غير موجود\")\n            return False\n            \n        # فحص حجم الملف\n        file_size = os.path.getsize(exe_path)\n        file_size_mb = file_size / (1024 * 1024)\n        \n        print(f\"✅ الملف التنفيذي موجود\")\n        print(f\"📊 حجم الملف: {file_size_mb:.1f} MB\")\n        \n        if file_size_mb < 10:\n            print(\"⚠️ حجم الملف صغير - قد يكون هناك مشكلة\")\n            return False\n            \n        return True\n        \n    def cleanup_temp_files(self):\n        \"\"\"تنظيف الملفات المؤقتة\"\"\"\n        print(\"\\n🧹 تنظيف الملفات المؤقتة...\")\n        \n        temp_files = [\n            'version_info.txt',\n            self.build_dir,\n            'build'\n        ]\n        \n        for item in temp_files:\n            if os.path.exists(item):\n                try:\n                    if os.path.isdir(item):\n                        shutil.rmtree(item)\n                    else:\n                        os.remove(item)\n                    print(f\"🗑️ تم حذف {item}\")\n                except Exception as e:\n                    print(f\"⚠️ لم يتم حذف {item}: {str(e)}\")\n                    \n    def run(self):\n        \"\"\"تشغيل عملية البناء الكاملة\"\"\"\n        self.print_header()\n        \n        # فحص المتطلبات\n        if not self.check_requirements():\n            print(\"❌ فشل في فحص المتطلبات\")\n            return False\n            \n        # تنظيف البناءات السابقة\n        self.cleanup_previous_builds()\n        \n        # تثبيت المتطلبات\n        if not self.install_dependencies():\n            print(\"❌ فشل في تثبيت المتطلبات\")\n            return False\n            \n        # إنشاء معلومات الإصدار\n        self.create_version_info()\n        \n        # بناء الملف التنفيذي\n        if not self.build_executable():\n            print(\"❌ فشل في بناء الملف التنفيذي\")\n            return False\n            \n        # نسخ الملفات الإضافية\n        self.copy_additional_files()\n        \n        # إنشاء ملفات التوثيق\n        self.create_readme()\n        self.create_build_info()\n        \n        # التحقق من البناء\n        if not self.verify_build():\n            print(\"❌ فشل في التحقق من البناء\")\n            return False\n            \n        # تنظيف الملفات المؤقتة\n        self.cleanup_temp_files()\n        \n        # النتيجة النهائية\n        print(\"\\n\" + \"=\"*70)\n        print(\"🎉 تم بناء الملف التنفيذي بنجاح!\")\n        print(f\"📁 مجلد الإخراج: {self.output_dir}\")\n        print(f\"🚀 الملف التنفيذي: {self.project_name}.exe\")\n        print(\"✅ الملف مستقل تماماً ولا يحتاج أي متطلبات إضافية\")\n        print(\"=\"*70)\n        \n        return True\n\ndef main():\n    \"\"\"الدالة الرئيسية\"\"\"\n    builder = StandaloneBuilder()\n    success = builder.run()\n    \n    input(\"\\nاضغط Enter للإغلاق...\")\n    return 0 if success else 1\n\nif __name__ == \"__main__\":\n    sys.exit(main())
