نظام إدارة المستندات المحاسبية
وزارة الصحة الأردنية
========================================

🎯 وصف النظام:
نظام محاسبي متكامل لإدارة المستندات المالية والحوالات المحاسبية

✨ المميزات:
• إدارة الحسابات المحاسبية
• إضافة وتتبع المستندات المالية  
• إنشاء التقارير المحاسبية
• البحث في المستندات
• واجهة عربية كاملة
• حفظ البيانات في ملفات Excel

🚀 طريقة التشغيل:
1. انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
2. انتظر حتى يتم فحص وتثبيت المتطلبات
3. سيتم تشغيل النظام تلقائياً

📋 المتطلبات:
• Python 3.7 أو أحدث
• اتصال بالإنترنت (للتثبيت الأولي فقط)
• نظام Windows

🔧 في حالة وجود مشاكل:

مشكلة: "Python غير معروف"
الحل: تثبيت Python من https://www.python.org/downloads/
تأكد من تحديد "Add Python to PATH"

مشكلة: خطأ في المكتبات
الحل: تشغيل الأمر التالي في Command Prompt:
pip install openpyxl ttkthemes

مشكلة: لا يظهر النص العربي بشكل صحيح
الحل: تأكد من أن النظام يدعم UTF-8

📁 ملفات النظام:
• launcher.py - مشغل النظام
• app.py - التطبيق الرئيسي
• excel_manager.py - إدارة ملفات Excel
• document_window.py - نافذة المستندات
• search_window.py - نافذة البحث
• manage_accounts.py - إدارة الحسابات
• accounting_system.xlsx - ملف البيانات

⚠️ تنبيهات مهمة:
• لا تحذف أي ملف من ملفات النظام
• احتفظ بنسخة احتياطية من ملف البيانات
• تأكد من إغلاق النظام بشكل صحيح لحفظ البيانات

📞 الدعم الفني:
للمساعدة والدعم الفني، تواصل مع:
فريق تطوير الأنظمة المحاسبية
وزارة الصحة الأردنية

📄 معلومات النسخة:
النسخة: 1.0
تاريخ الإصدار: 2024
مطور خصيصاً لوزارة الصحة الأردنية

========================================
شكراً لاستخدام النظام
