import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/account.dart';
import '../utils/app_theme.dart';

class AccountCard extends StatelessWidget {
  final Account account;
  final VoidCallback? onTap;

  const AccountCard({
    super.key,
    required this.account,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة الحساب
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: const Icon(
                  FontAwesomeIcons.user,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // معلومات الحساب
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الحساب
                    Text(
                      account.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // رقم الحساب
                    Text(
                      'رقم الحساب: ${account.number}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.subtitleColor,
                      ),
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // تاريخ الإنشاء
                    if (account.createdDate.isNotEmpty)
                      Text(
                        'تاريخ الإنشاء: ${account.createdDate}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.subtitleColor,
                        ),
                      ),
                  ],
                ),
              ),
              
              // الرصيد
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${account.balance.toStringAsFixed(2)} د.أ',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: account.balance >= 0 
                          ? AppTheme.accentColor 
                          : AppTheme.errorColor,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Text(
                    'الرصيد الحالي',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.subtitleColor,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(width: 8),
              
              // سهم للتفاصيل
              const Icon(
                FontAwesomeIcons.chevronLeft,
                size: 16,
                color: AppTheme.subtitleColor,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
