#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعداد نظام إدارة المستندات المحاسبية
Setup file for Accounting System
وزارة الصحة الأردنية - Jordan Ministry of Health
"""

from setuptools import setup, find_packages
import os
import sys

# قراءة ملف المتطلبات
def read_requirements():
    """قراءة المتطلبات من ملف requirements.txt"""
    requirements = []
    if os.path.exists('requirements.txt'):
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # تجاهل التعليقات والأسطر الفارغة
                if line and not line.startswith('#'):
                    # إزالة التعليقات من نهاية السطر
                    if ';' in line:
                        line = line.split(';')[0].strip()
                    if line:
                        requirements.append(line)
    return requirements

# قراءة ملف README إذا كان موجوداً
def read_readme():
    """قراءة ملف README"""
    readme_files = ['README.md', 'README.txt', 'README.rst']
    for readme_file in readme_files:
        if os.path.exists(readme_file):
            with open(readme_file, 'r', encoding='utf-8') as f:
                return f.read()
    return "نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية"

# معلومات النسخة
VERSION = "2.0.0"
DESCRIPTION = "نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية"
LONG_DESCRIPTION = read_readme()

# إعداد الحزمة
setup(
    name="accounting-system-moh-jordan",
    version=VERSION,
    author="وزارة الصحة الأردنية",
    author_email="<EMAIL>",
    description=DESCRIPTION,
    long_description=LONG_DESCRIPTION,
    long_description_content_type="text/markdown",
    url="https://github.com/moh-jordan/accounting-system",
    
    # الحزم والملفات
    packages=find_packages(),
    py_modules=[
        'app',
        'launcher',
        'excel_manager',
        'multi_excel_manager',
        'document_window',
        'search_window',
        'manage_accounts',
        'user_manager',
        'receipts_account_window',
        'receipts_document_window',
        'manage_receipts_accounts',
        'receipts_search_window',
        'account_balances_window'
    ],
    
    # ملفات البيانات المطلوبة
    package_data={
        '': [
            '*.xlsx',
            '*.json',
            '*.ico',
            '*.png',
            '*.jpg',
            '*.gif',
            '*.md',
            '*.txt',
            '*.bat',
            '*.spec'
        ],
    },
    include_package_data=True,
    
    # المتطلبات
    install_requires=read_requirements(),
    
    # متطلبات Python
    python_requires=">=3.7",
    
    # التصنيفات
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Accounting",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: Microsoft :: Windows",
        "Operating System :: POSIX :: Linux",
        "Operating System :: MacOS",
        "Natural Language :: Arabic",
    ],
    
    # الكلمات المفتاحية
    keywords="accounting, excel, documents, management, jordan, ministry, health, محاسبة, مستندات",
    
    # نقاط الدخول
    entry_points={
        'console_scripts': [
            'accounting-system=launcher:main',
            'accounting-app=app:main',
        ],
        'gui_scripts': [
            'accounting-system-gui=launcher:main',
        ],
    },
    
    # خيارات إضافية
    zip_safe=False,
    
    # متطلبات إضافية اختيارية
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'black>=23.0.0',
            'flake8>=6.0.0',
            'mypy>=1.0.0',
        ],
        'build': [
            'pyinstaller>=5.13.0',
            'auto-py-to-exe>=2.40.0',
        ],
        'fonts': [
            'arabic-reshaper>=3.0.0',
            'python-bidi>=0.4.2',
        ],
    },
    
    # معلومات المشروع
    project_urls={
        "Bug Reports": "https://github.com/moh-jordan/accounting-system/issues",
        "Source": "https://github.com/moh-jordan/accounting-system",
        "Documentation": "https://github.com/moh-jordan/accounting-system/wiki",
    },
)

# رسائل ما بعد التثبيت
def post_install_message():
    """رسالة ما بعد التثبيت"""
    print("\n" + "="*60)
    print("🎉 تم تثبيت نظام إدارة المستندات المحاسبية بنجاح!")
    print("✅ Installation completed successfully!")
    print("="*60)
    print("📋 لتشغيل النظام:")
    print("   - استخدم الأمر: accounting-system")
    print("   - أو شغل الملف: python launcher.py")
    print("   - أو شغل الملف: python app.py")
    print("="*60)
    print("🔧 للحصول على المساعدة:")
    print("   - راجع ملف README.md")
    print("   - تواصل مع الدعم الفني")
    print("="*60)
    print("🏥 وزارة الصحة الأردنية")
    print("   Jordan Ministry of Health")
    print("="*60 + "\n")

if __name__ == "__main__":
    # تشغيل الإعداد
    setup()
    
    # عرض رسالة ما بعد التثبيت
    if len(sys.argv) > 1 and sys.argv[1] == 'install':
        post_install_message()
