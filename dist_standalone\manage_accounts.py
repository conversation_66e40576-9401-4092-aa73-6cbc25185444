import tkinter as tk
from tkinter import ttk, messagebox
import os

class ManageAccountsDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إدارة الحسابات")
        self.excel = excel

        # تكوين النافذة
        self.geometry("800x500")
        self.configure(bg='#f0f0f0')

        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # قائمة الحسابات
        self.create_accounts_list(main_frame)

        # أزرار التحكم
        self.create_control_buttons(main_frame)

        # تحديث القائمة
        self.load_accounts()

    def create_accounts_list(self, parent):
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="الحسابات", padding="5")
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)

        # إنشاء جدول الحسابات
        columns = ('رقم الحساب', 'اسم الحساب', 'الرصيد')
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show='headings')

        # تعيين العناوين
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=150)

        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)

        # وضع العناصر في الإطار
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # تمكين التمدد
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

    def create_control_buttons(self, parent):
        # إطار الأزرار
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=1, column=0, pady=10)

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(pady=5)

        ttk.Button(row1_frame, text="عرض تفاصيل الحساب",
                  command=self.view_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تصدير تفاصيل الحساب",
                  command=self.export_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تعديل الحساب",
                  command=self.edit_account).pack(side=tk.LEFT, padx=5)

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(pady=5)

        ttk.Button(row2_frame, text="حذف الحساب",
                  command=self.delete_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="إضافة جدول جديد",
                  command=self.add_new_table).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="تحديث القائمة",
                  command=self.refresh_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="إغلاق",
                  command=self.destroy).pack(side=tk.LEFT, padx=5)

    def _safe_get_numeric_value(self, cell):
        """استخراج قيمة رقمية من خلية مع معالجة الصيغ"""
        try:
            value = cell.value
            if value is None:
                return 0

            # إذا كانت القيمة رقمية بالفعل
            if isinstance(value, (int, float)):
                return float(value)

            # إذا كانت نص (قد تكون صيغة)
            if isinstance(value, str):
                # إذا كانت صيغة Excel
                if value.startswith('='):
                    # محاولة تقييم الصيغة بطريقة بسيطة
                    # هذه طريقة مبسطة للصيغ SUM
                    if 'SUM(' in value.upper():
                        # استخراج نطاق الخلايا من الصيغة
                        import re
                        match = re.search(r'SUM\(([A-Z]+\d+):([A-Z]+\d+)\)', value.upper())
                        if match:
                            start_cell = match.group(1)
                            end_cell = match.group(2)
                            return self._calculate_sum_range(cell.parent, start_cell, end_cell)
                    return 0  # صيغة غير مدعومة
                else:
                    # محاولة تحويل النص إلى رقم
                    try:
                        return float(value)
                    except ValueError:
                        return 0

            return 0

        except Exception as e:
            print(f"⚠️ خطأ في قراءة قيمة الخلية: {str(e)}")
            return 0

    def _calculate_sum_range(self, worksheet, start_cell, end_cell):
        """حساب مجموع نطاق من الخلايا"""
        try:
            # تحليل مرجع الخلايا
            from openpyxl.utils import range_boundaries
            min_col, min_row, max_col, max_row = range_boundaries(f"{start_cell}:{end_cell}")

            total = 0
            for row in range(min_row, max_row + 1):
                for col in range(min_col, max_col + 1):
                    cell_value = worksheet.cell(row=row, column=col).value
                    if isinstance(cell_value, (int, float)):
                        total += cell_value

            return total

        except Exception as e:
            print(f"⚠️ خطأ في حساب المجموع: {str(e)}")
            return 0

    def refresh_data(self):
        """تحديث البيانات تلقائياً من الملف"""
        try:
            print("🔄 بدء تحديث البيانات...")

            # إعادة تحميل الملف من القرص
            if os.path.exists(self.excel.current_file):
                # حفظ الملف أولاً
                self.excel.save_workbook()

                # إعادة تحميل الملف
                import openpyxl
                self.excel.workbook = openpyxl.load_workbook(self.excel.current_file)
                print("✅ تم إعادة تحميل الملف بنجاح")

                # تحديث قائمة الحسابات
                self.load_accounts()

                messagebox.showinfo("تحديث", "تم تحديث البيانات بنجاح")
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على ملف البيانات")

        except Exception as e:
            error_msg = f"خطأ في تحديث البيانات: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)

    def load_accounts(self):
        """تحميل الحسابات في الجدول"""
        # مسح الجدول
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # تحميل الحسابات (استثناء الصفحات الخاصة)
        special_sheets = ['مرحباً', 'التقارير', 'تقرير المستندات', 'التقرير الإجمالي']

        for sheet_name in self.excel.workbook.sheetnames:
            if sheet_name not in special_sheets:
                try:
                    # استخراج رقم واسم الحساب (فقط إذا كان يحتوي على علامة -)
                    if '-' in sheet_name:
                        account_num, account_name = sheet_name.split('-', 1)
                    else:
                        # تجاهل الصفحات التي لا تحتوي على علامة -
                        continue

                    # حساب الرصيد الصحيح من جميع الجداول
                    ws = self.excel.workbook[sheet_name]
                    balance = self._calculate_account_balance(ws)

                    # إضافة الصف
                    self.accounts_tree.insert('', tk.END, values=(account_num, account_name, balance))
                except:
                    continue

    def view_account_details(self):
        """عرض تفاصيل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لعرض تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل
        details_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        self.wait_window(details_dialog)

    def export_account_details(self):
        """تصدير تفاصيل الحساب المحدد مباشرة"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتصدير تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل مؤقتاً للحصول على البيانات
        temp_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        temp_dialog.withdraw()  # إخفاء النافذة

        # تصدير التفاصيل مباشرة
        temp_dialog.export_details()

        # إغلاق النافذة المؤقتة
        temp_dialog.destroy()

    def edit_account(self):
        """تعديل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتعديله")
            return

        item = selection[0]
        account_num, account_name, _ = self.accounts_tree.item(item)['values']

        # إنشاء نافذة التعديل
        dialog = AccountEditDialog(self, self.excel, account_num, account_name)
        self.wait_window(dialog)

        # تحديث القائمة
        self.load_accounts()

    def add_new_table(self):
        """إضافة جدول جديد للحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لإضافة جدول جديد له")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # تأكيد إضافة الجدول
        result = messagebox.askyesno("تأكيد إضافة جدول",
                                   f"هل تريد إضافة جدول جديد للحساب '{account_name}'?\n\n"
                                   f"سيتم إضافة جدول جديد في نهاية الورقة لاستيعاب مزيد من المستندات.")

        if result:
            try:
                print(f"📝 إضافة جدول جديد للحساب: {sheet_name}")

                # التحقق من وجود الحساب
                if sheet_name not in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", f"الحساب '{sheet_name}' غير موجود")
                    return

                ws = self.excel.workbook[sheet_name]

                # إضافة جدول جديد
                success = self.excel._create_new_table(ws, account_num, account_name)

                if success:
                    # حفظ التغييرات
                    save_result = self.excel.save_workbook()
                    if save_result:
                        messagebox.showinfo("نجاح", f"تم إضافة جدول جديد للحساب '{account_name}' بنجاح\n\n"
                                           f"يمكنك الآن إضافة مزيد من المستندات في الجدول الجديد.")
                        # تحديث القائمة
                        self.refresh_data()
                    else:
                        messagebox.showerror("خطأ", "تم إضافة الجدول ولكن فشل في حفظ الملف")
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة الجدول الجديد\n\n"
                                                 f"قد يكون الحساب ممتلئاً بالفعل أو هناك مشكلة في التنسيق.")

            except Exception as e:
                error_msg = f"خطأ في إضافة الجدول: {str(e)}"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)

    def delete_account(self):
        """حذف الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لحذفه")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # نافذة تأكيد بسيطة وموثوقة
        confirm_message = (f"هل أنت متأكد من حذف الحساب؟\n\n"
                          f"رقم الحساب: {account_num}\n"
                          f"اسم الحساب: {account_name}\n"
                          f"الرصيد الحالي: {balance}\n\n"
                          f"تحذير: سيتم حذف الحساب نهائياً مع جميع المستندات!")

        if messagebox.askyesno("تأكيد حذف الحساب", confirm_message):
            try:
                print(f"🗑️ حذف الحساب: {sheet_name}")  # للتشخيص

                # التحقق من وجود الحساب
                if sheet_name not in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", f"الحساب '{sheet_name}' غير موجود")
                    return

                # حذف الصفحة
                self.excel.workbook.remove(self.excel.workbook[sheet_name])
                print(f"✅ تم حذف الصفحة من الملف")  # للتشخيص

                # تحديث التقرير الإجمالي تلقائياً
                print(f"📊 تحديث التقرير الإجمالي بعد حذف الحساب...")
                self.excel.create_summary_report()

                # حفظ التغييرات
                if self.excel.save_workbook():
                    print(f"✅ تم حفظ الملف بنجاح")  # للتشخيص

                    # تحديث القائمة
                    self.load_accounts()

                    messagebox.showinfo("نجاح", f"تم حذف الحساب '{account_name}' بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء حذف الحساب: {str(e)}"
                print(f"❌ {error_msg}")  # للتشخيص
                messagebox.showerror("خطأ", error_msg)

class AccountEditDialog(tk.Toplevel):
    def __init__(self, parent, excel, account_num, account_name):
        super().__init__(parent)
        self.title("تعديل الحساب")
        self.excel = excel
        self.old_sheet_name = f"{account_num}-{account_name}"

        # تكوين النافذة
        self.geometry("400x200")

        # حقول الإدخال
        ttk.Label(self, text="رقم الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_num = ttk.Entry(self)
        self.account_num.insert(0, account_num)
        self.account_num.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(self, text="اسم الحساب:").grid(row=1, column=0, padx=5, pady=5)
        self.account_name = ttk.Entry(self)
        self.account_name.insert(0, account_name)
        self.account_name.grid(row=1, column=1, padx=5, pady=5)

        # أزرار
        ttk.Button(self, text="حفظ",
                  command=self.save_changes).grid(row=2, column=0, columnspan=2, pady=20)

    def save_changes(self):
        """حفظ التغييرات على الحساب"""
        try:
            new_sheet_name = f"{self.account_num.get()}-{self.account_name.get()}"

            if new_sheet_name != self.old_sheet_name:
                # التحقق من عدم وجود حساب بنفس الاسم
                if new_sheet_name in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", "يوجد حساب بنفس الرقم والاسم")
                    return

                # تغيير اسم الصفحة
                sheet = self.excel.workbook[self.old_sheet_name]
                sheet.title = new_sheet_name

                # حفظ التغييرات
                if self.excel.save_workbook():
                    messagebox.showinfo("نجاح", "تم تعديل الحساب بنجاح")
                    self.destroy()
            else:
                self.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل الحساب: {str(e)}")

class DeleteConfirmDialog(tk.Toplevel):
    """نافذة تأكيد حذف الحساب"""
    def __init__(self, parent, account_num, account_name, balance, sheet_name):
        super().__init__(parent)
        self.title("تأكيد حذف الحساب")
        self.account_num = account_num
        self.account_name = account_name
        self.balance = balance
        self.sheet_name = sheet_name
        self.confirmed = False

        # تكوين النافذة
        self.geometry("500x300")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة تحذير
        warning_label = tk.Label(main_frame, text="⚠️", font=("Arial", 48),
                                bg='#f0f0f0', fg='#ff6b6b')
        warning_label.pack(pady=(0, 20))

        # عنوان التحذير
        title_label = tk.Label(main_frame, text="تحذير: حذف الحساب",
                              font=("Arial", 16, "bold"),
                              bg='#f0f0f0', fg='#d63031')
        title_label.pack(pady=(0, 10))

        # معلومات الحساب
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(info_frame, text="معلومات الحساب المراد حذفه:",
                font=("Arial", 12, "bold"), bg='#ffffff').pack(pady=5)

        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"الرصيد الحالي: {self.balance}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10, pady=(0, 5))

        # رسالة التحذير
        warning_text = ("سيتم حذف الحساب نهائياً مع جميع المستندات والبيانات المرتبطة به.\n"
                       "هذا الإجراء لا يمكن التراجع عنه!")
        warning_label = tk.Label(main_frame, text=warning_text,
                                font=("Arial", 11), bg='#f0f0f0', fg='#d63031',
                                wraplength=400, justify=tk.CENTER)
        warning_label.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack()

        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="حذف الحساب",
                              command=self.confirm_delete,
                              bg='#d63031', fg='white',
                              font=("Arial", 11, "bold"),
                              padx=20, pady=5)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              command=self.cancel_delete,
                              bg='#74b9ff', fg='white',
                              font=("Arial", 11),
                              padx=20, pady=5)
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفتاح Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.cancel_delete())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def confirm_delete(self):
        """تأكيد الحذف"""
        self.confirmed = True
        self.destroy()

    def cancel_delete(self):
        """إلغاء الحذف"""
        self.confirmed = False
        self.destroy()

class AccountDetailsDialog(tk.Toplevel):
    """نافذة عرض تفاصيل الحساب"""
    def __init__(self, parent, excel, sheet_name, account_num, account_name):
        super().__init__(parent)
        self.title(f"تفاصيل الحساب: {account_name}")
        self.excel = excel
        self.sheet_name = sheet_name
        self.account_num = account_num
        self.account_name = account_name

        # تكوين النافذة
        self.geometry("700x500")
        self.configure(bg='#f0f0f0')

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.load_account_data()

        # توسيط النافذة
        self.center_window()

    def _safe_get_numeric_value(self, cell):
        """استخراج قيمة رقمية من خلية مع معالجة الصيغ"""
        try:
            value = cell.value
            if value is None:
                return 0

            # إذا كانت القيمة رقمية بالفعل
            if isinstance(value, (int, float)):
                return float(value)

            # إذا كانت نص (قد تكون صيغة)
            if isinstance(value, str):
                # إذا كانت صيغة Excel
                if value.startswith('='):
                    # محاولة تقييم الصيغة بطريقة بسيطة
                    # هذه طريقة مبسطة للصيغ SUM
                    if 'SUM(' in value.upper():
                        # استخراج نطاق الخلايا من الصيغة
                        import re
                        match = re.search(r'SUM\(([A-Z]+\d+):([A-Z]+\d+)\)', value.upper())
                        if match:
                            start_cell = match.group(1)
                            end_cell = match.group(2)
                            return self._calculate_sum_range(cell.parent, start_cell, end_cell)
                    return 0  # صيغة غير مدعومة
                else:
                    # محاولة تحويل النص إلى رقم
                    try:
                        return float(value)
                    except ValueError:
                        return 0

            return 0

        except Exception as e:
            print(f"⚠️ خطأ في قراءة قيمة الخلية: {str(e)}")
            return 0

    def _calculate_sum_range(self, worksheet, start_cell, end_cell):
        """حساب مجموع نطاق من الخلايا"""
        try:
            # تحليل مرجع الخلايا
            from openpyxl.utils import range_boundaries
            min_col, min_row, max_col, max_row = range_boundaries(f"{start_cell}:{end_cell}")

            total = 0
            for row in range(min_row, max_row + 1):
                for col in range(min_col, max_col + 1):
                    cell_value = worksheet.cell(row=row, column=col).value
                    if isinstance(cell_value, (int, float)):
                        total += cell_value

            return total

        except Exception as e:
            print(f"⚠️ خطأ في حساب المجموع: {str(e)}")
            return 0

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الحساب الأساسية
        info_frame = tk.LabelFrame(main_frame, text="معلومات الحساب",
                                  font=("Arial", 12, "bold"), bg='#f0f0f0')
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # رقم الحساب
        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # اسم الحساب
        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الافتتاحي
        self.opening_balance_label = tk.Label(info_frame, text="الرصيد الافتتاحي: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.opening_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الحالي
        self.current_balance_label = tk.Label(info_frame, text="الرصيد الحالي: جاري التحميل...",
                                            font=("Arial", 11, "bold"), bg='#f0f0f0')
        self.current_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # إحصائيات المستندات
        stats_frame = tk.LabelFrame(main_frame, text="إحصائيات المستندات",
                                   font=("Arial", 12, "bold"), bg='#f0f0f0')
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        self.documents_count_label = tk.Label(stats_frame, text="عدد المستندات: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.documents_count_label.pack(anchor=tk.W, padx=10, pady=5)

        self.total_amount_label = tk.Label(stats_frame, text="إجمالي المبالغ: جاري التحميل...",
                                         font=("Arial", 11), bg='#f0f0f0')
        self.total_amount_label.pack(anchor=tk.W, padx=10, pady=5)

        # قائمة المستندات
        documents_frame = tk.LabelFrame(main_frame, text="المستندات الأخيرة",
                                       font=("Arial", 12, "bold"), bg='#f0f0f0')
        documents_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # جدول المستندات
        columns = ('المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم')
        self.documents_tree = ttk.Treeview(documents_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.documents_tree.heading(col, text=col)
            self.documents_tree.column(col, width=120)

        # شريط التمرير للمستندات
        docs_scrollbar = ttk.Scrollbar(documents_frame, orient=tk.VERTICAL, command=self.documents_tree.yview)
        self.documents_tree.configure(yscrollcommand=docs_scrollbar.set)

        self.documents_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        docs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=10)

        # زر تصدير التفاصيل
        export_btn = tk.Button(buttons_frame, text="تصدير التفاصيل",
                              command=self.export_details,
                              bg='#00b894', fg='white', font=("Arial", 11),
                              padx=20, pady=5)
        export_btn.pack(side=tk.LEFT, padx=5)

        # زر طباعة التقرير
        print_btn = tk.Button(buttons_frame, text="طباعة التقرير",
                             command=self.print_report,
                             bg='#6c5ce7', fg='white', font=("Arial", 11),
                             padx=20, pady=5)
        print_btn.pack(side=tk.LEFT, padx=5)

        # زر الإغلاق
        close_btn = tk.Button(buttons_frame, text="إغلاق", command=self.destroy,
                             bg='#74b9ff', fg='white', font=("Arial", 11),
                             padx=20, pady=5)
        close_btn.pack(side=tk.LEFT, padx=5)

    def load_account_data(self):
        """تحميل بيانات الحساب"""
        try:
            if self.sheet_name not in self.excel.workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{self.sheet_name}' غير موجود")
                self.destroy()
                return

            ws = self.excel.workbook[self.sheet_name]

            # الرصيد الافتتاحي - فحص الخليتين A8 و A9 للتوافق مع التنسيقات المختلفة
            opening_balance = 0

            # فحص A8 أولاً (التنسيق الجديد)
            try:
                cell_a8 = ws['A8']
                if cell_a8.value and isinstance(cell_a8.value, (int, float)):
                    opening_balance = float(cell_a8.value)
            except (ValueError, TypeError):
                pass

            # إذا لم يتم العثور على رصيد، فحص A9 (التنسيق القديم)
            if opening_balance == 0:
                try:
                    cell_a9 = ws['A9']
                    if cell_a9.value and isinstance(cell_a9.value, (int, float)):
                        opening_balance = float(cell_a9.value)
                except (ValueError, TypeError):
                    pass
            self.opening_balance_label.config(text=f"الرصيد الافتتاحي: {opening_balance:,.2f}")

            # حساب الرصيد الحالي وإحصائيات المستندات من جميع الجداول
            documents_count = 0
            total_amount = 0
            documents_list = []

            print(f"🔍 فحص جميع الجداول في الحساب: {self.sheet_name}")

            # تحديد نطاقات الجداول في الورقة
            table_ranges = self._identify_table_ranges(ws)
            print(f"📊 تم العثور على {len(table_ranges)} جدول")

            # فحص كل جدول على حدة
            for table_index, (start_row, end_row) in enumerate(table_ranges, 1):
                print(f"📋 فحص الجدول {table_index} (الصفوف {start_row}-{end_row})")

                # فحص جميع الأقسام في هذا الجدول
                for section in range(6):
                    col_start = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                    # تحديد نطاق الصفوف للمستندات في هذا الجدول
                    doc_start_row = max(start_row + 2, 10)  # بداية المستندات
                    doc_end_row = min(end_row - 3, start_row + 25)  # نهاية المستندات

                    for row in range(doc_start_row, doc_end_row + 1):
                        try:
                            amount = ws.cell(row=row, column=col_start).value
                            doc_num = ws.cell(row=row, column=col_start+1).value
                            pay_num = ws.cell(row=row, column=col_start+2).value

                            # فحص أكثر دقة للمستندات الحقيقية
                            if self._is_valid_document(amount, doc_num, pay_num):
                                # محاولة تحويل المبلغ إلى رقم
                                try:
                                    amount_value = float(str(amount).strip())
                                    if amount_value > 0:  # فقط المبالغ الموجبة
                                        documents_count += 1
                                        total_amount += amount_value
                                        documents_list.append((
                                            amount_value,
                                            str(doc_num).strip(),
                                            str(pay_num).strip(),
                                            f"الجدول {table_index} - القسم {section + 1}"
                                        ))
                                        print(f"  ✅ مستند: {amount_value} - {doc_num} - {pay_num}")
                                except (ValueError, TypeError):
                                    continue
                        except Exception as e:
                            print(f"  ⚠️ خطأ في الصف {row}: {str(e)}")
                            continue

            print(f"📊 إجمالي المستندات الموجودة: {documents_count}")
            print(f"💰 إجمالي المبالغ: {total_amount:,.2f}")

            # الرصيد الحالي
            current_balance = opening_balance + total_amount
            self.current_balance_label.config(text=f"الرصيد الحالي: {current_balance:,.2f}")

            # إحصائيات المستندات
            self.documents_count_label.config(text=f"عدد المستندات: {documents_count}")
            self.total_amount_label.config(text=f"إجمالي المبالغ: {total_amount:,.2f}")

            # تحميل المستندات في الجدول (آخر 50 مستند لعرض أفضل)
            print(f"📋 عرض آخر {min(50, len(documents_list))} مستند")
            for doc in documents_list[-50:]:  # آخر 50 مستند
                self.documents_tree.insert('', tk.END, values=doc)

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الحساب: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الحساب: {str(e)}")

    def _identify_table_ranges(self, ws):
        """تحديد نطاقات الجداول في الورقة"""
        try:
            table_ranges = []
            current_table_start = None

            # البحث عن عناوين الجداول
            for row in range(1, ws.max_row + 1):
                for col in range(1, 19):  # فحص الأعمدة الرئيسية
                    cell = ws.cell(row=row, column=col)
                    if cell.value and isinstance(cell.value, str):
                        cell_text = str(cell.value).strip()
                        # البحث عن عناوين الجداول
                        if (('حساب' in cell_text or 'تابع' in cell_text) and
                            'رقم' not in cell_text and len(cell_text) > 5):
                            if current_table_start is not None:
                                # إنهاء الجدول السابق
                                table_ranges.append((current_table_start, row - 1))
                            current_table_start = row
                            print(f"📊 وجد جدول في الصف {row}: {cell_text}")
                            break

            # إضافة الجدول الأخير
            if current_table_start is not None:
                table_ranges.append((current_table_start, ws.max_row))

            # إذا لم يتم العثور على جداول، افترض جدول واحد
            if not table_ranges:
                table_ranges = [(1, ws.max_row)]
                print("📊 لم يتم العثور على عناوين جداول - افتراض جدول واحد")

            return table_ranges

        except Exception as e:
            print(f"❌ خطأ في تحديد نطاقات الجداول: {str(e)}")
            return [(1, ws.max_row)]  # افتراض جدول واحد في حالة الخطأ

    def _is_valid_document(self, amount, doc_num, pay_num):
        """فحص إذا كان هذا مستند صحيح"""
        try:
            # فحص وجود القيم
            if not (amount and doc_num and pay_num):
                return False

            # فحص المبلغ
            amount_str = str(amount).strip()
            if amount_str in ['', '0', '0.0']:
                return False

            # فحص رقم المستند
            doc_str = str(doc_num).strip()
            if doc_str in ['', 'رقم المستند', 'رقم المستند:', 'رصيد افتتاحي', 'ما قبله']:
                return False

            # فحص رقم القبض/التأدية
            pay_str = str(pay_num).strip()
            if pay_str in ['', 'رقم القبض', 'رقم القبض:', 'رقم التأدية', 'رقم التأدية:']:
                return False

            # فحص إذا كان المبلغ رقمياً
            try:
                amount_value = float(amount_str)
                return amount_value > 0
            except (ValueError, TypeError):
                return False

        except Exception as e:
            print(f"❌ خطأ في فحص صحة المستند: {str(e)}")
            return False

    def _calculate_account_balance(self, ws):
        """حساب رصيد الحساب الصحيح من جميع الجداول"""
        try:
            # الحصول على الرصيد الافتتاحي
            opening_balance = 0
            for location in ['A8', 'A9', 'A7']:
                try:
                    cell = ws[location]
                    if cell.value and isinstance(cell.value, (int, float)):
                        opening_balance = float(cell.value)
                        break
                except:
                    continue

            # حساب مجموع المستندات من جميع الجداول
            total_documents = 0
            table_ranges = self._identify_table_ranges(ws)

            for start_row, end_row in table_ranges:
                for section in range(6):
                    col_start = 1 + (section * 3)
                    doc_start_row = max(start_row + 2, 10)
                    doc_end_row = min(end_row - 3, start_row + 25)

                    for row in range(doc_start_row, doc_end_row + 1):
                        try:
                            amount = ws.cell(row=row, column=col_start).value
                            doc_num = ws.cell(row=row, column=col_start+1).value
                            pay_num = ws.cell(row=row, column=col_start+2).value

                            if self._is_valid_document(amount, doc_num, pay_num):
                                amount_value = float(str(amount).strip())
                                if amount_value > 0:
                                    total_documents += amount_value
                        except:
                            continue

            return opening_balance + total_documents

        except Exception as e:
            print(f"❌ خطأ في حساب رصيد الحساب: {str(e)}")
            # في حالة الخطأ، حاول قراءة الرصيد من A33
            try:
                balance_cell = ws['A33']
                return float(balance_cell.value) if balance_cell.value else 0
            except:
                return 0

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def export_details(self):
        """تصدير تفاصيل الحساب إلى ملف Excel"""
        try:
            from tkinter import filedialog
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from datetime import datetime

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ تفاصيل الحساب",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"تفاصيل_الحساب_{self.account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                return

            # إنشاء ملف Excel جديد
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = f"تفاصيل {self.account_name}"
            ws.sheet_properties.rightToLeft = True

            # الترويسة
            ws.merge_cells('A1:F1')
            ws['A1'] = "المملكة الأردنية الهاشمية - وزارة الصحة"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A2:F2')
            ws['A2'] = f"تفاصيل الحساب: {self.account_name}"
            ws['A2'].font = Font(bold=True, size=12)
            ws['A2'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A3:F3')
            ws['A3'] = f"تاريخ التصدير: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
            ws['A3'].font = Font(size=10)
            ws['A3'].alignment = Alignment(horizontal='center')

            # معلومات الحساب
            row = 5
            ws[f'A{row}'] = "رقم الحساب:"
            ws[f'B{row}'] = self.account_num
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "اسم الحساب:"
            ws[f'B{row}'] = self.account_name
            ws[f'A{row}'].font = Font(bold=True)

            # الحصول على البيانات من النافذة الحالية
            opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
            current_balance = self.current_balance_label.cget("text").split(": ")[1]
            documents_count = self.documents_count_label.cget("text").split(": ")[1]
            total_amount = self.total_amount_label.cget("text").split(": ")[1]

            row += 1
            ws[f'A{row}'] = "الرصيد الافتتاحي:"
            ws[f'B{row}'] = opening_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "الرصيد الحالي:"
            ws[f'B{row}'] = current_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "عدد المستندات:"
            ws[f'B{row}'] = documents_count
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "إجمالي المبالغ:"
            ws[f'B{row}'] = total_amount
            ws[f'A{row}'].font = Font(bold=True)

            # عناوين جدول المستندات
            row += 3
            headers = ['المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col)
                cell.value = header
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(all=Side(style='thin'))

            # بيانات المستندات
            for item in self.documents_tree.get_children():
                row += 1
                values = self.documents_tree.item(item)['values']
                for col, value in enumerate(values, 1):
                    cell = ws.cell(row=row, column=col)
                    cell.value = value
                    cell.border = Border(all=Side(style='thin'))
                    cell.alignment = Alignment(horizontal='center')
                    if col == 1:  # عمود المبلغ
                        cell.number_format = '#,##0.00'

            # تنسيق الأعمدة
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 20
            ws.column_dimensions['C'].width = 15
            ws.column_dimensions['D'].width = 15

            # حفظ الملف
            wb.save(filename)
            messagebox.showinfo("نجاح", f"تم تصدير تفاصيل الحساب إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def print_report(self):
        """طباعة تقرير الحساب"""
        try:
            import tempfile
            import os
            from datetime import datetime

            # إنشاء ملف HTML للطباعة
            html_content = self.generate_print_html()

            # حفظ الملف المؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            import webbrowser
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("طباعة", "تم فتح التقرير في المتصفح للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إعداد الطباعة: {str(e)}")

    def generate_print_html(self):
        """إنشاء محتوى HTML للطباعة"""
        from datetime import datetime

        # الحصول على البيانات
        opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
        current_balance = self.current_balance_label.cget("text").split(": ")[1]
        documents_count = self.documents_count_label.cget("text").split(": ")[1]
        total_amount = self.total_amount_label.cget("text").split(": ")[1]

        # بناء جدول المستندات
        documents_rows = ""
        for item in self.documents_tree.get_children():
            values = self.documents_tree.item(item)['values']
            documents_rows += f"""
            <tr>
                <td>{values[0]}</td>
                <td>{values[1]}</td>
                <td>{values[2]}</td>
                <td>{values[3]}</td>
            </tr>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تفاصيل الحساب - {self.account_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .info-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .info-table th {{ background-color: #f2f2f2; font-weight: bold; }}
                .documents-table {{ width: 100%; border-collapse: collapse; }}
                .documents-table th, .documents-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .documents-table th {{ background-color: #366092; color: white; }}
                @media print {{
                    body {{ margin: 0; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>المملكة الأردنية الهاشمية</h1>
                <h2>وزارة الصحة</h2>
                <h3>تفاصيل الحساب: {self.account_name}</h3>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M')}</p>
            </div>

            <table class="info-table">
                <tr><th>رقم الحساب</th><td>{self.account_num}</td></tr>
                <tr><th>اسم الحساب</th><td>{self.account_name}</td></tr>
                <tr><th>الرصيد الافتتاحي</th><td>{opening_balance}</td></tr>
                <tr><th>الرصيد الحالي</th><td>{current_balance}</td></tr>
                <tr><th>عدد المستندات</th><td>{documents_count}</td></tr>
                <tr><th>إجمالي المبالغ</th><td>{total_amount}</td></tr>
            </table>

            <h3>المستندات:</h3>
            <table class="documents-table">
                <thead>
                    <tr>
                        <th>المبلغ</th>
                        <th>رقم المستند</th>
                        <th>رقم التأدية</th>
                        <th>القسم</th>
                    </tr>
                </thead>
                <tbody>
                    {documents_rows}
                </tbody>
            </table>

            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
        """

        return html_content
