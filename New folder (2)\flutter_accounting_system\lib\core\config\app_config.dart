/// إعدادات التطبيق الأساسية
class AppConfig {
  static const String appName = 'نظام إدارة المستندات المحاسبية';
  static const String appVersion = '2.0.0';
  static const String organization = 'وزارة الصحة الأردنية';
  
  // إعدادات قاعدة البيانات
  static const String databaseName = 'accounting_system.db';
  static const int databaseVersion = 1;
  
  // إعدادات التخزين
  static const String prefsKeyUser = 'current_user';
  static const String prefsKeySettings = 'app_settings';
  static const String prefsKeyTheme = 'theme_mode';
  
  // إعدادات Excel
  static const String defaultExcelFileName = 'accounting_system.xlsx';
  static const String backupFolderName = 'backups';
  
  // إعدادات التصدير
  static const String exportFolderName = 'exports';
  static const String pdfFolderName = 'pdf_reports';
  
  // إعدادات الأمان
  static const int passwordMinLength = 6;
  static const int sessionTimeoutMinutes = 30;
  
  // إعدادات الواجهة
  static const double defaultPadding = 16.0;
  static const double defaultRadius = 12.0;
  static const double defaultElevation = 4.0;
  
  // ألوان النظام المحاسبي
  static const String amountColorHex = '#27AE60';      // أخضر للمبالغ
  static const String documentColorHex = '#3498DB';    // أزرق لأرقام المستندات
  static const String paymentColorHex = '#E74C3C';     // أحمر لأرقام التأدية
  
  // إعدادات الرسوم البيانية
  static const int chartAnimationDuration = 1500;
  static const double chartDefaultHeight = 300.0;
  
  // إعدادات التقارير
  static const int defaultItemsPerPage = 20;
  static const String defaultDateFormat = 'dd/MM/yyyy';
  static const String defaultCurrency = 'JOD';
  
  // رسائل النظام
  static const String successMessage = 'تم بنجاح';
  static const String errorMessage = 'حدث خطأ';
  static const String warningMessage = 'تحذير';
  static const String infoMessage = 'معلومات';
  
  // أذونات المستخدمين
  static const List<String> adminPermissions = [
    'add_account',
    'edit_account',
    'delete_account',
    'add_document',
    'edit_document',
    'delete_document',
    'view_reports',
    'export_data',
    'manage_users',
    'system_settings',
  ];
  
  static const List<String> accountantPermissions = [
    'add_account',
    'edit_account',
    'add_document',
    'edit_document',
    'view_reports',
    'export_data',
  ];
  
  static const List<String> userPermissions = [
    'add_document',
    'view_reports',
  ];
  
  // أنواع المستندات
  static const List<String> documentTypes = [
    'مصروف',
    'إيراد',
    'تحويل',
    'رصيد افتتاحي',
  ];
  
  // أنواع الحسابات
  static const List<String> accountTypes = [
    'حساب مصروفات',
    'حساب إيرادات',
    'حساب أصول',
    'حساب خصوم',
    'حساب رأس المال',
  ];
  
  // إعدادات التحقق
  static bool isValidAccountNumber(String accountNumber) {
    return accountNumber.isNotEmpty && 
           accountNumber.length >= 3 && 
           accountNumber.length <= 10;
  }
  
  static bool isValidAmount(double amount) {
    return amount > 0 && amount <= *********.99;
  }
  
  static bool isValidDocumentNumber(String documentNumber) {
    return documentNumber.isNotEmpty && documentNumber.length <= 50;
  }
  
  static bool isValidPaymentNumber(String paymentNumber) {
    return paymentNumber.isNotEmpty && paymentNumber.length <= 50;
  }
}
