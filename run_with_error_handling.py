#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع معالجة مباشرة وفورية للأخطاء
"""

import sys
import traceback
import threading
import time
import json
import os
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

# ملف حفظ معلومات تسجيل الدخول
LOGIN_SAVE_FILE = "saved_login.json"

class ErrorHandler:
    """معالج الأخطاء المتقدم"""
    
    def __init__(self):
        self.error_count = 0
        self.last_error_time = None
        self.error_log = []
    
    def handle_error(self, error_type, error_msg, traceback_str=None):
        """معالجة الأخطاء مع عرض فوري"""
        self.error_count += 1
        self.last_error_time = datetime.now()
        
        error_info = {
            'time': self.last_error_time.strftime('%H:%M:%S'),
            'type': error_type,
            'message': error_msg,
            'traceback': traceback_str
        }
        
        self.error_log.append(error_info)
        
        # عرض فوري للخطأ
        print(f"\n{'='*60}")
        print(f"🚨 خطأ #{self.error_count} - {error_info['time']}")
        print(f"📝 النوع: {error_type}")
        print(f"💬 الرسالة: {error_msg}")
        
        if traceback_str:
            print(f"📋 التفاصيل:")
            print(traceback_str)
        
        print(f"{'='*60}\n")
        
        # اقتراح حلول
        self.suggest_solution(error_type, error_msg)
    
    def suggest_solution(self, error_type, error_msg):
        """اقتراح حلول للأخطاء الشائعة"""
        suggestions = {
            'ImportError': [
                "💡 تأكد من وجود جميع الملفات المطلوبة",
                "📦 تحقق من تثبيت المكتبات المطلوبة",
                "🔍 تأكد من أن المسار صحيح"
            ],
            'FileNotFoundError': [
                "📁 تأكد من وجود الملف في المسار الصحيح",
                "📋 تحقق من اسم الملف والامتداد",
                "🔍 تأكد من صلاحيات الوصول للملف"
            ],
            'PermissionError': [
                "🔐 تأكد من صلاحيات الكتابة/القراءة",
                "👤 شغل التطبيق كمدير إذا لزم الأمر",
                "📁 تحقق من أن الملف غير مفتوح في برنامج آخر"
            ],
            'AttributeError': [
                "🔍 تحقق من أن الكائن يحتوي على الخاصية المطلوبة",
                "📝 تأكد من تهيئة الكائن بشكل صحيح",
                "🔄 جرب إعادة تشغيل التطبيق"
            ]
        }
        
        if error_type in suggestions:
            print("🔧 اقتراحات للحل:")
            for suggestion in suggestions[error_type]:
                print(f"   {suggestion}")
        else:
            print("🔧 اقتراحات عامة:")
            print("   🔄 جرب إعادة تشغيل التطبيق")
            print("   📋 تحقق من ملفات النظام")
            print("   💻 أعد تشغيل الكمبيوتر إذا لزم الأمر")
        
        print()

class AppMonitor:
    """مراقب التطبيق"""
    
    def __init__(self, error_handler):
        self.error_handler = error_handler
        self.app = None
        self.monitoring = True
        self.start_time = datetime.now()
    
    def start_monitoring(self):
        """بدء مراقبة التطبيق"""
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
    
    def _monitor_loop(self):
        """حلقة المراقبة"""
        while self.monitoring:
            try:
                # مراقبة حالة التطبيق
                if self.app and hasattr(self.app, 'root'):
                    if not self.app.root.winfo_exists():
                        print("ℹ️ تم إغلاق التطبيق بواسطة المستخدم")
                        self.monitoring = False
                        break
                
                time.sleep(1)  # فحص كل ثانية
                
            except Exception as e:
                self.error_handler.handle_error(
                    "MonitoringError", 
                    str(e), 
                    traceback.format_exc()
                )
                time.sleep(5)  # انتظار أطول عند حدوث خطأ
    
    def set_app(self, app):
        """تعيين التطبيق للمراقبة"""
        self.app = app
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring = False

def load_saved_login():
    """تحميل معلومات تسجيل الدخول المحفوظة"""
    try:
        if os.path.exists(LOGIN_SAVE_FILE):
            with open(LOGIN_SAVE_FILE, 'r', encoding='utf-8') as f:
                login_data = json.load(f)
            
            if login_data.get("auto_login", False):
                print(f"🔓 تم العثور على معلومات تسجيل دخول محفوظة: {login_data['username']}")
                return login_data
        
        return None
    except Exception as e:
        print(f"⚠️ خطأ في تحميل معلومات تسجيل الدخول: {e}")
        return None

def save_login_info(username):
    """حفظ معلومات تسجيل الدخول"""
    try:
        login_data = {
            "username": username,
            "last_login": datetime.now().isoformat(),
            "auto_login": True
        }
        
        with open(LOGIN_SAVE_FILE, 'w', encoding='utf-8') as f:
            json.dump(login_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ معلومات تسجيل الدخول: {username}")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ معلومات تسجيل الدخول: {e}")
        return False

def create_enhanced_app(root, error_handler, monitor):
    """إنشاء التطبيق مع معالجة محسنة للأخطاء"""
    try:
        print("📦 استيراد وحدات التطبيق...")
        
        from app import AccountingApp
        from user_manager import UserManager
        
        print("✅ تم استيراد الوحدات بنجاح")
        
        # تحميل معلومات تسجيل الدخول المحفوظة
        saved_login = load_saved_login()
        
        if saved_login:
            print(f"🚀 إنشاء التطبيق مع تسجيل دخول تلقائي: {saved_login['username']}")
            
            # إنشاء التطبيق
            app = AccountingApp(root)
            
            # تعيين المستخدم الحالي
            if hasattr(app, 'user_manager'):
                app.user_manager.current_user = {
                    'username': saved_login['username'],
                    'role': 'admin',
                    'permissions': ['add_account', 'delete_account', 'add_document', 'view_reports', 'manage_users']
                }
            
            # إغلاق نافذة تسجيل الدخول
            if hasattr(app, 'login_window') and app.login_window:
                try:
                    if hasattr(app.login_window, 'root'):
                        app.login_window.root.destroy()
                    print("✅ تم إغلاق نافذة تسجيل الدخول")
                except Exception as e:
                    error_handler.handle_error("LoginWindowError", str(e))
            
            # إظهار النافذة الرئيسية
            app.root.deiconify()
            
            # إعداد الواجهة
            if hasattr(app, 'setup_modern_ui'):
                print("🎨 إعداد الواجهة الحديثة...")
                app.setup_modern_ui()
            
            # بدء التحديث التلقائي
            if hasattr(app, 'start_auto_refresh'):
                try:
                    app.start_auto_refresh()
                    print("🔄 تم بدء التحديث التلقائي")
                except Exception as e:
                    error_handler.handle_error("AutoRefreshError", str(e))
            
            # تحديث عنوان النافذة
            app.root.title(f"نظام إدارة المستندات المحاسبية - {saved_login['username']} (تسجيل دخول تلقائي)")
            
            print(f"✅ تم تسجيل الدخول تلقائياً: {saved_login['username']}")
            
        else:
            print("🔐 إنشاء التطبيق مع نافذة تسجيل الدخول العادية")
            
            # إنشاء التطبيق عادي
            app = AccountingApp(root)
            
            # تحسين دالة تسجيل الدخول لحفظ المعلومات
            original_on_login_success = app.on_login_success
            
            def enhanced_on_login_success():
                try:
                    # حفظ معلومات تسجيل الدخول
                    if hasattr(app, 'user_manager') and app.user_manager.current_user:
                        username = app.user_manager.current_user.get('username', 'admin')
                        save_login_info(username)
                    
                    # استدعاء الدالة الأصلية
                    original_on_login_success()
                    
                except Exception as e:
                    error_handler.handle_error("LoginSuccessError", str(e), traceback.format_exc())
            
            app.on_login_success = enhanced_on_login_success
        
        # تعيين التطبيق للمراقبة
        monitor.set_app(app)
        
        # إعداد معالج الأخطاء للتطبيق
        def app_error_handler(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                print("\n⚠️ تم إيقاف التطبيق بواسطة المستخدم (Ctrl+C)")
                return
            
            error_handler.handle_error(
                exc_type.__name__,
                str(exc_value),
                "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            )
        
        sys.excepthook = app_error_handler
        
        return app
        
    except Exception as e:
        error_handler.handle_error("AppCreationError", str(e), traceback.format_exc())
        raise

def main():
    """الدالة الرئيسية"""
    
    print("=" * 80)
    print("🚀 نظام إدارة المستندات المحاسبية - تشغيل مع معالجة متقدمة للأخطاء")
    print("=" * 80)
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🛡️ معالجة فورية ومباشرة للأخطاء مفعلة")
    print("🔍 مراقبة مستمرة لحالة التطبيق")
    print("=" * 80)
    
    # إنشاء معالج الأخطاء
    error_handler = ErrorHandler()
    
    # إنشاء مراقب التطبيق
    monitor = AppMonitor(error_handler)
    
    try:
        print("🔧 إعداد البيئة...")
        
        # بدء مراقبة التطبيق
        monitor.start_monitoring()
        print("✅ تم بدء مراقبة التطبيق")
        
        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        
        # إنشاء التطبيق
        print("🏗️ إنشاء التطبيق...")
        app = create_enhanced_app(root, error_handler, monitor)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        print("▶️ بدء تشغيل التطبيق...")
        print("🛡️ معالج الأخطاء نشط ومراقب التطبيق يعمل")
        print("-" * 80)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف التطبيق بواسطة المستخدم")
        
    except Exception as e:
        error_handler.handle_error("MainError", str(e), traceback.format_exc())
        
    finally:
        # إيقاف المراقبة
        monitor.stop_monitoring()
        
        # عرض ملخص الأخطاء
        print(f"\n📊 ملخص الجلسة:")
        print(f"🕒 مدة التشغيل: {datetime.now() - monitor.start_time}")
        print(f"🚨 عدد الأخطاء: {error_handler.error_count}")
        
        if error_handler.error_count > 0:
            print(f"⏰ آخر خطأ: {error_handler.last_error_time.strftime('%H:%M:%S')}")
            print("\n📋 سجل الأخطاء:")
            for i, error in enumerate(error_handler.error_log[-5:], 1):  # آخر 5 أخطاء
                print(f"   {i}. [{error['time']}] {error['type']}: {error['message']}")
        
        print(f"\n🔚 انتهاء التشغيل - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

if __name__ == "__main__":
    main()
