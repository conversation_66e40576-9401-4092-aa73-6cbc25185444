# تطبيق الويب لنظام إدارة المستندات المحاسبية

## وصف التطبيق
تطبيق ويب بسيط ومنفصل تماماً يحاكي وظائف نظام إدارة المستندات المحاسبية الأصلي بواجهة HTML حديثة.

## المتطلبات
- Python 3.7 أو أحدث
- Flask
- openpyxl

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
# تشغيل ملف التثبيت
setup.bat

# أو تثبيت يدوي
pip install Flask openpyxl
```

### 2. تشغيل التطبيق
```bash
# تشغيل ملف التطبيق
run_web_app.bat

# أو تشغيل يدوي
python web_app.py
```

### 3. الوصول للتطبيق
- افتح المتصفح واذهب إلى: `http://localhost:5000`
- معلومات تسجيل الدخول: `admin / admin`

## الميزات

### ✅ الوظائف المتاحة:
- **تسجيل الدخول**: نظام بسيط لتسجيل الدخول
- **إدارة الحسابات**: عرض قائمة الحسابات مع الأرصدة
- **تفاصيل الحساب**: عرض تفاصيل كل حساب مع المستندات
- **إضافة مستند**: إضافة مستندات جديدة للحسابات
- **إضافة حساب**: إنشاء حسابات جديدة
- **البحث**: البحث في المستندات بمعايير مختلفة

### 📊 ملف البيانات:
- يتم إنشاء ملف `web_accounting_data.xlsx` تلقائياً
- يحتوي على جميع بيانات الحسابات والمستندات
- منفصل تماماً عن ملف البيانات الأصلي

### 🎨 الواجهة:
- تصميم حديث ومتجاوب
- دعم كامل للغة العربية
- واجهة سهلة الاستخدام
- ألوان وتأثيرات جذابة

## الاستخدام

### 1. تسجيل الدخول
- استخدم: `admin / admin`

### 2. إدارة الحسابات
- عرض جميع الحسابات
- النقر على "تفاصيل" لرؤية مستندات الحساب

### 3. إضافة حساب جديد
- أدخل رقم الحساب واسمه
- حدد الرصيد الافتتاحي (اختياري)

### 4. إضافة مستند
- اختر الحساب
- أدخل المبلغ ورقم المستند ورقم التأدية

### 5. البحث
- ابحث في جميع المستندات
- اختر نوع البحث (الكل، رقم المستند، رقم التأدية، المبلغ)

## ملاحظات مهمة
- التطبيق منفصل تماماً عن النظام الأصلي
- لا يؤثر على ملفات أو بيانات النظام الأصلي
- يستخدم ملف Excel منفصل للبيانات
- مناسب للاختبار والتجربة
