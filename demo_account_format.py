#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تنسيق صفحة الحساب الحالي مع مثال تفصيلي
"""

import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from datetime import datetime
import os

def create_demo_account():
    """إنشاء حساب تجريبي لعرض التنسيق"""
    
    # إنشاء ملف Excel جديد
    wb = openpyxl.Workbook()
    if 'Sheet' in wb.sheetnames:
        wb.remove(wb['Sheet'])
    
    # بيانات الحساب التجريبي
    account_num = "1001"
    account_name = "حساب الأدوية والمستلزمات الطبية"
    initial_balance = 50000.00
    sheet_name = f"{account_num}-{account_name}"
    
    # إنشاء الورقة
    ws = wb.create_sheet(sheet_name)
    ws.sheet_properties.rightToLeft = True
    
    # إعداد الترويسة
    setup_header(ws, account_num, account_name)
    
    # إعداد الأقسام
    setup_sections(ws, initial_balance)
    
    # إضافة بيانات تجريبية
    add_sample_data(ws)
    
    # حفظ الملف
    demo_file = "demo_account_format.xlsx"
    wb.save(demo_file)
    print(f"✅ تم إنشاء الملف التجريبي: {demo_file}")
    
    # عرض تفاصيل التنسيق
    display_format_details()
    
    return demo_file

def setup_header(ws, account_num, account_name):
    """إعداد ترويسة الصفحة"""
    
    # الترويسة الرسمية
    ws.merge_cells('A1:R1')
    ws['A1'] = "المملكة الأردنية الهاشمية"
    ws['A1'].font = Font(size=14, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    
    ws.merge_cells('A2:R2')
    ws['A2'] = "وزارة الصحة"
    ws['A2'].font = Font(size=12, bold=True)
    ws['A2'].alignment = Alignment(horizontal='center')
    
    ws.merge_cells('A3:R3')
    ws['A3'] = f"حساب: {account_name} - {account_num}"
    ws['A3'].font = Font(size=12, bold=True)
    ws['A3'].alignment = Alignment(horizontal='center')
    
    ws.merge_cells('A4:R4')
    current_date = datetime.now().strftime("%Y/%m/%d")
    ws['A4'] = f"تاريخ الإنشاء: {current_date}"
    ws['A4'].alignment = Alignment(horizontal='center')

def setup_sections(ws, initial_balance):
    """إعداد أقسام الصفحة"""
    
    # تعريف الحدود
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # إعداد عنوان الأقسام
    ws.merge_cells('A6:R6')
    ws['A6'] = "سجل المستندات والحوالات المالية"
    ws['A6'].font = Font(bold=True)
    ws['A6'].alignment = Alignment(horizontal='center')
    
    # إعداد الأقسام الستة
    for i in range(6):
        col = chr(65 + (i * 3))  # A, D, G, J, M, P
        
        # العناوين
        ws[f'{col}7'] = "المبلغ"
        ws[f'{chr(ord(col) + 1)}7'] = "مستند الصرف"
        ws[f'{chr(ord(col) + 2)}7'] = "رقم التأدية"
        
        # تنسيق العناوين
        for j in range(3):
            col_letter = chr(ord(col) + j)
            cell = ws[f'{col_letter}7']
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal='center')
            cell.border = thin_border
            
            # إضافة حدود لجميع الخلايا في العمود
            for row in range(8, 32):
                cell = ws[f'{col_letter}{row}']
                cell.border = thin_border
                cell.alignment = Alignment(horizontal='center')
        
        # القيم الأولية
        if i == 0:
            ws[f'{col}8'] = initial_balance
            ws[f'{chr(ord(col) + 1)}8'] = "رصيد افتتاحي"
        else:
            # ترحيل الرصيد من القسم السابق
            prev_col = chr(ord(col) - 3)
            ws[f'{col}8'] = f"={prev_col}32"
            ws[f'{chr(ord(col) + 1)}8'] = "ما قبله"
        
        # إعداد صف المجموع
        ws.merge_cells(f'{col}31:{chr(ord(col)+2)}31')
        ws[f'{col}31'] = "المجموع"
        ws[f'{col}31'].font = Font(bold=True)
        ws[f'{col}31'].fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
        ws[f'{col}31'].alignment = Alignment(horizontal='center')
        
        # صيغة المجموع
        ws[f'{col}32'] = f"=SUM({col}9:{col}30)"
        ws[f'{col}32'].font = Font(bold=True)
        ws[f'{chr(ord(col) + 1)}32'] = "الإجمالي"
        
        # تنسيق خلايا المجموع
        for j in range(3):
            col_letter = chr(ord(col) + j)
            cell = ws[f'{col_letter}32']
            cell.border = Border(
                top=Side(style='double'),
                bottom=Side(style='double'),
                left=Side(style='thin'),
                right=Side(style='thin')
            )
            cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")

def add_sample_data(ws):
    """إضافة بيانات تجريبية للعرض"""
    
    # بيانات تجريبية للقسم الأول
    sample_data = [
        (15000, "INV-001", "PAY-001"),
        (8500, "INV-002", "PAY-002"),
        (12000, "INV-003", "PAY-003"),
        (6750, "INV-004", "PAY-004"),
        (9200, "INV-005", "PAY-005"),
    ]
    
    # إضافة البيانات للقسم الأول (العمود A)
    for i, (amount, doc, pay) in enumerate(sample_data, start=9):
        ws[f'A{i}'] = amount
        ws[f'A{i}'].number_format = '#,##0.00'
        ws[f'B{i}'] = doc
        ws[f'C{i}'] = pay
    
    # بيانات تجريبية للقسم الثاني
    sample_data_2 = [
        (22000, "INV-006", "PAY-006"),
        (18500, "INV-007", "PAY-007"),
        (14000, "INV-008", "PAY-008"),
    ]
    
    # إضافة البيانات للقسم الثاني (العمود D)
    for i, (amount, doc, pay) in enumerate(sample_data_2, start=9):
        ws[f'D{i}'] = amount
        ws[f'D{i}'].number_format = '#,##0.00'
        ws[f'E{i}'] = doc
        ws[f'F{i}'] = pay

def display_format_details():
    """عرض تفاصيل التنسيق الحالي"""
    
    print("\n" + "="*80)
    print("📋 تفاصيل تنسيق صفحة الحساب الحالي")
    print("="*80)
    
    print("\n🏛️ الترويسة (الصفوف 1-4):")
    print("   الصف 1: المملكة الأردنية الهاشمية (خط 14، عريض، وسط)")
    print("   الصف 2: وزارة الصحة (خط 12، عريض، وسط)")
    print("   الصف 3: حساب: [اسم الحساب] - [رقم الحساب] (خط 12، عريض، وسط)")
    print("   الصف 4: تاريخ الإنشاء: [التاريخ] (وسط)")
    
    print("\n📊 تخطيط الأقسام:")
    print("   • 6 أقسام أفقية موزعة على 18 عمود (A-R)")
    print("   • كل قسم يحتوي على 3 أعمدة:")
    print("     - العمود الأول: المبلغ")
    print("     - العمود الثاني: مستند الصرف")
    print("     - العمود الثالث: رقم التأدية")
    
    print("\n📍 توزيع الأقسام:")
    print("   القسم 1: الأعمدة A-C")
    print("   القسم 2: الأعمدة D-F")
    print("   القسم 3: الأعمدة G-I")
    print("   القسم 4: الأعمدة J-L")
    print("   القسم 5: الأعمدة M-O")
    print("   القسم 6: الأعمدة P-R")
    
    print("\n📝 تفاصيل كل قسم:")
    print("   الصف 7: عناوين الأعمدة (رمادي، عريض)")
    print("   الصف 8: الرصيد الافتتاحي (القسم الأول) أو ترحيل من القسم السابق")
    print("   الصفوف 9-30: بيانات المستندات (22 صف)")
    print("   الصف 31: عنوان المجموع (أصفر فاتح، عريض)")
    print("   الصف 32: قيمة المجموع (رمادي، حدود مزدوجة)")
    
    print("\n🎨 الألوان المستخدمة:")
    print("   • العناوين: رمادي فاتح (#CCCCCC)")
    print("   • صف المجموع: أصفر فاتح (#FFEB9C)")
    print("   • الإجمالي: رمادي (#E6E6E6)")
    
    print("\n📏 الحدود والتنسيق:")
    print("   • حدود رفيعة لجميع الخلايا")
    print("   • حدود مزدوجة لصف المجموع")
    print("   • محاذاة وسط لجميع البيانات")
    print("   • تنسيق رقمي للمبالغ (#,##0.00)")
    
    print("\n💡 المشاكل في التنسيق الحالي:")
    print("   ❌ صعوبة في القراءة بسبب التوزيع الأفقي")
    print("   ❌ عدم وجود تاريخ للمستندات")
    print("   ❌ لا يوجد رقم تسلسلي")
    print("   ❌ صعوبة في الطباعة (عرض كبير)")
    print("   ❌ لا يوجد حقل للملاحظات")
    print("   ❌ ألوان محدودة وغير جذابة")
    
    print("\n✅ نقاط القوة:")
    print("   ✓ يستوعب عدد كبير من المستندات (132 مستند)")
    print("   ✓ حسابات تلقائية للمجاميع")
    print("   ✓ ترحيل تلقائي بين الأقسام")
    print("   ✓ تنسيق رسمي مناسب للجهات الحكومية")
    
    print("\n" + "="*80)

if __name__ == "__main__":
    demo_file = create_demo_account()
    print(f"\n🎯 تم إنشاء ملف العرض التوضيحي: {demo_file}")
    print("📂 افتح الملف في Excel لرؤية التنسيق الفعلي")
