#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة SearchWindow
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def test_search_window_import():
    """اختبار استيراد SearchWindow"""
    try:
        print("🔍 اختبار استيراد SearchWindow...")
        
        from search_window import SearchWindow
        print("✅ تم استيراد SearchWindow بنجاح")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد SearchWindow: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام في الاستيراد: {str(e)}")
        return False

def test_search_window_creation():
    """اختبار إنشاء SearchWindow"""
    try:
        print("\n🔧 اختبار إنشاء SearchWindow...")
        
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # محاكاة parent مع excel
        class MockParent:
            def __init__(self):
                self.root = root
                self.excel = MockExcel()
        
        class MockExcel:
            def __init__(self):
                import openpyxl
                self.workbook = openpyxl.Workbook()
                # إضافة ورقة اختبار
                ws = self.workbook.active
                ws.title = "حساب اختبار"
                # إضافة بعض البيانات للاختبار
                ws['A10'] = 1000
                ws['B10'] = "DOC001"
                ws['C10'] = "PAY001"
        
        # إنشاء parent محاكي
        parent = MockParent()
        
        # استيراد وإنشاء SearchWindow
        from search_window import SearchWindow
        search_window = SearchWindow(parent)
        
        print("✅ تم إنشاء SearchWindow بنجاح")
        
        # إغلاق النوافذ
        search_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء SearchWindow: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_app_search_function():
    """اختبار دالة البحث في التطبيق"""
    try:
        print("\n🔧 اختبار دالة البحث في التطبيق...")
        
        # قراءة ملف app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص الإصلاح
        if "window = SearchWindow(self)" in content:
            print("✅ تم إصلاح استدعاء SearchWindow")
        else:
            print("❌ لم يتم إصلاح استدعاء SearchWindow")
            return False
        
        # فحص عدم وجود الاستدعاء الخاطئ
        if "SearchWindow(self.root, self.excel)" in content:
            print("❌ الاستدعاء الخاطئ ما زال موجود")
            return False
        else:
            print("✅ تم حذف الاستدعاء الخاطئ")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص دالة البحث: {str(e)}")
        return False

def test_app_integration():
    """اختبار التكامل مع التطبيق"""
    try:
        print("\n🔗 اختبار التكامل مع التطبيق...")
        
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # محاولة استيراد وإنشاء التطبيق
        from app import AccountingApp
        app = AccountingApp(root)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # فحص وجود دالة البحث
        if hasattr(app, 'search_accounts'):
            print("✅ دالة search_accounts موجودة")
        else:
            print("❌ دالة search_accounts مفقودة")
            return False
        
        # إغلاق النافذة
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_fixed_run_script():
    """إنشاء سكريبت تشغيل مُصلح"""
    try:
        print("\n📝 إنشاء سكريبت تشغيل مُصلح...")
        
        script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشغيل مُصلح للتطبيق
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """تشغيل التطبيق مع معالجة شاملة للأخطاء"""
    try:
        print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية...")
        print("🔧 الإصدار: 2.1 - مُصلح")
        
        # التحقق من المتطلبات
        print("📋 فحص المتطلبات...")
        
        try:
            import openpyxl
            print("✅ مكتبة openpyxl متاحة")
        except ImportError:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\\nيرجى تثبيتها: pip install openpyxl")
            return
        
        # فحص الملفات المطلوبة
        required_files = [
            'app.py', 'excel_manager.py', 'user_manager.py', 
            'search_window.py', 'account_balances_window.py'
        ]
        
        missing_files = []
        for file in required_files:
            if os.path.exists(file):
                print(f"✅ {file}")
            else:
                print(f"❌ {file} مفقود")
                missing_files.append(file)
        
        if missing_files:
            messagebox.showerror("خطأ", f"ملفات مفقودة: {', '.join(missing_files)}")
            return
        
        # استيراد التطبيق
        try:
            from app import AccountingApp
            print("✅ تم استيراد التطبيق")
        except ImportError as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد التطبيق: {str(e)}")
            return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ عام في الاستيراد: {str(e)}")
            return
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية")
        root.geometry("1200x700")
        root.resizable(True, True)
        
        # تعيين الخط العربي
        root.option_add("*font", "Arial 12")
        
        print("✅ تم إعداد النافذة الرئيسية")
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        print("🎉 النظام جاهز للاستخدام!")
        print("🔐 سيتم عرض نافذة تسجيل الدخول...")
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل النظام: {str(e)}"
        print(f"❌ {error_msg}")
        messagebox.showerror("خطأ في التشغيل", error_msg)
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
        
        with open('run_app_final.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("✅ تم إنشاء run_app_final.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء السكريبت: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار إصلاح مشكلة SearchWindow")
    print("=" * 60)
    
    success_count = 0
    total_tests = 5
    
    # اختبار استيراد SearchWindow
    if test_search_window_import():
        success_count += 1
    
    # اختبار إنشاء SearchWindow
    if test_search_window_creation():
        success_count += 1
    
    # اختبار دالة البحث في التطبيق
    if test_app_search_function():
        success_count += 1
    
    # اختبار التكامل
    if test_app_integration():
        success_count += 1
    
    # إنشاء سكريبت التشغيل
    if create_fixed_run_script():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات إصلاح SearchWindow!")
        print("\n📝 الإصلاحات المطبقة:")
        print("✅ إصلاح استدعاء SearchWindow في app.py")
        print("✅ تحديث المعاملات المرسلة للنافذة")
        print("✅ التأكد من عمل دالة البحث")
        print("✅ اختبار التكامل مع التطبيق")
        print("✅ إنشاء سكريبت تشغيل نهائي")
        
        print("\n🚀 لتشغيل التطبيق:")
        print("python run_app_final.py")
        print("أو")
        print("python app.py")
        
        print("\n🔍 الآن يمكن استخدام البحث بدون أخطاء!")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات إصلاح SearchWindow")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
