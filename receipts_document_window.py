import tkinter as tk
from tkinter import ttk, messagebox
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
import os

class AddReceiptsDocumentWindow(tk.Toplevel):
    """نافذة إضافة مستند مقبوضات"""

    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("إضافة مستند مقبوضات")
        self.parent = parent

        # تكوين النافذة
        self.geometry("700x600")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent.root)
        self.grab_set()

        # متغيرات النموذج
        self.document_num_var = tk.StringVar()
        self.total_amount_var = tk.StringVar(value="0.000")

        # متغير رسالة الحالة
        self.status_message_var = tk.StringVar(value="جاهز لإضافة مستند جديد")

        # قائمة الحسابات المتاحة
        self.available_accounts = []

        # قائمة توزيع المبالغ
        self.distribution_rows = []

        # إنشاء الواجهة
        self.create_interface()

        # تحميل قائمة الحسابات
        self.load_accounts_list()

        # إعداد التحديث التلقائي لقائمة الحسابات
        self.setup_auto_refresh()

        # توسيط النافذة
        self.center_window()

        # ربط إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # عنوان النافذة
        title_label = ttk.Label(main_frame,
                               text="🧾 إضافة مستند مقبوضات",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))

        # إطار معلومات المستند
        document_frame = ttk.LabelFrame(main_frame,
                                       text="📋 معلومات المستند",
                                       padding="15")
        document_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # رقم المستند
        ttk.Label(document_frame, text="رقم المستند:", font=('Arial', 10, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=5)
        document_entry = ttk.Entry(document_frame, textvariable=self.document_num_var,
                                  font=('Arial', 12), width=30)
        document_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        # إجمالي المبلغ (للعرض فقط)
        ttk.Label(document_frame, text="إجمالي المبلغ:", font=('Arial', 10, 'bold')).grid(
            row=1, column=0, sticky=tk.W, pady=5)
        total_entry = ttk.Entry(document_frame, textvariable=self.total_amount_var,
                               font=('Arial', 12, 'bold'), width=30, state='readonly')
        total_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        # تكوين العمود
        document_frame.columnconfigure(1, weight=1)

        # إطار توزيع المبالغ
        distribution_frame = ttk.LabelFrame(main_frame,
                                           text="📊 توزيع المبالغ على الحسابات",
                                           padding="15")
        distribution_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))

        # إطار للجدول مع شريط تمرير
        table_container = ttk.Frame(distribution_frame)
        table_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # إنشاء Canvas وScrollbar للجدول
        self.canvas = tk.Canvas(table_container, height=200, bg='white')
        scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        table_container.columnconfigure(0, weight=1)
        table_container.rowconfigure(0, weight=1)

        # عناوين الجدول
        headers_frame = ttk.Frame(self.scrollable_frame)
        headers_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(headers_frame, text="اسم الحساب", font=('Arial', 10, 'bold')).grid(
            row=0, column=0, padx=5, sticky=tk.W)
        ttk.Label(headers_frame, text="المبلغ (0.000)", font=('Arial', 10, 'bold')).grid(
            row=0, column=1, padx=5, sticky=tk.W)
        ttk.Label(headers_frame, text="حذف", font=('Arial', 10, 'bold')).grid(
            row=0, column=2, padx=5, sticky=tk.W)

        # إطار للصفوف
        self.rows_frame = ttk.Frame(self.scrollable_frame)
        self.rows_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # زر إضافة حساب جديد
        add_account_btn = ttk.Button(distribution_frame,
                                    text="➕ إضافة حساب جديد",
                                    command=self.add_account_row)
        add_account_btn.grid(row=1, column=0, pady=(10, 0), sticky=tk.W)

        # تكوين العمود
        distribution_frame.columnconfigure(0, weight=1)
        distribution_frame.rowconfigure(0, weight=1)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # زر حفظ المستند
        save_btn = ttk.Button(buttons_frame,
                             text="💾 حفظ المستند",
                             command=self.save_document,
                             style='Accent.TButton')
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر حساب الإجمالي
        calculate_btn = ttk.Button(buttons_frame,
                                  text="🧮 حساب الإجمالي",
                                  command=self.calculate_total)
        calculate_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث الحسابات
        refresh_accounts_btn = ttk.Button(buttons_frame,
                                         text="🔄 تحديث الحسابات",
                                         command=self.manual_refresh_accounts)
        refresh_accounts_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر مسح الكل
        clear_btn = ttk.Button(buttons_frame,
                              text="🗑️ مسح الكل",
                              command=self.clear_all)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر المساعدة
        help_btn = ttk.Button(buttons_frame,
                             text="❓ مساعدة",
                             command=self.show_help)
        help_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # زر الإغلاق
        close_btn = ttk.Button(buttons_frame,
                              text="❌ إغلاق",
                              command=self.on_closing)
        close_btn.pack(side=tk.RIGHT)

        # إطار رسالة الحالة
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # رسالة الحالة
        self.status_label = ttk.Label(status_frame,
                                     textvariable=self.status_message_var,
                                     font=('Arial', 10),
                                     foreground='green')
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))

        # إضافة صف أولي (بعد تحميل الحسابات)
        # سيتم إضافته في دالة load_accounts_list

        # إعداد التنقل بزر الإدخال
        self.setup_enter_navigation(document_entry)

        # تركيز على رقم المستند
        document_entry.focus()

    def setup_enter_navigation(self, document_entry):
        """إعداد التنقل بزر الإدخال في حقل رقم المستند"""
        def on_document_enter(event):
            # الانتقال إلى أول حقل مبلغ في الجدول
            if self.distribution_rows:
                self.distribution_rows[0]['amount_entry'].focus()
            return "break"

        document_entry.bind('<Return>', on_document_enter)

        # إعداد التنقل في جدول التوزيع
        self.setup_table_navigation()

    def setup_table_navigation(self):
        """إعداد التنقل في جدول التوزيع"""
        def on_amount_enter(event, row_index):
            # الانتقال إلى الصف التالي أو إضافة صف جديد
            if row_index < len(self.distribution_rows) - 1:
                self.distribution_rows[row_index + 1]['amount_entry'].focus()
            else:
                # إضافة صف جديد والانتقال إليه
                self.add_account_row()
                if self.distribution_rows:
                    self.distribution_rows[-1]['amount_entry'].focus()
            return "break"

        # تطبيق التنقل على جميع الصفوف الموجودة
        for i, row_data in enumerate(self.distribution_rows):
            row_data['amount_entry'].bind('<Return>', lambda event, idx=i: on_amount_enter(event, idx))

    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي لقائمة الحسابات"""
        # تحديث قائمة الحسابات كل 10 ثواني (أسرع)
        def refresh_accounts():
            old_accounts = self.available_accounts.copy()
            self.load_accounts_list_silent()  # تحميل بصمت

            # تحديث قوائم الحسابات في جميع الصفوف
            if old_accounts != self.available_accounts:
                self.update_all_account_combos()
                print(f"🔄 تم تحديث تلقائي: {len(self.available_accounts)} حساب")

            # جدولة التحديث التالي
            self.after(10000, refresh_accounts)  # 10 ثواني

        # بدء التحديث التلقائي بعد 5 ثواني
        self.after(5000, refresh_accounts)

    def update_all_account_combos(self):
        """تحديث جميع قوائم الحسابات في الجدول"""
        for row_data in self.distribution_rows:
            current_value = row_data['account_var'].get()
            row_data['account_combo']['values'] = self.available_accounts

            # الاحتفاظ بالقيمة الحالية إذا كانت مازالت موجودة
            if current_value in self.available_accounts:
                row_data['account_var'].set(current_value)
            else:
                row_data['account_var'].set("")

    def refresh_single_combo(self, combo):
        """تحديث قائمة منسدلة واحدة"""
        try:
            # تحديث فوري لقائمة الحسابات
            old_accounts = list(combo['values']) if combo['values'] else []

            # إعادة تحميل قائمة الحسابات
            self.load_accounts_list_silent()

            # تحديث القائمة
            combo['values'] = self.available_accounts

            # إظهار رسالة إذا تغيرت القائمة
            if old_accounts != self.available_accounts:
                print(f"🔄 تم تحديث قائمة الحسابات: {len(self.available_accounts)} حساب")

        except Exception as e:
            print(f"❌ خطأ في تحديث القائمة: {str(e)}")

    def load_accounts_list_silent(self):
        """تحميل قائمة الحسابات بصمت (بدون رسائل)"""
        try:
            deductions_file = "Accounting system deductions.xlsx"
            if not os.path.exists(deductions_file):
                self.available_accounts = ["لا يوجد ملف"]
                return

            workbook = openpyxl.load_workbook(deductions_file)
            accounts = []

            for sheet_name in workbook.sheetnames:
                if sheet_name != "مرحباً":  # تجاهل ورقة الترحيب
                    accounts.append(sheet_name)

            workbook.close()

            if accounts:
                self.available_accounts = sorted(accounts)
            else:
                self.available_accounts = ["لا توجد حسابات"]

        except Exception as e:
            self.available_accounts = ["خطأ في التحميل"]

    def manual_refresh_accounts(self):
        """تحديث يدوي لقائمة الحسابات"""
        try:
            old_count = len(self.available_accounts)
            self.load_accounts_list_silent()
            self.update_all_account_combos()

            new_count = len(self.available_accounts)
            messagebox.showinfo("تحديث الحسابات",
                               f"تم تحديث قائمة الحسابات\n\n"
                               f"عدد الحسابات: {new_count}\n"
                               f"التغيير: {new_count - old_count if old_count > 0 else 0}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث الحسابات:\n{str(e)}")

    def load_accounts_list(self):
        """تحميل قائمة الحسابات من ملف المقبوضات"""
        try:
            deductions_file = "Accounting system deductions.xlsx"
            if not os.path.exists(deductions_file):
                messagebox.showwarning("تنبيه", f"ملف المقبوضات غير موجود: {deductions_file}")
                self.available_accounts = ["لا توجد حسابات"]
                return

            workbook = openpyxl.load_workbook(deductions_file)
            accounts = []

            for sheet_name in workbook.sheetnames:
                if sheet_name != "مرحباً":  # تجاهل ورقة الترحيب
                    accounts.append(sheet_name)

            workbook.close()

            if accounts:
                self.available_accounts = sorted(accounts)
                print(f"✅ تم تحميل {len(accounts)} حساب من ملف المقبوضات")
            else:
                self.available_accounts = ["لا توجد حسابات"]
                messagebox.showinfo("معلومات", "لا توجد حسابات في ملف المقبوضات")

                # إضافة الصف الأولي بعد تحميل الحسابات
            if not self.distribution_rows:  # إذا لم يكن هناك صفوف
                self.add_account_row(default_account="الطوابع")

        except Exception as e:
            print(f"❌ خطأ في تحميل قائمة الحسابات: {str(e)}")
            self.available_accounts = ["خطأ في التحميل"]
            messagebox.showerror("خطأ", f"فشل في تحميل قائمة الحسابات:\n{str(e)}")

    def add_account_row(self, default_account=None):
        """إضافة صف جديد لتوزيع المبلغ"""
        row_num = len(self.distribution_rows)

        # إطار للصف
        row_frame = ttk.Frame(self.rows_frame)
        row_frame.grid(row=row_num, column=0, sticky=(tk.W, tk.E), pady=2)

        # قائمة منسدلة للحسابات (محدثة تلقائياً)
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(row_frame, textvariable=account_var,
                                    values=self.available_accounts,
                                    width=30, state="readonly")
        account_combo.grid(row=0, column=0, padx=5, sticky=tk.W)

        # تحديث قائمة الحسابات فور إضافة الصف
        self.refresh_single_combo(account_combo)

        # تعيين الحساب الافتراضي إذا تم تحديده
        if default_account and default_account in self.available_accounts:
            account_var.set(default_account)
        elif default_account == "الطوابع":
            # البحث عن حساب يحتوي على "طوابع"
            for account in self.available_accounts:
                if "طوابع" in account or "طابع" in account:
                    account_var.set(account)
                    break

        # حقل المبلغ
        amount_var = tk.StringVar(value="0.000")
        amount_entry = ttk.Entry(row_frame, textvariable=amount_var,
                                width=15, justify='center')
        amount_entry.grid(row=0, column=1, padx=5, sticky=tk.W)

        # ربط تحديث الإجمالي عند تغيير المبلغ
        amount_var.trace('w', lambda *args: self.calculate_total())

        # إعداد التنقل بزر الإدخال للصف الجديد
        def on_amount_enter(event):
            if row_num < len(self.distribution_rows) - 1:
                self.distribution_rows[row_num + 1]['amount_entry'].focus()
            else:
                self.add_account_row()
                if self.distribution_rows:
                    self.distribution_rows[-1]['amount_entry'].focus()
            return "break"

        amount_entry.bind('<Return>', on_amount_enter)

        # زر حذف الصف
        delete_btn = ttk.Button(row_frame, text="❌", width=3,
                               command=lambda: self.delete_account_row(row_num))
        delete_btn.grid(row=0, column=2, padx=5, sticky=tk.W)

        # حفظ معلومات الصف
        row_data = {
            'frame': row_frame,
            'account_var': account_var,
            'amount_var': amount_var,
            'account_combo': account_combo,
            'amount_entry': amount_entry,
            'delete_btn': delete_btn
        }

        self.distribution_rows.append(row_data)

        # تحديث منطقة التمرير
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def delete_account_row(self, row_index):
        """حذف صف توزيع المبلغ"""
        if len(self.distribution_rows) <= 1:
            messagebox.showwarning("تنبيه", "يجب الاحتفاظ بصف واحد على الأقل")
            return

        # حذف الصف من الواجهة
        self.distribution_rows[row_index]['frame'].destroy()

        # حذف الصف من القائمة
        del self.distribution_rows[row_index]

        # إعادة ترقيم الصفوف
        for i, row_data in enumerate(self.distribution_rows):
            row_data['frame'].grid(row=i, column=0, sticky=(tk.W, tk.E), pady=2)
            # تحديث دالة الحذف
            row_data['delete_btn'].configure(command=lambda idx=i: self.delete_account_row(idx))

        # تحديث الإجمالي
        self.calculate_total()

        # تحديث منطقة التمرير
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def calculate_total(self):
        """حساب إجمالي المبالغ"""
        total = 0.0
        for row_data in self.distribution_rows:
            try:
                amount_str = row_data['amount_var'].get().strip()
                if amount_str:
                    amount = float(amount_str)
                    total += amount
            except ValueError:
                continue

        self.total_amount_var.set(f"{total:.3f}")

    def clear_all(self):
        """مسح جميع البيانات (بدون رسالة تأكيد)"""
        # مسح رقم المستند
        self.document_num_var.set("")

        # مسح جميع الصفوف ما عدا الأول
        while len(self.distribution_rows) > 1:
            self.delete_account_row(-1)

        # إعادة تعيين الصف الأول إلى الطوابع
        if self.distribution_rows:
            # البحث عن حساب الطوابع
            stamps_account = None
            for account in self.available_accounts:
                if "طوابع" in account or "طابع" in account:
                    stamps_account = account
                    break

            self.distribution_rows[0]['account_var'].set(stamps_account or "")
            self.distribution_rows[0]['amount_var'].set("0.000")

        # تحديث الإجمالي
        self.calculate_total()

        # تحديث رسالة الحالة
        self.update_status_message("تم مسح البيانات", 'blue')

    def save_document(self):
        """حفظ المستند وتوزيعه على الحسابات"""
        try:
            # التحقق من البيانات
            document_num = self.document_num_var.get().strip()
            if not document_num:
                messagebox.showerror("خطأ", "يجب إدخال رقم المستند")
                return

            # جمع بيانات التوزيع
            distribution_data = []
            for row_data in self.distribution_rows:
                account = row_data['account_var'].get().strip()
                amount_str = row_data['amount_var'].get().strip()

                if account and account != "لا توجد حسابات" and account != "خطأ في التحميل":
                    try:
                        amount = float(amount_str)
                        if amount > 0:
                            distribution_data.append({
                                'account': account,
                                'amount': amount
                            })
                    except ValueError:
                        messagebox.showerror("خطأ", f"المبلغ غير صحيح في الحساب: {account}")
                        return

            if not distribution_data:
                messagebox.showerror("خطأ", "يجب إضافة حساب واحد على الأقل مع مبلغ أكبر من صفر")
                return

            # فحص تكرار رقم المستند في جميع الحسابات
            duplicate_locations = self.check_document_duplicates(document_num, distribution_data)

            if duplicate_locations:
                duplicate_msg = "تحذير: رقم المستند موجود بالفعل في:\n\n"
                for location in duplicate_locations:
                    duplicate_msg += f"• {location}\n"
                duplicate_msg += "\nهل تريد المتابعة رغم التكرار؟"

                result = messagebox.askyesno("تكرار رقم المستند", duplicate_msg)
                if not result:
                    return

            # توزيع المستند على الحسابات
            success_results = []
            failed_results = []

            for item in distribution_data:
                result = self.add_to_account(item['account'], document_num, item['amount'])
                if result:
                    success_results.append(result)
                else:
                    failed_results.append(item['account'])

            # عرض نتائج في رسالة الحالة (بدون رسائل منبثقة)
            if success_results and not failed_results:
                # نجاح كامل
                status_msg = f"✅ تم حفظ المستند {document_num} بنجاح على {len(success_results)} حساب"
                self.update_status_message(status_msg, 'green')
                self.clear_data_keep_accounts()
            elif success_results and failed_results:
                # نجاح جزئي
                status_msg = f"⚠️ حفظ جزئي: {len(success_results)} نجح، {len(failed_results)} فشل"
                self.update_status_message(status_msg, 'orange')
            else:
                # فشل كامل
                status_msg = f"❌ فشل في حفظ المستند {document_num}"
                self.update_status_message(status_msg, 'red')

        except Exception as e:
            self.update_status_message(f"❌ خطأ في حفظ المستند: {str(e)}", 'red')

    def update_status_message(self, message, color='green'):
        """تحديث رسالة الحالة"""
        self.status_message_var.set(message)
        self.status_label.config(foreground=color)

        # إعادة تعيين الرسالة بعد 5 ثواني
        if color == 'green':
            self.after(5000, lambda: self.status_message_var.set("جاهز لإضافة مستند جديد"))
            self.after(5000, lambda: self.status_label.config(foreground='green'))

    def clear_data_keep_accounts(self):
        """مسح البيانات مع الاحتفاظ بالحسابات المحددة"""
        # مسح رقم المستند
        self.document_num_var.set("")

        # مسح المبالغ فقط مع الاحتفاظ بالحسابات
        for row_data in self.distribution_rows:
            row_data['amount_var'].set("0.000")
            # الاحتفاظ بالحساب المحدد

        # تحديث الإجمالي
        self.calculate_total()

    def check_document_duplicates(self, document_num, distribution_data):
        """فحص تكرار رقم المستند في جميع الحسابات"""
        duplicate_locations = []

        try:
            deductions_file = "Accounting system deductions.xlsx"
            if not os.path.exists(deductions_file):
                return duplicate_locations

            workbook = openpyxl.load_workbook(deductions_file)

            # فحص جميع الحسابات في قائمة التوزيع
            for item in distribution_data:
                account_name = item['account']

                if account_name in workbook.sheetnames:
                    ws = workbook[account_name]
                    locations = self.find_document_in_sheet(ws, document_num)

                    for location in locations:
                        duplicate_locations.append(f"{account_name} - {location}")

            workbook.close()

        except Exception as e:
            print(f"❌ خطأ في فحص التكرار: {str(e)}")

        return duplicate_locations

    def find_document_in_sheet(self, ws, document_num):
        """البحث عن رقم مستند في ورقة معينة"""
        locations = []
        doc_columns = ['B', 'D', 'F', 'H', 'J', 'L', 'N']
        section_names = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس', 'السابع']

        try:
            table_num = 1
            row = 13  # بداية البحث

            while row < 1000:
                if self.is_table_at_row(ws, row):
                    # البحث في هذا الجدول
                    for section_idx, doc_col in enumerate(doc_columns):
                        for data_row in range(row + 1, row + 22):  # 21 صف لكل قسم
                            cell_value = ws[f'{doc_col}{data_row}'].value

                            if (cell_value and
                                str(cell_value).strip() == str(document_num).strip()):
                                locations.append(f"الجدول {table_num} - القسم {section_names[section_idx]} - {doc_col}{data_row}")

                    table_num += 1
                    row += 40
                else:
                    row += 1

        except Exception as e:
            print(f"❌ خطأ في البحث: {str(e)}")

        return locations

    def add_to_account(self, account_name, document_num, amount):
        """إضافة المستند إلى حساب معين مع إرجاع معلومات مفصلة"""
        try:
            deductions_file = "Accounting system deductions.xlsx"
            workbook = openpyxl.load_workbook(deductions_file)

            if account_name not in workbook.sheetnames:
                print(f"❌ الحساب غير موجود: {account_name}")
                return None

            ws = workbook[account_name]

            # البحث عن أول خلية فارغة في الأقسام بالترتيب
            empty_cell = self.find_next_empty_cell(ws)

            if not empty_cell:
                # إنشاء جدول جديد
                if self.create_new_table(ws, account_name):
                    empty_cell = self.find_next_empty_cell(ws)
                else:
                    print(f"❌ فشل في إنشاء جدول جديد للحساب: {account_name}")
                    return None

            if empty_cell:
                amount_col, doc_col, row = empty_cell

                # تحديد القسم والجدول
                section_info = self.get_section_info(amount_col, row)

                # إضافة البيانات
                ws[f'{amount_col}{row}'] = amount
                ws[f'{amount_col}{row}'].number_format = '0.000'
                ws[f'{doc_col}{row}'] = document_num

                # حفظ الملف
                workbook.save(deductions_file)

                # إرجاع معلومات مفصلة
                result = {
                    'account': account_name,
                    'amount': amount,
                    'document_num': document_num,
                    'position': f'{amount_col}{row}',
                    'section': section_info['section'],
                    'table': section_info['table']
                }

                print(f"✅ تم إضافة المستند {document_num} بمبلغ {amount} إلى {account_name} - {section_info['section']} - {amount_col}{row}")
                return result
            else:
                print(f"❌ لم يتم العثور على خلية فارغة في الحساب: {account_name}")
                return None

        except Exception as e:
            print(f"❌ خطأ في إضافة المستند إلى الحساب {account_name}: {str(e)}")
            return None
        finally:
            try:
                workbook.close()
            except:
                pass

    def get_section_info(self, amount_col, row):
        """تحديد معلومات القسم والجدول"""
        # تحديد القسم حسب العمود
        section_map = {
            'A': 'الأول', 'C': 'الثاني', 'E': 'الثالث', 'G': 'الرابع',
            'I': 'الخامس', 'K': 'السادس', 'M': 'السابع'
        }

        section = section_map.get(amount_col, 'غير محدد')

        # تقدير رقم الجدول (بناءً على رقم الصف)
        if row >= 13:
            table_num = ((row - 13) // 40) + 1
        else:
            table_num = 1

        return {
            'section': section,
            'table': table_num
        }

    def find_next_empty_cell(self, ws):
        """البحث عن أول خلية فارغة في الأقسام بالترتيب"""
        # الأعمدة بالترتيب: A-B, C-D, E-F, G-H, I-J, K-L, M-N
        columns_pairs = [
            ('A', 'B'), ('C', 'D'), ('E', 'F'), ('G', 'H'),
            ('I', 'J'), ('K', 'L'), ('M', 'N')
        ]

        # البحث في الجداول الموجودة
        table_start_row = 13  # بداية البيانات في الجدول الأول

        while table_start_row < 1000:  # حد أقصى للبحث
            # فحص وجود جدول في هذا الموقع
            if self.is_table_at_row(ws, table_start_row):
                # البحث في الأقسام بالترتيب
                for amount_col, doc_col in columns_pairs:
                    for row in range(table_start_row, table_start_row + 21):  # 21 صف لكل قسم
                        if not ws[f'{amount_col}{row}'].value:
                            return (amount_col, doc_col, row)

                # الانتقال إلى الجدول التالي
                table_start_row += 40  # تقدير المسافة بين الجداول
            else:
                break

        return None

    def is_table_at_row(self, ws, row):
        """فحص وجود جدول في الصف المحدد"""
        try:
            # فحص وجود عناوين الأعمدة
            if (ws[f'A{row-1}'].value == 'المبلغ' or
                ws[f'A{row-2}'].value == 'المبلغ'):
                return True
            return False
        except:
            return False

    def create_new_table(self, ws, account_name):
        """إنشاء جدول جديد في الورقة"""
        try:
            # العثور على آخر صف مستخدم
            last_row = ws.max_row

            # موقع الجدول الجديد
            new_table_start = last_row + 5

            # نسخ تنسيق الجدول (مبسط)
            # هذا يحتاج تطوير أكثر تفصيلاً
            print(f"🔄 إنشاء جدول جديد للحساب {account_name} في الصف {new_table_start}")

            # إضافة عناوين بسيطة للجدول الجديد
            columns_pairs = [
                ('A', 'B'), ('C', 'D'), ('E', 'F'), ('G', 'H'),
                ('I', 'J'), ('K', 'L'), ('M', 'N')
            ]

            # إضافة عناوين الأعمدة
            for amount_col, doc_col in columns_pairs:
                ws[f'{amount_col}{new_table_start-1}'] = 'المبلغ'
                ws[f'{doc_col}{new_table_start-1}'] = 'رقم المستند'

            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول جديد: {str(e)}")
            return False

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = (
            "📚 مساعدة - نافذة إضافة مستند مقبوضات\n\n"
            "🔹 أدخل رقم المستند في الحقل العلوي\n"
            "🔹 اختر الحسابات من القائمة المنسدلة\n"
            "🔹 أدخل المبلغ لكل حساب (بثلاث خانات عشرية)\n"
            "🔹 استخدم 'إضافة حساب جديد' لإضافة المزيد\n"
            "🔹 سيتم توزيع المستند على جميع الحسابات المحددة\n"
            "🔹 يتم الترحيل تلقائياً في الأقسام بالترتيب\n\n"
            "💡 ملاحظة: يجب أن يكون ملف المقبوضات مغلقاً قبل الحفظ"
        )
        messagebox.showinfo("مساعدة", help_text)

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """معالج إغلاق النافذة"""
        self.destroy()
