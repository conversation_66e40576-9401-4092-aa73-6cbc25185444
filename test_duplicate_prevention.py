#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار آلية منع تكرار المستندات المحدثة
"""

import sys
import os
from tkinter import messagebox

def test_duplicate_prevention():
    """اختبار شامل لآلية منع تكرار المستندات"""
    try:
        print("\n" + "=" * 80)
        print("🧪 اختبار آلية منع تكرار المستندات المحدثة")
        print("=" * 80)
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي للاختبار
        test_account = "TEST001-حساب اختبار منع التكرار"
        
        print(f"\n📋 إنشاء حساب تجريبي: {test_account}")
        if test_account not in excel.workbook.sheetnames:
            success = excel.create_account("TEST001", "حساب اختبار منع التكرار")
            if not success:
                print("❌ فشل في إنشاء الحساب التجريبي")
                return False
        
        print("✅ تم إنشاء الحساب التجريبي بنجاح")
        
        # اختبار 1: إضافة مستند جديد (يجب أن ينجح)
        print(f"\n🧪 اختبار 1: إضافة مستند جديد")
        result1 = excel.add_document(test_account, 1000, "DOC001", "PAY001")
        if result1:
            print("✅ تم إضافة المستند الأول بنجاح")
        else:
            print("❌ فشل في إضافة المستند الأول")
            return False
        
        # اختبار 2: محاولة إضافة نفس المستند في نفس الحساب (يجب أن يفشل)
        print(f"\n🧪 اختبار 2: محاولة إضافة نفس المستند في نفس الحساب")
        result2 = excel.add_document(test_account, 2000, "DOC001", "PAY001")
        if not result2:
            print("✅ تم منع التكرار في نفس الحساب بنجاح")
        else:
            print("❌ لم يتم منع التكرار في نفس الحساب!")
            return False
        
        # إنشاء حساب ثاني للاختبار
        test_account2 = "TEST002-حساب اختبار منع التكرار 2"
        print(f"\n📋 إنشاء حساب تجريبي ثاني: {test_account2}")
        if test_account2 not in excel.workbook.sheetnames:
            success = excel.create_account("TEST002", "حساب اختبار منع التكرار 2")
            if not success:
                print("❌ فشل في إنشاء الحساب التجريبي الثاني")
                return False
        
        # اختبار 3: محاولة إضافة نفس المستند في حساب آخر (يجب أن يفشل)
        print(f"\n🧪 اختبار 3: محاولة إضافة نفس المستند في حساب آخر")
        result3 = excel.add_document(test_account2, 3000, "DOC001", "PAY001")
        if not result3:
            print("✅ تم منع التكرار بين الحسابات بنجاح")
        else:
            print("❌ لم يتم منع التكرار بين الحسابات!")
            return False
        
        # اختبار 4: إضافة مستند جديد مختلف في الحساب الثاني (يجب أن ينجح)
        print(f"\n🧪 اختبار 4: إضافة مستند جديد مختلف في الحساب الثاني")
        result4 = excel.add_document(test_account2, 1500, "DOC002", "PAY002")
        if result4:
            print("✅ تم إضافة المستند الجديد في الحساب الثاني بنجاح")
        else:
            print("❌ فشل في إضافة المستند الجديد في الحساب الثاني")
            return False
        
        # اختبار 5: محاولة إضافة مستند برقم مستند مختلف ولكن نفس رقم التأدية (يجب أن ينجح)
        print(f"\n🧪 اختبار 5: إضافة مستند برقم مستند مختلف ونفس رقم التأدية")
        result5 = excel.add_document(test_account, 2500, "DOC003", "PAY001")
        if result5:
            print("✅ تم إضافة المستند برقم مستند مختلف ونفس رقم التأدية بنجاح")
        else:
            print("❌ فشل في إضافة المستند برقم مستند مختلف ونفس رقم التأدية")
            return False
        
        # اختبار 6: محاولة إضافة مستند بنفس رقم المستند ورقم تأدية مختلف (يجب أن ينجح)
        print(f"\n🧪 اختبار 6: إضافة مستند بنفس رقم المستند ورقم تأدية مختلف")
        result6 = excel.add_document(test_account, 3500, "DOC001", "PAY003")
        if result6:
            print("✅ تم إضافة المستند بنفس رقم المستند ورقم تأدية مختلف بنجاح")
        else:
            print("❌ فشل في إضافة المستند بنفس رقم المستند ورقم تأدية مختلف")
            return False
        
        # حفظ الملف
        print(f"\n💾 حفظ الملف...")
        excel.save_file()
        
        print(f"\n🎉 تم اجتياز جميع اختبارات منع التكرار بنجاح!")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """اختبار الحالات الحدية"""
    try:
        print("\n" + "=" * 80)
        print("🧪 اختبار الحالات الحدية لمنع التكرار")
        print("=" * 80)
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        test_account = "EDGE001-حساب اختبار الحالات الحدية"
        
        # إنشاء حساب للاختبار
        if test_account not in excel.workbook.sheetnames:
            excel.create_account("EDGE001", "حساب اختبار الحالات الحدية")
        
        # اختبار 1: أرقام مستندات بمسافات
        print(f"\n🧪 اختبار 1: أرقام مستندات بمسافات")
        result1 = excel.add_document(test_account, 1000, " DOC001 ", " PAY001 ")
        if result1:
            print("✅ تم إضافة المستند بمسافات بنجاح")
            
            # محاولة إضافة نفس المستند بدون مسافات
            result1b = excel.add_document(test_account, 2000, "DOC001", "PAY001")
            if not result1b:
                print("✅ تم منع التكرار مع المسافات بنجاح")
            else:
                print("❌ لم يتم منع التكرار مع المسافات!")
                return False
        else:
            print("❌ فشل في إضافة المستند بمسافات")
            return False
        
        # اختبار 2: أرقام مستندات رقمية
        print(f"\n🧪 اختبار 2: أرقام مستندات رقمية")
        result2 = excel.add_document(test_account, 1500, 123, 456)
        if result2:
            print("✅ تم إضافة المستند بأرقام رقمية بنجاح")
            
            # محاولة إضافة نفس المستند كنص
            result2b = excel.add_document(test_account, 2500, "123", "456")
            if not result2b:
                print("✅ تم منع التكرار بين الأرقام والنصوص بنجاح")
            else:
                print("❌ لم يتم منع التكرار بين الأرقام والنصوص!")
                return False
        else:
            print("❌ فشل في إضافة المستند بأرقام رقمية")
            return False
        
        print(f"\n🎉 تم اجتياز جميع اختبارات الحالات الحدية بنجاح!")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحالات الحدية: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبارات آلية منع تكرار المستندات")
    
    # تشغيل الاختبارات الأساسية
    basic_test_result = test_duplicate_prevention()
    
    # تشغيل اختبارات الحالات الحدية
    edge_test_result = test_edge_cases()
    
    # النتيجة النهائية
    if basic_test_result and edge_test_result:
        print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("✅ آلية منع تكرار المستندات تعمل بشكل صحيح")
    else:
        print("\n❌ فشل في بعض الاختبارات")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
