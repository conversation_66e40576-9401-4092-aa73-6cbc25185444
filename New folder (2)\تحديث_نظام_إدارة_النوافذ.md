# تحديث نظام إدارة النوافذ - منع فتح النوافذ المكررة

## 📋 ملخص التحديث

تم تطبيق نظام شامل لإدارة النوافذ في التطبيق لمنع فتح أكثر من نافذة واحدة من نفس النوع، مع تحسين تجربة المستخدم وضمان استقرار التطبيق.

---

## 🎯 الأهداف المحققة

### ✅ منع فتح النوافذ المكررة
- **نافذة إدارة الحسابات**: لا يمكن فتح أكثر من نافذة واحدة
- **نافذة إضافة حساب**: نافذة واحدة فقط في كل مرة
- **نافذة إضافة مستند**: نافذة واحدة فقط في كل مرة
- **نافذة البحث**: نافذة واحدة فقط في كل مرة
- **نافذة إدارة المستخدمين**: نافذة واحدة فقط في كل مرة
- **نافذة تغيير كلمة المرور**: نافذة واحدة فقط في كل مرة

### ✅ تحسين تجربة المستخدم
- **تركيز تلقائي**: عند محاولة فتح نافذة مفتوحة بالفعل، يتم التركيز عليها
- **رسائل تنبيه**: إعلام المستخدم عند محاولة فتح نافذة مكررة
- **إدارة النوافذ الفرعية**: إغلاق تلقائي للنوافذ الفرعية عند إغلاق النافذة الرئيسية

---

## 🔧 التحديثات المطبقة

### 1. **ملف `app.py` - التطبيق الرئيسي**

#### إضافة نظام إدارة النوافذ:
```python
# نظام إدارة النوافذ المفتوحة
self.open_windows = {
    'manage_accounts': None,
    'add_account': None,
    'add_document': None,
    'search': None,
    'user_management': None,
    'change_password': None,
    'account_details': [],  # قائمة لأنه يمكن فتح عدة نوافذ تفاصيل
    'reports': None
}
```

#### دوال إدارة النوافذ الجديدة:
- `is_window_open(window_type)`: فحص إذا كانت نافذة معينة مفتوحة
- `register_window(window_type, window)`: تسجيل نافذة جديدة
- `close_window(window_type, window)`: إغلاق نافذة وإزالتها من القائمة
- `focus_existing_window(window_type)`: تركيز على نافذة موجودة
- `on_window_close(window_type, window)`: معالج حدث إغلاق النافذة

#### تحديث دوال فتح النوافذ:
```python
def manage_accounts(self):
    """إدارة الحسابات المحسنة"""
    if self.check_permission('edit_account'):
        # فحص إذا كانت النافذة مفتوحة بالفعل
        if self.is_window_open('manage_accounts'):
            if self.focus_existing_window('manage_accounts'):
                messagebox.showinfo("تنبيه", "نافذة إدارة الحسابات مفتوحة بالفعل")
                return
        
        try:
            # فتح نافذة جديدة
            window = ManageAccountsDialog(self.root, self.excel)
            self.register_window('manage_accounts', window)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الحسابات: {str(e)}")
```

### 2. **ملف `manage_accounts.py` - نافذة إدارة الحسابات**

#### إضافة نظام إدارة النوافذ الفرعية:
```python
# نظام إدارة النوافذ الفرعية
self.child_windows = {
    'account_details': [],
    'print_preview': None,
    'export_options': None,
    'edit_account': None
}
```

#### تحسينات النافذة:
- **حجم أكبر**: `900x600` بدلاً من `800x500`
- **قابلية التمدد**: `resizable(True, True)`
- **توسيط تلقائي**: `center_window()`
- **إغلاق آمن**: `on_closing()` مع إغلاق النوافذ الفرعية

#### منع فتح نوافذ تفاصيل مكررة:
```python
def view_enhanced_account_details(self):
    # فحص إذا كانت نافذة تفاصيل لنفس الحساب مفتوحة بالفعل
    for window in self.child_windows['account_details']:
        if (hasattr(window, 'sheet_name') and window.sheet_name == sheet_name and 
            window.winfo_exists()):
            window.lift()
            window.focus_force()
            messagebox.showinfo("تنبيه", f"نافذة تفاصيل الحساب '{account_name}' مفتوحة بالفعل")
            return
```

---

## 🚀 الميزات الجديدة

### 1. **منع التكرار الذكي**
- فحص تلقائي للنوافذ المفتوحة قبل فتح نافذة جديدة
- تركيز على النافذة الموجودة بدلاً من فتح نافذة جديدة

### 2. **إدارة النوافذ الفرعية**
- تتبع جميع النوافذ الفرعية المفتوحة
- إغلاق تلقائي للنوافذ الفرعية عند إغلاق النافذة الرئيسية

### 3. **تحسين الأداء**
- تقليل استهلاك الذاكرة بمنع فتح نوافذ غير ضرورية
- تحسين استجابة التطبيق

### 4. **تجربة مستخدم محسنة**
- رسائل تنبيه واضحة ومفيدة
- تركيز تلقائي على النوافذ المفتوحة
- واجهة أكثر تنظيماً

---

## 📝 دليل الاستخدام

### للمستخدم العادي:
1. **فتح النوافذ**: استخدم القوائم أو الأزرار كالمعتاد
2. **النوافذ المكررة**: إذا حاولت فتح نافذة مفتوحة بالفعل، ستظهر رسالة تنبيه وسيتم التركيز على النافذة الموجودة
3. **إغلاق النوافذ**: أغلق النوافذ كالمعتاد، وستُغلق النوافذ الفرعية تلقائياً

### للمطور:
1. **إضافة نافذة جديدة**: أضف نوع النافذة إلى `self.open_windows`
2. **تسجيل النافذة**: استخدم `self.register_window(window_type, window)`
3. **فحص النافذة**: استخدم `self.is_window_open(window_type)`

---

## 🧪 الاختبار

### ملف الاختبار: `test_window_management.py`
```bash
python test_window_management.py
```

### اختبارات يدوية:
1. **اختبار منع التكرار**:
   - افتح نافذة إدارة الحسابات
   - حاول فتحها مرة أخرى
   - تأكد من ظهور رسالة التنبيه والتركيز على النافذة الموجودة

2. **اختبار النوافذ الفرعية**:
   - افتح نافذة إدارة الحسابات
   - افتح نافذة تفاصيل حساب
   - أغلق نافذة إدارة الحسابات
   - تأكد من إغلاق نافذة التفاصيل تلقائياً

3. **اختبار جميع النوافذ**:
   - اختبر كل نافذة في التطبيق
   - تأكد من عمل نظام منع التكرار

---

## 🔍 المشاكل المحلولة

### ✅ مشاكل تم حلها:
1. **فتح نوافذ متعددة**: لا يمكن الآن فتح أكثر من نافذة واحدة من نفس النوع
2. **استهلاك الذاكرة**: تقليل استهلاك الذاكرة بمنع النوافذ المكررة
3. **تشويش الواجهة**: واجهة أكثر تنظيماً ووضوحاً
4. **النوافذ المعلقة**: إغلاق تلقائي للنوافذ الفرعية

### ⚠️ ملاحظات مهمة:
- **نوافذ تفاصيل الحساب**: يُسمح بفتح عدة نوافذ لحسابات مختلفة، لكن نافذة واحدة فقط لكل حساب
- **الحفظ التلقائي**: يتم حفظ البيانات تلقائياً عند إغلاق النوافذ
- **التوافق**: النظام متوافق مع جميع الميزات الموجودة

---

## 📊 إحصائيات التحديث

- **الملفات المحدثة**: 2 ملف رئيسي
- **الدوال المضافة**: 6 دوال جديدة
- **الدوال المحدثة**: 8 دوال محدثة
- **الميزات الجديدة**: 4 ميزات رئيسية
- **المشاكل المحلولة**: 4 مشاكل رئيسية

---

## 🎉 الخلاصة

تم تطبيق نظام شامل ومتقدم لإدارة النوافذ في التطبيق، مما يضمن:
- **عدم فتح نوافذ مكررة**
- **تجربة مستخدم محسنة**
- **أداء أفضل للتطبيق**
- **استقرار أكبر**

النظام جاهز للاستخدام ويعمل بشكل تلقائي دون الحاجة لتدخل من المستخدم.
