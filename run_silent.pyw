#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستندات المحاسبية
وزارة الصحة - التأمين الصحي

ملف التشغيل الرئيسي (بدون نافذة CMD)
انقر مزدوج على هذا الملف لتشغيل النظام

بيانات تسجيل الدخول:
اسم المستخدم: admin
كلمة المرور: admin
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """تشغيل النظام بدون نافذة CMD"""
    try:
        # إخفاء نافذة CMD إذا كانت موجودة
        try:
            import ctypes
            ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
        except:
            pass

        # التأكد من وجود الملفات المطلوبة
        required_files = ['app.py', 'user_manager.py', 'excel_manager.py']
        missing_files = []

        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)

        if missing_files:
            messagebox.showerror("ملفات مفقودة",
                                f"ملفات مفقودة: {', '.join(missing_files)}\n\nتأكد من وجود جميع ملفات النظام")
            return

        # تنظيف الملفات التالفة
        files_to_clean = ["users.json", "accounting_system.xlsx"]
        for file in files_to_clean:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except:
                    pass

        # استيراد وتشغيل النظام
        from app import AccountingApp

        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")

        # تعيين أيقونة النظام (اختياري)
        try:
            root.iconbitmap(default="icon.ico")
        except:
            pass

        # إنشاء وتشغيل التطبيق
        app = AccountingApp(root)

    except ImportError as e:
        messagebox.showerror("خطأ في المكتبات",
                            f"خطأ في استيراد المكتبات:\n{str(e)}\n\nلحل هذه المشكلة:\n1. افتح CMD أو PowerShell\n2. اكتب: pip install openpyxl\n3. أعد تشغيل البرنامج")

    except Exception as e:
        messagebox.showerror("خطأ في النظام",
                            f"خطأ في تشغيل النظام:\n{str(e)}\n\nإذا استمرت المشكلة، تواصل مع الدعم الفني")

if __name__ == "__main__":
    main()
