@echo off
chcp 65001 >nul
title إنشاء حزمة التوزيع v1.1.0 - نظام إدارة المستندات المحاسبية

echo.
echo ========================================================
echo 📦 إنشاء حزمة التوزيع النهائية v1.1.0
echo    نظام إدارة المستندات المحاسبية مع وظيفة الطباعة
echo ========================================================
echo.

REM إنشاء مجلد التوزيع النهائي
set DIST_FOLDER=نظام_المحاسبة_الأردني_v1.1.0_مع_الطباعة
if exist "%DIST_FOLDER%" rmdir /s /q "%DIST_FOLDER%"
mkdir "%DIST_FOLDER%"

echo 📁 إنشاء مجلد التوزيع: %DIST_FOLDER%

REM نسخ الملف التنفيذي
if exist "dist\نظام_إدارة_المستندات_المحاسبية.exe" (
    copy "dist\نظام_إدارة_المستندات_المحاسبية.exe" "%DIST_FOLDER%\"
    echo ✅ تم نسخ الملف التنفيذي v1.1.0
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    pause
    exit /b 1
)

REM نسخ ملفات التوثيق المحدثة
copy "dist\README.txt" "%DIST_FOLDER%\"
copy "dist\تعليمات_التثبيت_والطباعة.txt" "%DIST_FOLDER%\"
copy "dist\معلومات_الإصدار_v1.1.0.txt" "%DIST_FOLDER%\"
copy "dist\تشغيل_النظام_v1.1.0.bat" "%DIST_FOLDER%\"
echo ✅ تم نسخ ملفات التوثيق المحدثة

REM إنشاء ملف معلومات سريع محدث
echo 🏥 نظام إدارة المستندات المحاسبية v1.1.0 > "%DIST_FOLDER%\ابدأ_هنا.txt"
echo وزارة الصحة - المملكة الأردنية الهاشمية >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo. >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 🆕 الجديد في الإصدار 1.1.0: >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 🖨️ وظيفة الطباعة الشاملة للحسابات >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 💾 حفظ التقارير بصيغ متعددة >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 📋 معاينة الطباعة المحسنة >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo. >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 🚀 للتشغيل السريع: >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo انقر مرتين على "تشغيل_النظام_v1.1.0.bat" >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo أو انقر مرتين على "نظام_إدارة_المستندات_المحاسبية.exe" >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo. >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 🖨️ لاستخدام وظيفة الطباعة: >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 1. اذهب إلى "إدارة الحسابات" >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 2. حدد الحساب المطلوب >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 3. انقر "🖨️ طباعة الحساب" >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo. >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo 📖 للمزيد من المعلومات: >> "%DIST_FOLDER%\ابدأ_هنا.txt"
echo اقرأ ملف "README.txt" >> "%DIST_FOLDER%\ابدأ_هنا.txt"

echo ✅ تم إنشاء ملف البداية السريعة المحدث

REM عرض معلومات الحزمة
echo.
echo ========================================================
echo ✅ تم إنشاء حزمة التوزيع v1.1.0 بنجاح!
echo ========================================================
echo.
echo 📁 مجلد التوزيع: %DIST_FOLDER%
echo 📊 محتويات الحزمة:
dir "%DIST_FOLDER%" /b
echo.
echo 🆕 المميزات الجديدة في v1.1.0:
echo    🖨️ طباعة شاملة للحسابات مع معاينة
echo    💾 حفظ التقارير كملفات نصية أو Word
echo    📋 تنسيق احترافي للتقارير المطبوعة
echo.
echo 🎯 الحزمة جاهزة للتوزيع والنسخ إلى أجهزة أخرى
echo 💾 يمكنك ضغط المجلد إلى ملف ZIP لسهولة النقل
echo.
echo 📞 للدعم: قسم تقنية المعلومات - وزارة الصحة الأردنية
echo.

pause
