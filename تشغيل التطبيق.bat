@echo off
chcp 65001 >nul
title Accounting System - Ministry of Health

echo ========================================
echo    Accounting Documents Management System
echo    Ministry of Health - Jordan
echo ========================================
echo.

echo Starting system...
echo.

REM Try different Python commands in order of preference
set PYTHON_CMD=

REM Try py command first (recommended for Windows)
py -c "print('test')" >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found via py command
    set "PYTHON_CMD=py"
    goto :python_found
)

REM Try python command
python -c "print('test')" >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found in PATH
    set "PYTHON_CMD=python"
    goto :python_found
)

REM Check for specific Python path as last resort
set PYTHON_PATH=C:\Users\<USER>\Downloads\python-3.13.2-embed-amd64\python.exe
if exist "%PYTHON_PATH%" (
    echo Python found at: %PYTHON_PATH%
    echo WARNING: Using embedded Python - pip may not be available
    set "PYTHON_CMD=%PYTHON_PATH%"
    goto :python_found
)

REM Python not found
echo ERROR: Python not found
echo.
echo Please install Python 3.7 or newer
echo.
pause
exit /b 1

:python_found
echo.
echo Checking required libraries...

REM Check if offline installer exists
if exist "libraries\install_offline.py" (
    echo 📦 Using offline library installer...
    "%PYTHON_CMD%" libraries\install_offline.py
    if %errorlevel% equ 0 (
        echo ✅ Offline libraries installed successfully
        goto :libraries_ready
    ) else (
        echo ⚠️ Offline installer had issues - trying online install...
    )
)

REM Fallback to online installation
echo 🌐 Checking and installing libraries online...

REM Check and install openpyxl
"%PYTHON_CMD%" -c "import openpyxl" 2>nul
if %errorlevel% neq 0 (
    echo 📥 openpyxl not found, installing...
    "%PYTHON_CMD%" -m pip install openpyxl>=3.1.0 --quiet 2>nul
    if %errorlevel% neq 0 (
        echo ⚠️ pip not available or installation failed
        echo Please install openpyxl manually:
        echo   py -m pip install openpyxl
        echo.
        echo Continuing anyway - the app may handle missing libraries...
    ) else (
        echo ✅ openpyxl installed successfully
    )
) else (
    echo ✅ openpyxl is available
)

REM Check and install optional libraries
"%PYTHON_CMD%" -c "import ttkthemes" 2>nul
if %errorlevel% neq 0 (
    echo 📥 Installing ttkthemes...
    "%PYTHON_CMD%" -m pip install ttkthemes>=3.2.2 --quiet 2>nul
    if %errorlevel% equ 0 (
        echo ✅ ttkthemes installed
    )
) else (
    echo ✅ ttkthemes available
)

"%PYTHON_CMD%" -c "import PIL" 2>nul
if %errorlevel% neq 0 (
    echo 📥 Installing Pillow...
    "%PYTHON_CMD%" -m pip install Pillow>=10.0.0 --quiet 2>nul
    if %errorlevel% equ 0 (
        echo ✅ Pillow installed
    )
) else (
    echo ✅ Pillow available
)

REM Check tkinter (required)
"%PYTHON_CMD%" -c "import tkinter" 2>nul
if %errorlevel% neq 0 (
    echo ❌ tkinter not available - GUI required
    echo tkinter should be built-in with Python
    echo Please reinstall Python with tkinter support
    pause
    exit /b 1
) else (
    echo ✅ tkinter is available
)

:libraries_ready

echo.
echo All libraries ready
echo.
echo Starting Accounting System...
echo.

REM Run the application
if exist "launcher.py" (
    echo Running launcher.py...
    echo.
    echo Login Info:
    echo Username: admin
    echo Password: admin
    echo.
    "%PYTHON_CMD%" launcher.py
) else if exist "app.py" (
    echo Running app.py...
    echo.
    echo Login Info:
    echo Username: admin
    echo Password: admin
    echo.
    "%PYTHON_CMD%" app.py
) else if exist "start.py" (
    echo Running start.py...
    "%PYTHON_CMD%" start.py
) else if exist "main.py" (
    echo Running main.py...
    "%PYTHON_CMD%" main.py
) else (
    echo ERROR: No launcher file found!
    echo Looking for: launcher.py, app.py, start.py, or main.py
    echo.
    echo Available Python files:
    dir *.py /b 2>nul
    echo.
    pause
    exit /b 1
)

echo.
echo System closed
echo.
pause
