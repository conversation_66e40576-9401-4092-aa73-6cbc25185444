#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع تسجيل شامل للأخطاء
"""

import sys
import traceback
import logging
from datetime import datetime
import os

# إعداد نظام التسجيل
def setup_logging():
    """إعداد نظام تسجيل الأخطاء"""
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # اسم ملف السجل مع الوقت الحالي
    log_filename = f"logs/app_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    # إعداد التسجيل
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    return log_filename

def create_app_with_auto_login(root, logger):
    """إنشاء التطبيق مع تجاوز نظام تسجيل الدخول"""
    try:
        from app import AccountingApp
        from user_manager import UserManager

        logger.info("🔓 بدء إعداد التطبيق مع تجاوز تسجيل الدخول...")

        # إنشاء التطبيق بشكل عادي
        app = AccountingApp(root)

        # تجاوز نظام تسجيل الدخول
        logger.info("🚫 تجاوز نظام تسجيل الدخول...")

        # إغلاق نافذة تسجيل الدخول إذا كانت مفتوحة
        if hasattr(app, 'login_window') and app.login_window:
            try:
                if hasattr(app.login_window, 'root'):
                    app.login_window.root.destroy()
                logger.info("✅ تم إغلاق نافذة تسجيل الدخول")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في إغلاق نافذة تسجيل الدخول: {str(e)}")

        # إظهار النافذة الرئيسية
        logger.info("📺 إظهار النافذة الرئيسية...")
        app.root.deiconify()

        # إعداد الواجهة مباشرة
        if hasattr(app, 'setup_modern_ui'):
            logger.info("🎨 إعداد الواجهة الحديثة...")
            app.setup_modern_ui()

        # بدء التحديث التلقائي إذا كان متاحاً
        if hasattr(app, 'start_auto_refresh'):
            logger.info("🔄 بدء التحديث التلقائي...")
            app.start_auto_refresh()

        # تحديث عنوان النافذة
        app.root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة (تشغيل مباشر)")

        # حفظ معلومات المستخدم الافتراضي
        if hasattr(app, 'user_manager'):
            app.user_manager.current_user = {
                'username': 'admin',
                'role': 'admin',
                'permissions': ['add_account', 'delete_account', 'add_document', 'view_reports', 'manage_users']
            }
            logger.info("👤 تم تعيين مستخدم افتراضي: admin")

        logger.info("✅ تم إعداد التطبيق بنجاح مع تجاوز تسجيل الدخول")
        return app

    except Exception as e:
        logger.error(f"❌ خطأ في إعداد التطبيق: {str(e)}")
        raise

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    log_file = setup_logging()
    logger = logging.getLogger(__name__)

    print("=" * 80)
    print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية")
    print("=" * 80)
    print(f"📝 ملف السجل: {log_file}")
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    logger.info("🚀 بدء تشغيل التطبيق")

    try:
        # استيراد التطبيق الرئيسي
        logger.info("📦 استيراد الوحدات...")

        import tkinter as tk
        logger.info("✅ تم استيراد tkinter بنجاح")

        from app import AccountingApp
        logger.info("✅ تم استيراد AccountingApp بنجاح")

        # إنشاء النافذة الرئيسية
        logger.info("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()

        # إعداد معالج الأخطاء العام
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            logger.error(f"❌ خطأ غير متوقع:\n{error_msg}")
            print(f"\n❌ حدث خطأ غير متوقع! تم تسجيله في: {log_file}")
            print("تفاصيل الخطأ:")
            print(error_msg)

        sys.excepthook = handle_exception

        # إنشاء التطبيق مع تجاوز تسجيل الدخول
        logger.info("🏗️ إنشاء كائن التطبيق مع تجاوز تسجيل الدخول...")

        # إنشاء التطبيق بطريقة مخصصة
        app = create_app_with_auto_login(root, logger)
        logger.info("✅ تم إنشاء التطبيق بنجاح")

        # تشغيل التطبيق
        logger.info("▶️ بدء تشغيل حلقة الأحداث الرئيسية...")
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🔓 تم تجاوز نظام تسجيل الدخول تلقائياً")
        print("📋 لمراقبة الأخطاء، راجع ملف السجل أو هذه النافذة")
        print("-" * 80)

        root.mainloop()

    except ImportError as e:
        error_msg = f"❌ خطأ في الاستيراد: {str(e)}"
        logger.error(error_msg)
        print(error_msg)
        print("💡 تأكد من وجود جميع الملفات المطلوبة:")
        print("   - app.py")
        print("   - excel_manager.py")
        print("   - manage_accounts.py")
        print("   - document_window.py")
        print("   - search_window.py")

    except FileNotFoundError as e:
        error_msg = f"❌ ملف غير موجود: {str(e)}"
        logger.error(error_msg)
        print(error_msg)
        print("💡 تأكد من وجود ملف Excel: accounting_system.xlsx")

    except Exception as e:
        error_msg = f"❌ خطأ عام: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        print(error_msg)
        print(f"📝 تم تسجيل تفاصيل الخطأ في: {log_file}")

    finally:
        logger.info("🔚 انتهاء تشغيل التطبيق")
        print(f"\n📝 سجل التشغيل محفوظ في: {log_file}")
        print("=" * 80)

if __name__ == "__main__":
    main()
