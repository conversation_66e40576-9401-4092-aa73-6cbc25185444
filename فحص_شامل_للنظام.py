#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص شامل لنظام إدارة المستندات المحاسبية
Comprehensive System Check
وزارة الصحة الأردنية - Jordan Ministry of Health
"""

import sys
import os
import importlib
import subprocess
import json
import platform
from datetime import datetime
import traceback

class SystemChecker:
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'platform': platform.platform(),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'checks': {},
            'recommendations': [],
            'errors': [],
            'warnings': []
        }
        
        # قائمة المكتبات المطلوبة
        self.required_packages = {
            'openpyxl': '3.1.0',
            'tkinter': 'builtin',
        }
        
        # قائمة المكتبات الاختيارية
        self.optional_packages = {
            'ttkthemes': '3.2.2',
            'PIL': '10.0.0',
            'pyinstaller': '5.13.0',
            'cryptography': '41.0.0',
            'dateutil': '2.8.2',
            'jsonschema': '4.17.0',
            'arabic_reshaper': '3.0.0',
            'bidi': '0.4.2'
        }
        
        # قائمة الملفات المطلوبة
        self.required_files = [
            'app.py',
            'launcher.py', 
            'excel_manager.py',
            'document_window.py',
            'search_window.py',
            'manage_accounts.py',
            'user_manager.py',
            'requirements.txt'
        ]
        
        # قائمة الملفات الاختيارية
        self.optional_files = [
            'accounting_system.xlsx',
            'Accounting system deductions.xlsx',
            'users.json',
            'setup.py',
            'build_standalone.py',
            'accounting_system.spec'
        ]
        
    def print_header(self):
        """طباعة رأس البرنامج"""
        print("\n" + "="*70)
        print("🔍 فحص شامل لنظام إدارة المستندات المحاسبية")
        print("   Comprehensive System Check")
        print("🏥 وزارة الصحة الأردنية - Jordan Ministry of Health")
        print("="*70)
        
    def check_python_version(self):
        """فحص إصدار Python"""
        print("\n🐍 فحص إصدار Python...")
        
        version = sys.version_info
        version_str = f"{version.major}.{version.minor}.{version.micro}"
        
        print(f"   الإصدار الحالي: Python {version_str}")
        print(f"   المسار: {sys.executable}")
        print(f"   المنصة: {platform.platform()}")
        
        if version.major < 3:
            self.results['errors'].append("Python 2 غير مدعوم - يتطلب Python 3.7+")
            self.results['checks']['python_version'] = False
            print("❌ Python 2 غير مدعوم")
            return False
        elif version.major == 3 and version.minor < 7:
            self.results['errors'].append(f"Python {version_str} قديم - يتطلب Python 3.7+")
            self.results['checks']['python_version'] = False
            print(f"❌ Python {version_str} قديم")
            return False
        else:
            self.results['checks']['python_version'] = True
            print(f"✅ Python {version_str} مناسب")
            return True
            
    def check_pip(self):
        """فحص pip"""
        print("\n📦 فحص مدير الحزم pip...")
        
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True, check=True)
            pip_version = result.stdout.strip()
            print(f"✅ pip متوفر: {pip_version}")
            self.results['checks']['pip'] = True
            return True
            
        except subprocess.CalledProcessError:
            print("❌ pip غير متوفر")
            self.results['errors'].append("pip غير متوفر")
            self.results['checks']['pip'] = False
            return False
        except Exception as e:
            print(f"❌ خطأ في فحص pip: {str(e)}")
            self.results['errors'].append(f"خطأ في فحص pip: {str(e)}")
            self.results['checks']['pip'] = False
            return False
            
    def check_package(self, package_name, required_version=None):
        """فحص مكتبة واحدة"""
        try:
            # محاولة استيراد المكتبة
            if package_name == 'PIL':
                module = importlib.import_module('PIL')
            else:
                module = importlib.import_module(package_name)
            
            # محاولة الحصول على الإصدار
            version = None
            for attr in ['__version__', 'version', 'VERSION']:
                if hasattr(module, attr):
                    version = getattr(module, attr)
                    break
                    
            if version:
                print(f"✅ {package_name} - الإصدار: {version}")
                if required_version and required_version != 'builtin':
                    # مقارنة بسيطة للإصدارات
                    try:
                        from packaging import version as pkg_version
                        if pkg_version.parse(str(version)) >= pkg_version.parse(required_version):
                            return True, version
                        else:
                            print(f"⚠️ {package_name} - إصدار قديم (مطلوب: {required_version})")
                            return False, version
                    except:
                        # إذا لم تكن packaging متوفرة، نقبل أي إصدار
                        return True, version
                else:
                    return True, version
            else:
                print(f"✅ {package_name} - متوفر (لا يمكن تحديد الإصدار)")
                return True, "unknown"
                
        except ImportError:
            print(f"❌ {package_name} - غير متوفر")
            return False, None
        except Exception as e:
            print(f"⚠️ {package_name} - خطأ: {str(e)}")
            return False, str(e)
            
    def check_required_packages(self):
        """فحص المكتبات المطلوبة"""
        print("\n📋 فحص المكتبات المطلوبة...")
        
        all_good = True
        missing_packages = []
        
        for package, version in self.required_packages.items():
            success, found_version = self.check_package(package, version)
            self.results['checks'][f'package_{package}'] = success
            
            if not success:
                all_good = False
                missing_packages.append(package)
                self.results['errors'].append(f"مكتبة مطلوبة مفقودة: {package}")
                
        if missing_packages:
            print(f"\n❌ مكتبات مطلوبة مفقودة: {', '.join(missing_packages)}")
            self.results['recommendations'].append("تثبيت المكتبات المفقودة باستخدام: pip install " + " ".join(missing_packages))
        else:
            print("\n✅ جميع المكتبات المطلوبة متوفرة")
            
        return all_good
        
    def check_optional_packages(self):
        """فحص المكتبات الاختيارية"""
        print("\n🔧 فحص المكتبات الاختيارية...")
        
        missing_optional = []
        
        for package, version in self.optional_packages.items():
            success, found_version = self.check_package(package, version)
            self.results['checks'][f'optional_{package}'] = success
            
            if not success:
                missing_optional.append(package)
                self.results['warnings'].append(f"مكتبة اختيارية مفقودة: {package}")
                
        if missing_optional:
            print(f"\n⚠️ مكتبات اختيارية مفقودة: {', '.join(missing_optional)}")
            self.results['recommendations'].append("لتحسين الأداء، ثبت المكتبات الاختيارية: pip install " + " ".join(missing_optional))
        else:
            print("\n✅ جميع المكتبات الاختيارية متوفرة")
            
    def check_files(self):
        """فحص الملفات المطلوبة"""
        print("\n📁 فحص الملفات المطلوبة...")
        
        missing_required = []
        missing_optional = []
        
        # فحص الملفات المطلوبة
        for file in self.required_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"✅ {file} ({size} بايت)")
                self.results['checks'][f'file_{file}'] = True
            else:
                print(f"❌ {file} - مفقود")
                missing_required.append(file)
                self.results['checks'][f'file_{file}'] = False
                self.results['errors'].append(f"ملف مطلوب مفقود: {file}")
                
        # فحص الملفات الاختيارية
        print("\n📂 فحص الملفات الاختيارية...")
        for file in self.optional_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"✅ {file} ({size} بايت)")
                self.results['checks'][f'optional_file_{file}'] = True
            else:
                print(f"⚠️ {file} - مفقود (اختياري)")
                missing_optional.append(file)
                self.results['checks'][f'optional_file_{file}'] = False
                self.results['warnings'].append(f"ملف اختياري مفقود: {file}")
                
        if missing_required:
            print(f"\n❌ ملفات مطلوبة مفقودة: {', '.join(missing_required)}")
            return False
        else:
            print("\n✅ جميع الملفات المطلوبة موجودة")
            return True
            
    def test_imports(self):
        """اختبار استيراد الوحدات"""
        print("\n🧪 اختبار استيراد الوحدات...")
        
        test_imports = [
            ('tkinter', 'import tkinter as tk'),
            ('openpyxl', 'import openpyxl'),
            ('app', 'from app import AccountingApp'),
            ('excel_manager', 'from excel_manager import ExcelManager'),
            ('user_manager', 'from user_manager import UserManager'),
        ]
        
        failed_imports = []
        
        for name, import_cmd in test_imports:
            try:
                exec(import_cmd)
                print(f"✅ {name} - استيراد ناجح")
                self.results['checks'][f'import_{name}'] = True
            except Exception as e:
                print(f"❌ {name} - فشل الاستيراد: {str(e)}")
                failed_imports.append((name, str(e)))
                self.results['checks'][f'import_{name}'] = False
                self.results['errors'].append(f"فشل استيراد {name}: {str(e)}")
                
        if failed_imports:
            print(f"\n❌ فشل استيراد {len(failed_imports)} وحدة")
            return False
        else:
            print("\n✅ جميع الاستيرادات ناجحة")
            return True
            
    def check_system_resources(self):
        """فحص موارد النظام"""
        print("\n💻 فحص موارد النظام...")
        
        try:
            import psutil
            
            # فحص الذاكرة
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            print(f"💾 الذاكرة الكلية: {memory_gb:.1f} GB")
            print(f"💾 الذاكرة المتاحة: {memory.available / (1024**3):.1f} GB")
            
            if memory_gb < 4:
                self.results['warnings'].append("الذاكرة أقل من 4 GB - قد يؤثر على الأداء")
                
            # فحص القرص
            disk = psutil.disk_usage('.')
            disk_free_gb = disk.free / (1024**3)
            print(f"💽 مساحة القرص المتاحة: {disk_free_gb:.1f} GB")
            
            if disk_free_gb < 1:
                self.results['warnings'].append("مساحة القرص أقل من 1 GB")
                
            # فحص المعالج
            cpu_count = psutil.cpu_count()
            print(f"⚡ عدد المعالجات: {cpu_count}")
            
            self.results['checks']['system_resources'] = True
            
        except ImportError:
            print("⚠️ psutil غير متوفر - لا يمكن فحص موارد النظام")
            self.results['checks']['system_resources'] = False
            
    def check_permissions(self):
        """فحص الصلاحيات"""
        print("\n🔐 فحص صلاحيات الكتابة...")
        
        test_file = "test_permissions.tmp"
        
        try:
            # محاولة إنشاء ملف
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("test")
                
            # محاولة قراءة الملف
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # حذف الملف
            os.remove(test_file)
            
            print("✅ صلاحيات الكتابة متوفرة")
            self.results['checks']['write_permissions'] = True
            return True
            
        except Exception as e:
            print(f"❌ مشكلة في صلاحيات الكتابة: {str(e)}")
            self.results['errors'].append(f"مشكلة في صلاحيات الكتابة: {str(e)}")
            self.results['checks']['write_permissions'] = False
            return False
            
    def generate_report(self):
        """إنشاء تقرير شامل"""
        print("\n📊 إنشاء التقرير...")
        
        # حساب الإحصائيات
        total_checks = len([k for k in self.results['checks'].keys()])
        passed_checks = len([k for k, v in self.results['checks'].items() if v])
        failed_checks = total_checks - passed_checks
        
        success_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
        
        # إضافة الإحصائيات للتقرير
        self.results['statistics'] = {\n            'total_checks': total_checks,\n            'passed_checks': passed_checks,\n            'failed_checks': failed_checks,\n            'success_rate': round(success_rate, 1)\n        }\n        \n        # حفظ التقرير\n        try:\n            with open('system_check_report.json', 'w', encoding='utf-8') as f:\n                json.dump(self.results, f, ensure_ascii=False, indent=2)\n            print(\"✅ تم حفظ التقرير في: system_check_report.json\")\n        except Exception as e:\n            print(f\"⚠️ لم يتم حفظ التقرير: {str(e)}\")\n            \n    def print_summary(self):\n        \"\"\"طباعة ملخص النتائج\"\"\"\n        print(\"\\n\" + \"=\"*70)\n        print(\"📋 ملخص نتائج الفحص\")\n        print(\"=\"*70)\n        \n        stats = self.results.get('statistics', {})\n        print(f\"📊 إجمالي الفحوصات: {stats.get('total_checks', 0)}\")\n        print(f\"✅ نجح: {stats.get('passed_checks', 0)}\")\n        print(f\"❌ فشل: {stats.get('failed_checks', 0)}\")\n        print(f\"📈 معدل النجاح: {stats.get('success_rate', 0)}%\")\n        \n        if self.results['errors']:\n            print(f\"\\n❌ أخطاء ({len(self.results['errors'])}):\")            for error in self.results['errors']:\n                print(f\"   • {error}\")\n                \n        if self.results['warnings']:\n            print(f\"\\n⚠️ تحذيرات ({len(self.results['warnings'])}):\")            for warning in self.results['warnings']:\n                print(f\"   • {warning}\")\n                \n        if self.results['recommendations']:\n            print(f\"\\n💡 توصيات ({len(self.results['recommendations'])}):\")            for rec in self.results['recommendations']:\n                print(f\"   • {rec}\")\n                \n        # تقييم عام\n        if stats.get('success_rate', 0) >= 90:\n            print(\"\\n🎉 النظام في حالة ممتازة!\")\n        elif stats.get('success_rate', 0) >= 75:\n            print(\"\\n✅ النظام في حالة جيدة مع بعض التحسينات المطلوبة\")\n        elif stats.get('success_rate', 0) >= 50:\n            print(\"\\n⚠️ النظام يحتاج إلى إصلاحات\")\n        else:\n            print(\"\\n❌ النظام يحتاج إلى إصلاحات جوهرية\")\n            \n        print(\"=\"*70)\n        \n    def run(self):\n        \"\"\"تشغيل الفحص الشامل\"\"\"\n        self.print_header()\n        \n        try:\n            # فحص Python\n            self.check_python_version()\n            \n            # فحص pip\n            self.check_pip()\n            \n            # فحص المكتبات\n            self.check_required_packages()\n            self.check_optional_packages()\n            \n            # فحص الملفات\n            self.check_files()\n            \n            # اختبار الاستيرادات\n            self.test_imports()\n            \n            # فحص موارد النظام\n            self.check_system_resources()\n            \n            # فحص الصلاحيات\n            self.check_permissions()\n            \n            # إنشاء التقرير\n            self.generate_report()\n            \n            # طباعة الملخص\n            self.print_summary()\n            \n            return len(self.results['errors']) == 0\n            \n        except Exception as e:\n            print(f\"\\n❌ خطأ حرج في الفحص: {str(e)}\")\n            print(\"\\n🔍 تفاصيل الخطأ:\")\n            traceback.print_exc()\n            return False\n\ndef main():\n    \"\"\"الدالة الرئيسية\"\"\"\n    checker = SystemChecker()\n    success = checker.run()\n    \n    print(\"\\n\" + \"=\"*70)\n    if success:\n        print(\"🎉 اكتمل الفحص بنجاح - النظام جاهز للتشغيل!\")\n    else:\n        print(\"⚠️ اكتمل الفحص مع وجود مشاكل - راجع التقرير أعلاه\")\n    print(\"=\"*70)\n    \n    input(\"\\nاضغط Enter للإغلاق...\")\n    return 0 if success else 1\n\nif __name__ == \"__main__\":\n    sys.exit(main())
