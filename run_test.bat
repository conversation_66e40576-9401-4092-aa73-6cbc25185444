@echo off
echo تشغيل اختبار التنسيق الجديد...
echo.

REM محاولة تشغيل Python بطرق مختلفة
python create_sample_account.py
if %errorlevel% neq 0 (
    python3 create_sample_account.py
    if %errorlevel% neq 0 (
        py create_sample_account.py
        if %errorlevel% neq 0 (
            echo خطأ: لم يتم العثور على Python
            pause
            exit /b 1
        )
    )
)

echo.
echo تم إنجاز الاختبار!
echo يمكنك فتح الملف sample_new_format.xlsx لمراجعة التنسيق الجديد
pause
