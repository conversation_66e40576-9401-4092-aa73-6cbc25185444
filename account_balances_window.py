import tkinter as tk
from tkinter import ttk, messagebox
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import os
from datetime import datetime

class AccountBalancesWindow(tk.Toplevel):
    """نافذة أرصدة الحسابات مع تقارير المقبوضات والمواد"""

    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("تقارير أرصدة الحسابات")
        self.parent = parent

        # تكوين النافذة
        self.geometry("600x500")  # زيادة الارتفاع لإظهار الأزرار بوضوح
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent.root)
        self.grab_set()

        # إنشاء الواجهة
        self.create_interface()

        # توسيط النافذة
        self.center_window()

        # ربط إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="30")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # عنوان النافذة
        title_label = ttk.Label(main_frame,
                               text="📊 تقارير أرصدة الحسابات",
                               font=('Arial', 18, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 30))

        # وصف النافذة
        desc_label = ttk.Label(main_frame,
                              text="اختر نوع التقرير المطلوب إنشاؤه",
                              font=('Arial', 12))
        desc_label.grid(row=1, column=0, pady=(0, 40))

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)

        # زر تقرير حسابات المقبوضات (محسن)
        receipts_btn = ttk.Button(buttons_frame,
                                 text="💰 تقرير حسابات المقبوضات",
                                 command=self.create_receipts_accounts_report,
                                 style='Accent.TButton')
        receipts_btn.grid(row=0, column=0, padx=(0, 10), pady=10, sticky=(tk.W, tk.E))

        # زر تقرير حسابات المواد (محسن)
        materials_btn = ttk.Button(buttons_frame,
                                  text="📦 تقرير حسابات المواد",
                                  command=self.create_materials_accounts_report,
                                  style='Accent.TButton')
        materials_btn.grid(row=0, column=1, padx=(10, 0), pady=10, sticky=(tk.W, tk.E))

        # إطار معلومات إضافية
        info_frame = ttk.LabelFrame(main_frame, text="معلومات التقارير", padding="20")
        info_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(30, 20))

        # معلومات تقرير المقبوضات
        receipts_info = ttk.Label(info_frame,
                                 text="🔹 تقرير حسابات المقبوضات: يتم إنشاؤه في ملف 'Accounting system deductions.xlsx'\n"
                                      "   جدول منسق يحتوي على أسماء الحسابات وأرصدتها مع إجمالي نهائي",
                                 font=('Arial', 10),
                                 justify='right')
        receipts_info.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # معلومات تقرير حسابات المواد
        materials_info = ttk.Label(info_frame,
                                  text="🔹 تقرير حسابات المواد: يتم إنشاؤه في ملف 'accounting_system.xlsx'\n"
                                       "   جدول منسق يحتوي على أسماء حسابات النظام وأرصدتها مع إجمالي نهائي",
                                  font=('Arial', 10),
                                  justify='right')
        materials_info.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # إطار أزرار التحكم
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(20, 0))

        # زر المساعدة
        help_btn = ttk.Button(control_frame,
                             text="❓ مساعدة",
                             command=self.show_help)
        help_btn.pack(side=tk.LEFT)

        # زر الإغلاق
        close_btn = ttk.Button(control_frame,
                              text="❌ إغلاق",
                              command=self.on_closing)
        close_btn.pack(side=tk.RIGHT)

    def create_materials_accounts_report(self):
        """إنشاء تقرير حسابات المواد المحسن"""
        try:
            materials_file = "accounting_system.xlsx"

            if not os.path.exists(materials_file):
                messagebox.showerror("خطأ", f"ملف النظام غير موجود: {materials_file}")
                return

            # عرض رسالة تحميل
            messagebox.showinfo("جاري المعالجة", "جاري إنشاء تقرير حسابات المواد...")

            # تحميل الملف
            workbook = openpyxl.load_workbook(materials_file)

            # حذف التقرير القديم إن وجد
            report_sheet_name = "تقرير حسابات المواد"
            if report_sheet_name in workbook.sheetnames:
                workbook.remove(workbook[report_sheet_name])

            # إنشاء ورقة التقرير الجديدة
            ws = workbook.create_sheet(report_sheet_name, 0)  # في المقدمة
            ws.sheet_properties.rightToLeft = True

            # إعداد التقرير المحسن
            self.setup_enhanced_materials_report(ws, workbook)

            # حفظ الملف
            workbook.save(materials_file)
            workbook.close()

            messagebox.showinfo("نجاح ✅",
                               f"تم إنشاء تقرير حسابات المواد بنجاح!\n\n"
                               f"📁 الملف: {materials_file}\n"
                               f"📄 الورقة: {report_sheet_name}\n\n"
                               f"📈 جدول منسق مع تحديث تلقائي للأرصدة")

        except Exception as e:
            messagebox.showerror("خطأ ❌", f"فشل في إنشاء تقرير المواد:\n\n{str(e)}")

    def create_receipts_accounts_report(self):
        """إنشاء تقرير حسابات المقبوضات المحسن"""
        try:
            deductions_file = "Accounting system deductions.xlsx"

            if not os.path.exists(deductions_file):
                messagebox.showerror("خطأ", f"ملف المقبوضات غير موجود: {deductions_file}")
                return

            # عرض رسالة تحميل
            loading_msg = messagebox.showinfo("جاري المعالجة", "جاري إنشاء تقرير حسابات المقبوضات...")

            # تحميل الملف
            workbook = openpyxl.load_workbook(deductions_file)

            # حذف التقرير القديم إن وجد
            report_sheet_name = "تقرير حسابات المقبوضات"
            if report_sheet_name in workbook.sheetnames:
                workbook.remove(workbook[report_sheet_name])

            # إنشاء ورقة التقرير الجديدة
            ws = workbook.create_sheet(report_sheet_name, 0)  # في المقدمة
            ws.sheet_properties.rightToLeft = True

            # إعداد التقرير المحسن
            self.setup_enhanced_receipts_report(ws, workbook)

            # حفظ الملف
            workbook.save(deductions_file)
            workbook.close()

            messagebox.showinfo("نجاح ✅",
                               f"تم إنشاء تقرير حسابات المقبوضات بنجاح!\n\n"
                               f"📁 الملف: {deductions_file}\n"
                               f"📄 الورقة: {report_sheet_name}\n\n"
                               f"📈 جدول منسق مع تحديث تلقائي للأرصدة")

        except Exception as e:
            messagebox.showerror("خطأ ❌", f"فشل في إنشاء تقرير المقبوضات:\n\n{str(e)}")

    def create_materials_balances_report(self):
        """إنشاء تقرير أرصدة حسابات المواد"""
        try:
            materials_file = "accounting_system.xlsx"

            if not os.path.exists(materials_file):
                messagebox.showerror("خطأ", f"ملف المواد غير موجود: {materials_file}")
                return

            # تحميل الملف
            workbook = openpyxl.load_workbook(materials_file)

            # حذف التقرير القديم إن وجد
            report_sheet_name = "تقرير أرصدة المواد"
            if report_sheet_name in workbook.sheetnames:
                workbook.remove(workbook[report_sheet_name])

            # إنشاء ورقة التقرير الجديدة
            ws = workbook.create_sheet(report_sheet_name, 0)  # في المقدمة
            ws.sheet_properties.rightToLeft = True

            # إعداد التقرير
            self.setup_materials_report(ws, workbook)

            # حفظ الملف
            workbook.save(materials_file)
            workbook.close()

            messagebox.showinfo("نجاح",
                               f"تم إنشاء تقرير أرصدة المواد بنجاح!\n"
                               f"الملف: {materials_file}\n"
                               f"الورقة: {report_sheet_name}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المواد:\n{str(e)}")

    def setup_receipts_report(self, ws, workbook):
        """إعداد تقرير أرصدة المقبوضات"""
        # إعداد التنسيقات
        title_font = Font(size=16, bold=True)
        header_font = Font(size=12, bold=True)
        normal_font = Font(size=10)
        total_font = Font(size=12, bold=True)

        center_alignment = Alignment(horizontal='center', vertical='center')
        right_alignment = Alignment(horizontal='right', vertical='center')

        thick_border = Border(
            left=Side(style='thick'), right=Side(style='thick'),
            top=Side(style='thick'), bottom=Side(style='thick')
        )
        thin_border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )

        # رأس التقرير
        ws['A1'] = 'وزارة الصحة الأردنية'
        ws['A1'].font = title_font
        ws['A1'].alignment = center_alignment
        ws.merge_cells('A1:D1')

        ws['A2'] = 'قسم الخصومات والاستقطاعات'
        ws['A2'].font = header_font
        ws['A2'].alignment = center_alignment
        ws.merge_cells('A2:D2')

        ws['A4'] = 'تقرير أرصدة حسابات المقبوضات'
        ws['A4'].font = title_font
        ws['A4'].alignment = center_alignment
        ws.merge_cells('A4:D4')

        ws['A5'] = f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M")}'
        ws['A5'].font = normal_font
        ws['A5'].alignment = center_alignment
        ws.merge_cells('A5:D5')

        # عناوين الجدول
        headers = ['م', 'اسم الحساب', 'عدد المستندات', 'إجمالي الرصيد']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=7, column=col, value=header)
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thick_border

        # جمع بيانات الحسابات
        accounts_data = []
        total_balance = 0.0
        total_documents = 0

        for sheet_name in workbook.sheetnames:
            if sheet_name not in ["مرحباً", "تقرير أرصدة المقبوضات"]:
                try:
                    account_ws = workbook[sheet_name]
                    balance = self.calculate_account_balance_receipts(account_ws)
                    doc_count = self.count_account_documents_receipts(account_ws)

                    if balance > 0 or doc_count > 0:  # فقط الحسابات التي لها أرصدة أو مستندات
                        accounts_data.append({
                            'name': sheet_name,
                            'balance': balance,
                            'documents': doc_count
                        })
                        total_balance += balance
                        total_documents += doc_count

                except Exception as e:
                    print(f"خطأ في معالجة الحساب {sheet_name}: {str(e)}")

        # ترتيب الحسابات حسب الرصيد (تنازلي)
        accounts_data.sort(key=lambda x: x['balance'], reverse=True)

        # إضافة بيانات الحسابات
        row = 8
        for i, account in enumerate(accounts_data, 1):
            ws.cell(row=row, column=1, value=i)
            ws.cell(row=row, column=2, value=account['name'])
            ws.cell(row=row, column=3, value=account['documents'])
            ws.cell(row=row, column=4, value=account['balance'])

            # تنسيق الخلايا
            for col in range(1, 5):
                cell = ws.cell(row=row, column=col)
                cell.font = normal_font
                cell.alignment = center_alignment if col != 2 else right_alignment
                cell.border = thin_border
                if col == 4:  # عمود الرصيد
                    cell.number_format = '#,##0.000'

            row += 1

        # صف الإجمالي
        ws.cell(row=row, column=1, value='')
        ws.cell(row=row, column=2, value='الإجمالي العام')
        ws.cell(row=row, column=3, value=total_documents)
        ws.cell(row=row, column=4, value=total_balance)

        # تنسيق صف الإجمالي
        for col in range(1, 5):
            cell = ws.cell(row=row, column=col)
            cell.font = total_font
            cell.alignment = center_alignment if col != 2 else right_alignment
            cell.border = thick_border
            if col == 4:  # عمود الرصيد
                cell.number_format = '#,##0.000'

        # تعيين عرض الأعمدة
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 40
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 20

        # معلومات إضافية
        info_row = row + 3
        ws.cell(row=info_row, column=1, value=f'عدد الحسابات: {len(accounts_data)}')
        ws.cell(row=info_row + 1, column=1, value=f'إجمالي المستندات: {total_documents}')
        ws.cell(row=info_row + 2, column=1, value=f'إجمالي الأرصدة: {total_balance:.3f}')

    def setup_materials_report(self, ws, workbook):
        """إعداد تقرير أرصدة المواد"""
        # إعداد التنسيقات (نفس التنسيقات)
        title_font = Font(size=16, bold=True)
        header_font = Font(size=12, bold=True)
        normal_font = Font(size=10)
        total_font = Font(size=12, bold=True)

        center_alignment = Alignment(horizontal='center', vertical='center')
        right_alignment = Alignment(horizontal='right', vertical='center')

        thick_border = Border(
            left=Side(style='thick'), right=Side(style='thick'),
            top=Side(style='thick'), bottom=Side(style='thick')
        )
        thin_border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )

        # رأس التقرير
        ws['A1'] = 'وزارة الصحة الأردنية'
        ws['A1'].font = title_font
        ws['A1'].alignment = center_alignment
        ws.merge_cells('A1:D1')

        ws['A2'] = 'قسم المحاسبة والمواد'
        ws['A2'].font = header_font
        ws['A2'].alignment = center_alignment
        ws.merge_cells('A2:D2')

        ws['A4'] = 'تقرير أرصدة حسابات المواد'
        ws['A4'].font = title_font
        ws['A4'].alignment = center_alignment
        ws.merge_cells('A4:D4')

        ws['A5'] = f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M")}'
        ws['A5'].font = normal_font
        ws['A5'].alignment = center_alignment
        ws.merge_cells('A5:D5')

        # عناوين الجدول
        headers = ['م', 'اسم الحساب', 'عدد المستندات', 'إجمالي الرصيد']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=7, column=col, value=header)
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thick_border

        # جمع بيانات الحسابات
        accounts_data = []
        total_balance = 0.0
        total_documents = 0

        for sheet_name in workbook.sheetnames:
            if sheet_name not in ["أرصدة الحسابات", "تقرير أرصدة المواد", "التقارير"]:
                try:
                    account_ws = workbook[sheet_name]
                    balance = self.calculate_account_balance_materials(account_ws)
                    doc_count = self.count_account_documents_materials(account_ws)

                    if balance > 0 or doc_count > 0:  # فقط الحسابات التي لها أرصدة أو مستندات
                        accounts_data.append({
                            'name': sheet_name,
                            'balance': balance,
                            'documents': doc_count
                        })
                        total_balance += balance
                        total_documents += doc_count

                except Exception as e:
                    print(f"خطأ في معالجة الحساب {sheet_name}: {str(e)}")

        # ترتيب الحسابات حسب الرصيد (تنازلي)
        accounts_data.sort(key=lambda x: x['balance'], reverse=True)

        # إضافة بيانات الحسابات
        row = 8
        for i, account in enumerate(accounts_data, 1):
            ws.cell(row=row, column=1, value=i)
            ws.cell(row=row, column=2, value=account['name'])
            ws.cell(row=row, column=3, value=account['documents'])
            ws.cell(row=row, column=4, value=account['balance'])

            # تنسيق الخلايا
            for col in range(1, 5):
                cell = ws.cell(row=row, column=col)
                cell.font = normal_font
                cell.alignment = center_alignment if col != 2 else right_alignment
                cell.border = thin_border
                if col == 4:  # عمود الرصيد
                    cell.number_format = '#,##0.000'

            row += 1

        # صف الإجمالي
        ws.cell(row=row, column=1, value='')
        ws.cell(row=row, column=2, value='الإجمالي العام')
        ws.cell(row=row, column=3, value=total_documents)
        ws.cell(row=row, column=4, value=total_balance)

        # تنسيق صف الإجمالي
        for col in range(1, 5):
            cell = ws.cell(row=row, column=col)
            cell.font = total_font
            cell.alignment = center_alignment if col != 2 else right_alignment
            cell.border = thick_border
            if col == 4:  # عمود الرصيد
                cell.number_format = '#,##0.000'

        # تعيين عرض الأعمدة
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 40
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 20

        # معلومات إضافية
        info_row = row + 3
        ws.cell(row=info_row, column=1, value=f'عدد الحسابات: {len(accounts_data)}')
        ws.cell(row=info_row + 1, column=1, value=f'إجمالي المستندات: {total_documents}')
        ws.cell(row=info_row + 2, column=1, value=f'إجمالي الأرصدة: {total_balance:.3f}')

    def calculate_account_balance_receipts(self, ws):
        """حساب رصيد حساب المقبوضات"""
        total = 0.0
        try:
            # البحث في الأعمدة A, C, E, G, I, K, M (أعمدة المبالغ)
            amount_columns = ['A', 'C', 'E', 'G', 'I', 'K', 'M']

            for col in amount_columns:
                for row in range(13, 1000):  # البحث في نطاق واسع
                    try:
                        cell_value = ws[f'{col}{row}'].value
                        if isinstance(cell_value, (int, float)) and cell_value > 0:
                            total += cell_value
                        elif row > 50 and not cell_value:  # توقف إذا وصلنا لمنطقة فارغة
                            break
                    except:
                        continue

        except Exception as e:
            print(f"خطأ في حساب رصيد المقبوضات: {str(e)}")

        return total

    def count_account_documents_receipts(self, ws):
        """عد مستندات حساب المقبوضات"""
        count = 0
        try:
            # البحث في الأعمدة B, D, F, H, J, L, N (أعمدة المستندات)
            doc_columns = ['B', 'D', 'F', 'H', 'J', 'L', 'N']

            for col in doc_columns:
                for row in range(13, 1000):  # البحث في نطاق واسع
                    try:
                        cell_value = ws[f'{col}{row}'].value
                        if (cell_value and
                            str(cell_value).strip() and
                            str(cell_value).strip() != '0' and
                            str(cell_value).strip().lower() != 'ما قبله'):
                            count += 1
                        elif row > 50 and not cell_value:  # توقف إذا وصلنا لمنطقة فارغة
                            break
                    except:
                        continue

        except Exception as e:
            print(f"خطأ في عد مستندات المقبوضات: {str(e)}")

        return count

    def calculate_account_balance_materials(self, ws):
        """حساب رصيد حساب المواد"""
        total = 0.0
        try:
            # البحث في العمود C (عمود المبلغ في نظام المواد)
            for row in range(10, 1000):  # البحث في نطاق واسع
                try:
                    cell_value = ws[f'C{row}'].value
                    if isinstance(cell_value, (int, float)) and cell_value > 0:
                        total += cell_value
                    elif row > 50 and not cell_value:  # توقف إذا وصلنا لمنطقة فارغة
                        break
                except:
                    continue

        except Exception as e:
            print(f"خطأ في حساب رصيد المواد: {str(e)}")

        return total

    def count_account_documents_materials(self, ws):
        """عد مستندات حساب المواد"""
        count = 0
        try:
            # البحث في العمود B (عمود رقم المستند في نظام المواد)
            for row in range(10, 1000):  # البحث في نطاق واسع
                try:
                    cell_value = ws[f'B{row}'].value
                    if (cell_value and
                        str(cell_value).strip() and
                        str(cell_value).strip() != '0'):
                        count += 1
                    elif row > 50 and not cell_value:  # توقف إذا وصلنا لمنطقة فارغة
                        break
                except:
                    continue

        except Exception as e:
            print(f"خطأ في عد مستندات المواد: {str(e)}")

        return count

    def setup_enhanced_receipts_report(self, ws, workbook):
        """إعداد تقرير حسابات المقبوضات المحسن"""
        # إعداد التنسيقات المحسنة
        title_font = Font(size=18, bold=True, color="FFFFFF")
        subtitle_font = Font(size=14, bold=True, color="2C3E50")
        header_font = Font(size=12, bold=True, color="FFFFFF")
        normal_font = Font(size=11)
        total_font = Font(size=13, bold=True, color="E74C3C")

        center_alignment = Alignment(horizontal='center', vertical='center')
        right_alignment = Alignment(horizontal='right', vertical='center')

        # حدود مختلفة
        thick_border = Border(
            left=Side(style='thick', color='2C3E50'),
            right=Side(style='thick', color='2C3E50'),
            top=Side(style='thick', color='2C3E50'),
            bottom=Side(style='thick', color='2C3E50')
        )
        thin_border = Border(
            left=Side(style='thin', color='34495E'),
            right=Side(style='thin', color='34495E'),
            top=Side(style='thin', color='34495E'),
            bottom=Side(style='thin', color='34495E')
        )

        # ألوان مختلفة
        title_fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")
        header_fill = PatternFill(start_color="3498DB", end_color="3498DB", fill_type="solid")
        total_fill = PatternFill(start_color="E8F6F3", end_color="E8F6F3", fill_type="solid")

        # رأس التقرير المحسن
        ws.merge_cells('A1:E2')
        ws['A1'] = '🏥 وزارة الصحة الأردنية'
        ws['A1'].font = title_font
        ws['A1'].alignment = center_alignment
        ws['A1'].fill = title_fill

        ws.merge_cells('A3:E3')
        ws['A3'] = '📊 قسم الخصومات والاستقطاعات'
        ws['A3'].font = subtitle_font
        ws['A3'].alignment = center_alignment

        ws.merge_cells('A5:E5')
        ws['A5'] = '💰 تقرير حسابات المقبوضات'
        ws['A5'].font = Font(size=16, bold=True, color="2C3E50")
        ws['A5'].alignment = center_alignment

        ws.merge_cells('A6:E6')
        ws['A6'] = f'📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M")} | 🔄 تحديث تلقائي'
        ws['A6'].font = Font(size=10, italic=True, color="7F8C8D")
        ws['A6'].alignment = center_alignment

        # عناوين الجدول المحسنة
        headers = ['🔢 م', '💼 اسم الحساب', '📄 عدد المستندات', '💵 إجمالي الرصيد', '📈 النسبة %']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=8, column=col, value=header)
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thick_border
            cell.fill = header_fill

        # جمع بيانات الحسابات مع التحديث التلقائي
        accounts_data = []
        total_balance = 0.0
        total_documents = 0

        for sheet_name in workbook.sheetnames:
            if sheet_name not in ["مرحباً", "تقرير حسابات المقبوضات"]:
                try:
                    account_ws = workbook[sheet_name]
                    balance = self.calculate_account_balance_receipts(account_ws)
                    doc_count = self.count_account_documents_receipts(account_ws)

                    # إضافة جميع الحسابات (حتى الفارغة)
                    accounts_data.append({
                        'name': sheet_name,
                        'balance': balance,
                        'documents': doc_count
                    })
                    total_balance += balance
                    total_documents += doc_count

                except Exception as e:
                    print(f"خطأ في معالجة الحساب {sheet_name}: {str(e)}")

        # ترتيب الحسابات حسب الرصيد (تنازلي)
        accounts_data.sort(key=lambda x: x['balance'], reverse=True)

        # إضافة بيانات الحسابات مع التنسيق المحسن
        row = 9
        for i, account in enumerate(accounts_data, 1):
            # حساب النسبة المئوية
            percentage = (account['balance'] / total_balance * 100) if total_balance > 0 else 0

            ws.cell(row=row, column=1, value=i)
            ws.cell(row=row, column=2, value=account['name'])
            ws.cell(row=row, column=3, value=account['documents'])
            ws.cell(row=row, column=4, value=account['balance'])
            ws.cell(row=row, column=5, value=f"{percentage:.1f}%")

            # تنسيق الخلايا مع ألوان متناوبة
            row_fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid") if i % 2 == 0 else None

            for col in range(1, 6):
                cell = ws.cell(row=row, column=col)
                cell.font = normal_font
                cell.alignment = center_alignment if col != 2 else right_alignment
                cell.border = thin_border
                if row_fill:
                    cell.fill = row_fill

                if col == 4:  # عمود الرصيد
                    cell.number_format = '#,##0.000'

            row += 1

        # صف الإجمالي المحسن
        ws.cell(row=row, column=1, value='')
        ws.cell(row=row, column=2, value='📊 الإجمالي العام')
        ws.cell(row=row, column=3, value=total_documents)
        ws.cell(row=row, column=4, value=total_balance)
        ws.cell(row=row, column=5, value='100.0%')

        # تنسيق صف الإجمالي
        for col in range(1, 6):
            cell = ws.cell(row=row, column=col)
            cell.font = total_font
            cell.alignment = center_alignment if col != 2 else right_alignment
            cell.border = thick_border
            cell.fill = total_fill
            if col == 4:  # عمود الرصيد
                cell.number_format = '#,##0.000'

        # تعيين عرض الأعمدة بشكل محسن
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 45
        ws.column_dimensions['C'].width = 18
        ws.column_dimensions['D'].width = 20
        ws.column_dimensions['E'].width = 15

        # معلومات إضافية محسنة
        info_row = row + 3
        info_data = [
            f'📁 عدد الحسابات: {len(accounts_data)}',
            f'📄 إجمالي المستندات: {total_documents:,}',
            f'💰 إجمالي الأرصدة: {total_balance:,.3f} دينار',
            f'🔄 آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        ]

        for i, info in enumerate(info_data):
            cell = ws.cell(row=info_row + i, column=1, value=info)
            cell.font = Font(size=10, bold=True, color="2C3E50")
            ws.merge_cells(f'A{info_row + i}:B{info_row + i}')

    def setup_enhanced_materials_report(self, ws, workbook):
        """إعداد تقرير حسابات المواد المحسن"""
        # إعداد التنسيقات المحسنة
        title_font = Font(size=18, bold=True, color="FFFFFF")
        subtitle_font = Font(size=14, bold=True, color="2C3E50")
        header_font = Font(size=12, bold=True, color="FFFFFF")
        normal_font = Font(size=11)
        total_font = Font(size=13, bold=True, color="E74C3C")

        center_alignment = Alignment(horizontal='center', vertical='center')
        right_alignment = Alignment(horizontal='right', vertical='center')

        # حدود مختلفة
        thick_border = Border(
            left=Side(style='thick', color='2C3E50'),
            right=Side(style='thick', color='2C3E50'),
            top=Side(style='thick', color='2C3E50'),
            bottom=Side(style='thick', color='2C3E50')
        )
        thin_border = Border(
            left=Side(style='thin', color='34495E'),
            right=Side(style='thin', color='34495E'),
            top=Side(style='thin', color='34495E'),
            bottom=Side(style='thin', color='34495E')
        )

        # ألوان مختلفة
        title_fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")
        header_fill = PatternFill(start_color="27AE60", end_color="27AE60", fill_type="solid")  # أخضر للمواد
        total_fill = PatternFill(start_color="E8F8F5", end_color="E8F8F5", fill_type="solid")

        # رأس التقرير المحسن
        ws.merge_cells('A1:E2')
        ws['A1'] = '🏥 وزارة الصحة الأردنية'
        ws['A1'].font = title_font
        ws['A1'].alignment = center_alignment
        ws['A1'].fill = title_fill

        ws.merge_cells('A3:E3')
        ws['A3'] = '📦 قسم المحاسبة والمواد'
        ws['A3'].font = subtitle_font
        ws['A3'].alignment = center_alignment

        ws.merge_cells('A5:E5')
        ws['A5'] = '📦 تقرير حسابات المواد'
        ws['A5'].font = Font(size=16, bold=True, color="2C3E50")
        ws['A5'].alignment = center_alignment

        ws.merge_cells('A6:E6')
        ws['A6'] = f'📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M")} | 🔄 تحديث تلقائي'
        ws['A6'].font = Font(size=10, italic=True, color="7F8C8D")
        ws['A6'].alignment = center_alignment

        # عناوين الجدول المحسنة
        headers = ['🔢 م', '📦 اسم الحساب', '📄 عدد المستندات', '💵 إجمالي الرصيد', '📈 النسبة %']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=8, column=col, value=header)
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thick_border
            cell.fill = header_fill

        # جمع بيانات حسابات النظام مع التحديث التلقائي
        accounts_data = []
        total_balance = 0.0
        total_documents = 0

        # الصفحات المستثناة من التقرير
        excluded_sheets = [
            "مرحباً", "أرصدة الحسابات", "التقارير",
            "تقرير المستندات", "التقرير الإجمالي",
            "تقرير حسابات المواد"
        ]

        for sheet_name in workbook.sheetnames:
            if sheet_name not in excluded_sheets:
                try:
                    account_ws = workbook[sheet_name]
                    balance = self.calculate_account_balance_materials(account_ws)
                    doc_count = self.count_account_documents_materials(account_ws)

                    # إضافة جميع حسابات النظام
                    accounts_data.append({
                        'name': sheet_name,
                        'balance': balance,
                        'documents': doc_count
                    })
                    total_balance += balance
                    total_documents += doc_count

                except Exception as e:
                    print(f"خطأ في معالجة حساب النظام {sheet_name}: {str(e)}")

        # ترتيب الحسابات حسب الرصيد (تنازلي)
        accounts_data.sort(key=lambda x: x['balance'], reverse=True)

        # إضافة بيانات الحسابات مع التنسيق المحسن
        row = 9
        for i, account in enumerate(accounts_data, 1):
            # حساب النسبة المئوية
            percentage = (account['balance'] / total_balance * 100) if total_balance > 0 else 0

            ws.cell(row=row, column=1, value=i)
            ws.cell(row=row, column=2, value=account['name'])
            ws.cell(row=row, column=3, value=account['documents'])
            ws.cell(row=row, column=4, value=account['balance'])
            ws.cell(row=row, column=5, value=f"{percentage:.1f}%")

            # تنسيق الخلايا مع ألوان متناوبة
            row_fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid") if i % 2 == 0 else None

            for col in range(1, 6):
                cell = ws.cell(row=row, column=col)
                cell.font = normal_font
                cell.alignment = center_alignment if col != 2 else right_alignment
                cell.border = thin_border
                if row_fill:
                    cell.fill = row_fill

                if col == 4:  # عمود الرصيد
                    cell.number_format = '#,##0.000'

            row += 1

        # صف الإجمالي المحسن
        ws.cell(row=row, column=1, value='')
        ws.cell(row=row, column=2, value='📈 الإجمالي العام')
        ws.cell(row=row, column=3, value=total_documents)
        ws.cell(row=row, column=4, value=total_balance)
        ws.cell(row=row, column=5, value='100.0%')

        # تنسيق صف الإجمالي
        for col in range(1, 6):
            cell = ws.cell(row=row, column=col)
            cell.font = total_font
            cell.alignment = center_alignment if col != 2 else right_alignment
            cell.border = thick_border
            cell.fill = total_fill
            if col == 4:  # عمود الرصيد
                cell.number_format = '#,##0.000'

        # تعيين عرض الأعمدة بشكل محسن
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 45
        ws.column_dimensions['C'].width = 18
        ws.column_dimensions['D'].width = 20
        ws.column_dimensions['E'].width = 15

        # معلومات إضافية محسنة
        info_row = row + 3
        info_data = [
            f'📁 عدد حسابات النظام: {len(accounts_data)}',
            f'📄 إجمالي المستندات: {total_documents:,}',
            f'💰 إجمالي الأرصدة: {total_balance:,.3f} دينار',
            f'🔄 آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        ]

        for i, info in enumerate(info_data):
            cell = ws.cell(row=info_row + i, column=1, value=info)
            cell.font = Font(size=10, bold=True, color="2C3E50")
            ws.merge_cells(f'A{info_row + i}:B{info_row + i}')

    def calculate_account_balance_materials(self, ws):
        """حساب رصيد حساب المواد (نظام رئيسي)"""
        try:
            # الرصيد الافتتاحي من الخلية A7
            opening_balance = ws['A7'].value or 0
            if isinstance(opening_balance, str):
                try:
                    opening_balance = float(opening_balance.replace(',', ''))
                except:
                    opening_balance = 0

            # حساب مجموع المستندات
            documents_total = 0

            # فحص جميع الأقسام في الجداول
            for section in range(6):  # 6 أقسام
                col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                # فحص المستندات في هذا القسم
                for row in range(8, 33):  # من الصف 8 إلى 32
                    try:
                        amount_cell = ws.cell(row=row, column=col)
                        if amount_cell.value and isinstance(amount_cell.value, (int, float)):
                            documents_total += float(amount_cell.value)
                    except:
                        continue

            # الرصيد النهائي = الرصيد الافتتاحي + مجموع المستندات
            final_balance = opening_balance + documents_total

            return final_balance

        except Exception as e:
            print(f"خطأ في حساب رصيد المواد: {str(e)}")
            return 0.0

    def count_account_documents_materials(self, ws):
        """عد مستندات حساب المواد (نظام رئيسي)"""
        count = 0
        try:
            # فحص جميع الأقسام
            for section in range(6):  # 6 أقسام
                col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                # عد المستندات في هذا القسم
                for row in range(8, 33):  # من الصف 8 إلى 32
                    try:
                        amount_cell = ws.cell(row=row, column=col)
                        doc_cell = ws.cell(row=row, column=col+1)

                        # فحص وجود مبلغ ورقم مستند
                        if (amount_cell.value and isinstance(amount_cell.value, (int, float)) and
                            doc_cell.value and str(doc_cell.value).strip() and
                            str(doc_cell.value).strip().lower() != 'ما قبله'):
                            count += 1
                    except:
                        continue

        except Exception as e:
            print(f"خطأ في عد مستندات المواد: {str(e)}")

        return count

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = (
            "📚 مساعدة - تقارير أرصدة الحسابات\n\n"
            "💰 تقرير حسابات المقبوضات (محسن):\n"
            "   • يتم إنشاؤه في ملف 'Accounting system deductions.xlsx'\n"
            "   • جدول منسق بألوان زرقاء وحدود محسنة\n"
            "   • يحتوي على جميع حسابات المقبوضات وأرصدتها\n"
            "   • يعرض عدد المستندات والنسب المئوية\n"
            "   • تحديث تلقائي للبيانات والأرصدة\n\n"
            "📦 تقرير حسابات المواد (جديد):\n"
            "   • يتم إنشاؤه في ملف 'accounting_system.xlsx'\n"
            "   • جدول منسق بألوان خضراء وحدود محسنة\n"
            "   • يحتوي على جميع حسابات النظام وأرصدتها\n"
            "   • يعرض عدد المستندات والنسب المئوية\n"
            "   • تحديث تلقائي للبيانات والأرصدة\n"
            "   • إجمالي نهائي مع معلومات إحصائية\n\n"
            "🎆 ميزات مشتركة:\n"
            "   • تنسيق محسن بألوان ورموز تعبيرية\n"
            "   • حساب النسب المئوية لكل حساب\n"
            "   • معلومات إحصائية مفصلة في نهاية التقرير\n"
            "   • تاريخ ووقت التحديث التلقائي\n"
            "   • صفوف متناوبة الألوان لسهولة القراءة"
        )
        messagebox.showinfo("📚 مساعدة", help_text)

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """معالج إغلاق النافذة"""
        self.destroy()
