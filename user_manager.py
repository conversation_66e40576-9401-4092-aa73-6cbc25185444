#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستخدمين والصلاحيات
"""

import json
import hashlib
import os
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox

class UserManager:
    def __init__(self):
        self.users_file = "users.json"
        self.current_user = None
        self.users_data = self.load_users()

        # إنشاء المستخدم الرئيسي إذا لم يكن موجوداً
        if not self.users_data:
            self.create_default_admin()

    def load_users(self):
        """تحميل بيانات المستخدمين"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل بيانات المستخدمين: {str(e)}")
            return {}

    def save_users(self):
        """حفظ بيانات المستخدمين"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ بيانات المستخدمين: {str(e)}")
            return False

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def create_default_admin(self):
        """إنشاء المستخدم الرئيسي الافتراضي"""
        admin_user = {
            "password": self.hash_password("admin"),  # كلمة المرور: admin
            "role": "admin",
            "full_name": "المدير العام - وزارة الصحة",
            "permissions": {
                "add_account": True,
                "edit_account": True,
                "delete_account": True,
                "add_document": True,
                "edit_document": True,
                "delete_document": True,
                "view_reports": True,
                "manage_users": True,
                "backup_restore": True,
                "system_admin": True  # صلاحية إدارة النظام
            },
            "created_date": datetime.now().isoformat(),
            "last_login": None,
            "active": True,
            "can_change_password": True
        }

        self.users_data["admin"] = admin_user
        self.save_users()
        print("✅ تم إنشاء المستخدم الرئيسي: admin / admin")

    def authenticate(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        if username in self.users_data:
            user = self.users_data[username]
            if user.get("active", True) and user["password"] == self.hash_password(password):
                # تحديث آخر تسجيل دخول
                user["last_login"] = datetime.now().isoformat()
                self.save_users()
                self.current_user = {
                    "username": username,
                    "role": user["role"],
                    "full_name": user["full_name"],
                    "permissions": user["permissions"]
                }
                return True
        return False

    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

    def has_permission(self, permission):
        """التحقق من صلاحية معينة"""
        if not self.current_user:
            return False
        return self.current_user["permissions"].get(permission, False)

    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        if not self.current_user:
            return False
        return self.current_user["role"] == "admin"

    def add_user(self, username, password, role, full_name, permissions):
        """إضافة مستخدم جديد"""
        if not self.has_permission("manage_users"):
            return False, "ليس لديك صلاحية إدارة المستخدمين"

        if username in self.users_data:
            return False, "اسم المستخدم موجود بالفعل"

        new_user = {
            "password": self.hash_password(password),
            "role": role,
            "full_name": full_name,
            "permissions": permissions,
            "created_date": datetime.now().isoformat(),
            "last_login": None,
            "active": True
        }

        self.users_data[username] = new_user
        if self.save_users():
            return True, "تم إضافة المستخدم بنجاح"
        return False, "فشل في حفظ بيانات المستخدم"

    def edit_user(self, username, **kwargs):
        """تعديل بيانات مستخدم"""
        if not self.has_permission("manage_users"):
            return False, "ليس لديك صلاحية إدارة المستخدمين"

        if username not in self.users_data:
            return False, "المستخدم غير موجود"

        user = self.users_data[username]

        # تحديث البيانات
        if "password" in kwargs:
            user["password"] = self.hash_password(kwargs["password"])
        if "role" in kwargs:
            user["role"] = kwargs["role"]
        if "full_name" in kwargs:
            user["full_name"] = kwargs["full_name"]
        if "permissions" in kwargs:
            user["permissions"] = kwargs["permissions"]
        if "active" in kwargs:
            user["active"] = kwargs["active"]

        if self.save_users():
            return True, "تم تحديث بيانات المستخدم بنجاح"
        return False, "فشل في حفظ التحديثات"

    def delete_user(self, username):
        """حذف مستخدم"""
        if not self.has_permission("manage_users"):
            return False, "ليس لديك صلاحية إدارة المستخدمين"

        if username == "admin":
            return False, "لا يمكن حذف المستخدم الرئيسي"

        if username not in self.users_data:
            return False, "المستخدم غير موجود"

        del self.users_data[username]
        if self.save_users():
            return True, "تم حذف المستخدم بنجاح"
        return False, "فشل في حذف المستخدم"

    def change_user_password(self, username, old_password, new_password):
        """تغيير كلمة مرور مستخدم"""
        if username not in self.users_data:
            return False, "المستخدم غير موجود"

        user = self.users_data[username]

        # التحقق من كلمة المرور القديمة
        if user["password"] != self.hash_password(old_password):
            return False, "كلمة المرور الحالية غير صحيحة"

        # تغيير كلمة المرور
        user["password"] = self.hash_password(new_password)

        if self.save_users():
            return True, "تم تغيير كلمة المرور بنجاح"
        return False, "فشل في حفظ كلمة المرور الجديدة"

    def admin_change_user_password(self, target_username, new_password):
        """تغيير كلمة مرور مستخدم من قبل المدير"""
        if not self.has_permission("manage_users"):
            return False, "ليس لديك صلاحية إدارة المستخدمين"

        if target_username not in self.users_data:
            return False, "المستخدم غير موجود"

        # تغيير كلمة المرور
        self.users_data[target_username]["password"] = self.hash_password(new_password)

        if self.save_users():
            return True, f"تم تغيير كلمة مرور المستخدم '{target_username}' بنجاح"
        return False, "فشل في حفظ كلمة المرور الجديدة"

    def get_all_users(self):
        """الحصول على قائمة جميع المستخدمين"""
        if not self.has_permission("manage_users"):
            return []

        users_list = []
        for username, user_data in self.users_data.items():
            users_list.append({
                "username": username,
                "full_name": user_data["full_name"],
                "role": user_data["role"],
                "active": user_data.get("active", True),
                "last_login": user_data.get("last_login"),
                "created_date": user_data.get("created_date")
            })
        return users_list

    def get_user_info(self, username=None):
        """الحصول على معلومات مستخدم"""
        if username is None:
            return self.current_user

        if username in self.users_data:
            user = self.users_data[username]
            return {
                "username": username,
                "full_name": user["full_name"],
                "role": user["role"],
                "active": user.get("active", True),
                "last_login": user.get("last_login"),
                "created_date": user.get("created_date"),
                "permissions": user["permissions"]
            }
        return None

class LoginWindow:
    def __init__(self, user_manager, on_success_callback):
        self.user_manager = user_manager
        self.on_success_callback = on_success_callback
        self.root = tk.Tk()  # استخدام Tk بدلاً من Toplevel
        self.root.title("تسجيل الدخول - نظام إدارة المستندات المحاسبية")
        self.root.geometry("500x600")
        self.root.resizable(False, False)

        # جعل النافذة في المقدمة
        self.root.attributes('-topmost', True)
        self.root.focus_force()

        # توسيط النافذة
        self.center_window()

        self.setup_ui()

        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()

        # ربط زر Enter
        self.root.bind('<Return>', lambda e: self.login())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"500x600+{x}+{y}")

    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول المحسنة"""
        # تعيين لون الخلفية
        self.root.configure(bg='#f0f8ff')

        # الإطار الرئيسي مع لون جميل
        main_frame = tk.Frame(self.root, bg='#ffffff', relief='raised', bd=2)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # إطار العنوان مع لون مميز
        title_frame = tk.Frame(main_frame, bg='#4a90e2', height=80)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 20))
        title_frame.pack_propagate(False)

        # العنوان الرئيسي
        title_label = tk.Label(title_frame, text="نظام إدارة المستندات المحاسبية",
                               font=("Arial", 16, "bold"), fg='white', bg='#4a90e2')
        title_label.pack(expand=True)

        # العنوان الفرعي
        subtitle_label = tk.Label(title_frame, text="وزارة الصحة - التأمين الصحي",
                                 font=("Arial", 12), fg='#e6f3ff', bg='#4a90e2')
        subtitle_label.pack()

        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg='#ffffff')
        login_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # عنوان تسجيل الدخول
        login_title = tk.Label(login_frame, text="تسجيل الدخول",
                              font=("Arial", 14, "bold"), fg='#2c3e50', bg='#ffffff')
        login_title.pack(pady=(0, 30))

        # حقول الإدخال
        # اسم المستخدم
        username_label = tk.Label(login_frame, text="اسم المستخدم:",
                                 font=("Arial", 11, "bold"), fg='#34495e', bg='#ffffff')
        username_label.pack(anchor=tk.W, pady=(0, 5))

        self.username_entry = tk.Entry(login_frame, font=("Arial", 12), width=25,
                                      relief='solid', bd=1, bg='#f8f9fa')
        self.username_entry.pack(pady=(0, 15), ipady=5)

        # كلمة المرور
        password_label = tk.Label(login_frame, text="كلمة المرور:",
                                 font=("Arial", 11, "bold"), fg='#34495e', bg='#ffffff')
        password_label.pack(anchor=tk.W, pady=(0, 5))

        self.password_entry = tk.Entry(login_frame, font=("Arial", 12), width=25, show="*",
                                      relief='solid', bd=1, bg='#f8f9fa')
        self.password_entry.pack(pady=(0, 25), ipady=5)

        # أزرار محسنة
        buttons_frame = tk.Frame(login_frame, bg='#ffffff')
        buttons_frame.pack(pady=15)

        # زر تسجيل الدخول
        login_btn = tk.Button(buttons_frame, text="🔑 تسجيل دخول",
                             command=self.login, font=("Arial", 11, "bold"),
                             bg='#27ae60', fg='white', relief='flat',
                             padx=20, pady=8, cursor='hand2')
        login_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر الخروج
        exit_btn = tk.Button(buttons_frame, text="❌ خروج",
                            command=self.cancel, font=("Arial", 11, "bold"),
                            bg='#e74c3c', fg='white', relief='flat',
                            padx=20, pady=8, cursor='hand2')
        exit_btn.pack(side=tk.LEFT)

        # معلومات أمان عامة
        info_frame = tk.LabelFrame(login_frame, text="🔒 معلومات مهمة",
                                  font=("Arial", 10, "bold"), fg='#2c3e50', bg='#ffffff',
                                  relief='groove', bd=2)
        info_frame.pack(fill=tk.X, pady=(25, 0))

        info_content = tk.Frame(info_frame, bg='#f8f9fa')
        info_content.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(info_content, text="• استخدم بيانات تسجيل الدخول المعطاة لك",
                font=("Arial", 9), fg='#7f8c8d', bg='#f8f9fa').pack(anchor=tk.W, pady=2)
        tk.Label(info_content, text="• للحصول على بيانات تسجيل الدخول اتصل بالمدير",
                font=("Arial", 9), fg='#7f8c8d', bg='#f8f9fa').pack(anchor=tk.W, pady=2)
        tk.Label(info_content, text="• يمكن تغيير كلمة المرور بعد تسجيل الدخول",
                font=("Arial", 9), fg='#7f8c8d', bg='#f8f9fa').pack(anchor=tk.W, pady=2)

        # تأثيرات بصرية للأزرار
        self.add_button_effects(login_btn, '#27ae60', '#2ecc71')
        self.add_button_effects(exit_btn, '#e74c3c', '#c0392b')

    def add_button_effects(self, button, normal_color, hover_color):
        """إضافة تأثيرات بصرية للأزرار"""
        def on_enter(e):
            button.config(bg=hover_color)

        def on_leave(e):
            button.config(bg=normal_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        if self.user_manager.authenticate(username, password):
            # حفظ مرجع للدالة قبل إغلاق النافذة
            callback = self.on_success_callback
            # إغلاق نافذة تسجيل الدخول
            self.root.quit()  # إيقاف حلقة الأحداث
            # استدعاء الدالة
            callback()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)
            self.username_entry.focus()

    def cancel(self):
        """إلغاء تسجيل الدخول"""
        self.root.quit()
        self.root.destroy()
        import sys
        sys.exit()

class UserManagementWindow:
    def __init__(self, parent, user_manager):
        self.parent = parent
        self.user_manager = user_manager
        self.root = tk.Toplevel(parent)
        self.root.title("إدارة المستخدمين")
        self.root.geometry("800x700")  # زيادة الارتفاع لإظهار الأزرار بوضوح

        self.setup_ui()
        self.load_users()

    def setup_ui(self):
        """إعداد واجهة إدارة المستخدمين"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title_label = ttk.Label(main_frame, text="إدارة المستخدمين",
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # جدول المستخدمين
        columns = ('اسم المستخدم', 'الاسم الكامل', 'الدور', 'الحالة', 'آخر دخول')
        self.users_tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="إضافة مستخدم",
                  command=self.add_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="تعديل مستخدم",
                  command=self.edit_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="حذف مستخدم",
                  command=self.delete_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="تحديث",
                  command=self.load_users).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="إغلاق",
                  command=self.root.destroy).pack(side=tk.RIGHT)

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        # مسح الجدول
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)

        # تحميل المستخدمين
        users = self.user_manager.get_all_users()
        for user in users:
            status = "نشط" if user['active'] else "معطل"
            last_login = user['last_login']
            if last_login:
                last_login = datetime.fromisoformat(last_login).strftime("%Y-%m-%d %H:%M")
            else:
                last_login = "لم يسجل دخول"

            self.users_tree.insert('', tk.END, values=(
                user['username'],
                user['full_name'],
                user['role'],
                status,
                last_login
            ))

    def add_user(self):
        """إضافة مستخدم جديد"""
        AddUserWindow(self.root, self.user_manager, self.load_users)

    def edit_user(self):
        """تعديل مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "يرجى اختيار مستخدم للتعديل")
            return

        item = selection[0]
        username = self.users_tree.item(item)['values'][0]
        EditUserWindow(self.root, self.user_manager, username, self.load_users)

    def delete_user(self):
        """حذف مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "يرجى اختيار مستخدم للحذف")
            return

        item = selection[0]
        username = self.users_tree.item(item)['values'][0]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستخدم '{username}'؟"):
            success, message = self.user_manager.delete_user(username)
            if success:
                messagebox.showinfo("نجح", message)
                self.load_users()
            else:
                messagebox.showerror("خطأ", message)

class AddUserWindow:
    def __init__(self, parent, user_manager, refresh_callback):
        self.parent = parent
        self.user_manager = user_manager
        self.refresh_callback = refresh_callback
        self.root = tk.Toplevel(parent)
        self.root.title("إضافة مستخدم جديد")
        self.root.geometry("500x600")

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة إضافة المستخدم"""
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        ttk.Label(main_frame, text="إضافة مستخدم جديد",
                 font=("Arial", 14, "bold")).pack(pady=(0, 20))

        # الحقول الأساسية
        ttk.Label(main_frame, text="اسم المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        self.username_entry = ttk.Entry(main_frame, width=30)
        self.username_entry.pack(pady=(0, 10))

        ttk.Label(main_frame, text="كلمة المرور:").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(main_frame, width=30, show="*")
        self.password_entry.pack(pady=(0, 10))

        ttk.Label(main_frame, text="الاسم الكامل:").pack(anchor=tk.W, pady=(0, 5))
        self.fullname_entry = ttk.Entry(main_frame, width=30)
        self.fullname_entry.pack(pady=(0, 10))

        ttk.Label(main_frame, text="الدور:").pack(anchor=tk.W, pady=(0, 5))
        self.role_combo = ttk.Combobox(main_frame, width=27, state='readonly')
        self.role_combo['values'] = ['admin', 'manager', 'user', 'viewer']
        self.role_combo.set('user')
        self.role_combo.pack(pady=(0, 15))

        # الصلاحيات
        permissions_frame = ttk.LabelFrame(main_frame, text="الصلاحيات", padding="10")
        permissions_frame.pack(fill=tk.X, pady=(0, 15))

        self.permissions = {}
        permissions_list = [
            ('add_account', 'إضافة حساب'),
            ('edit_account', 'تعديل حساب'),
            ('delete_account', 'حذف حساب'),
            ('add_document', 'إضافة مستند'),
            ('edit_document', 'تعديل مستند'),
            ('delete_document', 'حذف مستند'),
            ('view_reports', 'عرض التقارير'),
            ('manage_users', 'إدارة المستخدمين'),
            ('backup_restore', 'النسخ الاحتياطي')
        ]

        for perm_key, perm_name in permissions_list:
            var = tk.BooleanVar()
            ttk.Checkbutton(permissions_frame, text=perm_name, variable=var).pack(anchor=tk.W)
            self.permissions[perm_key] = var

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="إضافة", command=self.add_user).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.root.destroy).pack(side=tk.LEFT)

    def add_user(self):
        """إضافة المستخدم"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        fullname = self.fullname_entry.get().strip()
        role = self.role_combo.get()

        if not all([username, password, fullname, role]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        # جمع الصلاحيات
        permissions = {}
        for perm_key, var in self.permissions.items():
            permissions[perm_key] = var.get()

        success, message = self.user_manager.add_user(username, password, role, fullname, permissions)

        if success:
            messagebox.showinfo("نجح", message)
            self.refresh_callback()
            self.root.destroy()
        else:
            messagebox.showerror("خطأ", message)

class EditUserWindow:
    def __init__(self, parent, user_manager, username, refresh_callback):
        self.parent = parent
        self.user_manager = user_manager
        self.username = username
        self.refresh_callback = refresh_callback
        self.root = tk.Toplevel(parent)
        self.root.title(f"تعديل المستخدم: {username}")
        self.root.geometry("500x600")

        self.user_info = self.user_manager.get_user_info(username)
        self.setup_ui()
        self.load_user_data()

    def setup_ui(self):
        """إعداد واجهة تعديل المستخدم"""
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        ttk.Label(main_frame, text=f"تعديل المستخدم: {self.username}",
                 font=("Arial", 14, "bold")).pack(pady=(0, 20))

        # الحقول الأساسية
        ttk.Label(main_frame, text="كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية):").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(main_frame, width=30, show="*")
        self.password_entry.pack(pady=(0, 10))

        ttk.Label(main_frame, text="الاسم الكامل:").pack(anchor=tk.W, pady=(0, 5))
        self.fullname_entry = ttk.Entry(main_frame, width=30)
        self.fullname_entry.pack(pady=(0, 10))

        ttk.Label(main_frame, text="الدور:").pack(anchor=tk.W, pady=(0, 5))
        self.role_combo = ttk.Combobox(main_frame, width=27, state='readonly')
        self.role_combo['values'] = ['admin', 'manager', 'user', 'viewer']
        self.role_combo.pack(pady=(0, 10))

        # حالة المستخدم
        self.active_var = tk.BooleanVar()
        ttk.Checkbutton(main_frame, text="المستخدم نشط", variable=self.active_var).pack(anchor=tk.W, pady=(0, 15))

        # الصلاحيات
        permissions_frame = ttk.LabelFrame(main_frame, text="الصلاحيات", padding="10")
        permissions_frame.pack(fill=tk.X, pady=(0, 15))

        self.permissions = {}
        permissions_list = [
            ('add_account', 'إضافة حساب'),
            ('edit_account', 'تعديل حساب'),
            ('delete_account', 'حذف حساب'),
            ('add_document', 'إضافة مستند'),
            ('edit_document', 'تعديل مستند'),
            ('delete_document', 'حذف مستند'),
            ('view_reports', 'عرض التقارير'),
            ('manage_users', 'إدارة المستخدمين'),
            ('backup_restore', 'النسخ الاحتياطي')
        ]

        for perm_key, perm_name in permissions_list:
            var = tk.BooleanVar()
            ttk.Checkbutton(permissions_frame, text=perm_name, variable=var).pack(anchor=tk.W)
            self.permissions[perm_key] = var

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="حفظ", command=self.save_user).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.root.destroy).pack(side=tk.LEFT)

    def load_user_data(self):
        """تحميل بيانات المستخدم"""
        if self.user_info:
            self.fullname_entry.insert(0, self.user_info['full_name'])
            self.role_combo.set(self.user_info['role'])
            self.active_var.set(self.user_info['active'])

            # تحميل الصلاحيات
            for perm_key, var in self.permissions.items():
                var.set(self.user_info['permissions'].get(perm_key, False))

    def save_user(self):
        """حفظ تعديلات المستخدم"""
        fullname = self.fullname_entry.get().strip()
        role = self.role_combo.get()
        password = self.password_entry.get()

        if not fullname or not role:
            messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
            return

        # جمع البيانات المحدثة
        update_data = {
            'full_name': fullname,
            'role': role,
            'active': self.active_var.get(),
            'permissions': {}
        }

        # إضافة كلمة المرور إذا تم إدخالها
        if password:
            update_data['password'] = password

        # جمع الصلاحيات
        for perm_key, var in self.permissions.items():
            update_data['permissions'][perm_key] = var.get()

        success, message = self.user_manager.edit_user(self.username, **update_data)

        if success:
            messagebox.showinfo("نجح", message)
            self.refresh_callback()
            self.root.destroy()
        else:
            messagebox.showerror("خطأ", message)

class ChangePasswordWindow:
    def __init__(self, parent, user_manager, target_username=None):
        self.parent = parent
        self.user_manager = user_manager
        self.target_username = target_username  # إذا كان المدير يغير لمستخدم آخر
        self.root = tk.Toplevel(parent)

        if target_username:
            self.root.title(f"تغيير كلمة مرور المستخدم: {target_username}")
        else:
            self.root.title("تغيير كلمة المرور")

        self.root.geometry("450x500")
        self.root.resizable(False, False)

        # جعل النافذة في المقدمة
        self.root.transient(parent)
        self.root.grab_set()

        # توسيط النافذة
        self.center_window()

        self.setup_ui()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"450x500+{x}+{y}")

    def setup_ui(self):
        """إعداد واجهة تغيير كلمة المرور"""
        # تعيين لون الخلفية
        self.root.configure(bg='#f0f8ff')

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#ffffff', relief='raised', bd=2)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # إطار العنوان
        title_frame = tk.Frame(main_frame, bg='#3498db', height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 20))
        title_frame.pack_propagate(False)

        # العنوان
        if self.target_username:
            title_text = f"🔒 تغيير كلمة مرور المستخدم\n{self.target_username}"
        else:
            title_text = "🔒 تغيير كلمة المرور"

        title_label = tk.Label(title_frame, text=title_text,
                              font=("Arial", 14, "bold"), fg='white', bg='#3498db')
        title_label.pack(expand=True)

        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg='#ffffff')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إذا لم يكن المدير يغير لمستخدم آخر
        if not self.target_username:
            # كلمة المرور الحالية
            tk.Label(content_frame, text="كلمة المرور الحالية:",
                    font=("Arial", 11, "bold"), fg='#2c3e50', bg='#ffffff').pack(anchor=tk.W, pady=(10, 5))

            self.current_password_entry = tk.Entry(content_frame, font=("Arial", 12), width=30, show="*",
                                                  relief='solid', bd=1, bg='#f8f9fa')
            self.current_password_entry.pack(pady=(0, 15), ipady=5)

        # كلمة المرور الجديدة
        tk.Label(content_frame, text="كلمة المرور الجديدة:",
                font=("Arial", 11, "bold"), fg='#2c3e50', bg='#ffffff').pack(anchor=tk.W, pady=(0, 5))

        self.new_password_entry = tk.Entry(content_frame, font=("Arial", 12), width=30, show="*",
                                          relief='solid', bd=1, bg='#f8f9fa')
        self.new_password_entry.pack(pady=(0, 15), ipady=5)

        # تأكيد كلمة المرور
        tk.Label(content_frame, text="تأكيد كلمة المرور:",
                font=("Arial", 11, "bold"), fg='#2c3e50', bg='#ffffff').pack(anchor=tk.W, pady=(0, 5))

        self.confirm_password_entry = tk.Entry(content_frame, font=("Arial", 12), width=30, show="*",
                                              relief='solid', bd=1, bg='#f8f9fa')
        self.confirm_password_entry.pack(pady=(0, 25), ipady=5)

        # معلومات إرشادية
        info_frame = tk.LabelFrame(content_frame, text="📝 معلومات مهمة",
                                  font=("Arial", 10, "bold"), fg='#2c3e50', bg='#ffffff',
                                  relief='groove', bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        info_content = tk.Frame(info_frame, bg='#f8f9fa')
        info_content.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(info_content, text="• يجب أن تكون كلمة المرور 6 أحرف على الأقل",
                font=("Arial", 9), fg='#7f8c8d', bg='#f8f9fa').pack(anchor=tk.W, pady=1)
        tk.Label(info_content, text="• استخدم مزيج من الأحرف والأرقام والرموز",
                font=("Arial", 9), fg='#7f8c8d', bg='#f8f9fa').pack(anchor=tk.W, pady=1)
        tk.Label(info_content, text="• تجنب استخدام معلومات شخصية واضحة",
                font=("Arial", 9), fg='#7f8c8d', bg='#f8f9fa').pack(anchor=tk.W, pady=1)

        # أزرار
        buttons_frame = tk.Frame(content_frame, bg='#ffffff')
        buttons_frame.pack(pady=15)

        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="💾 حفظ التغيير",
                            command=self.change_password, font=("Arial", 11, "bold"),
                            bg='#27ae60', fg='white', relief='flat',
                            padx=20, pady=8, cursor='hand2')
        save_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                              command=self.root.destroy, font=("Arial", 11, "bold"),
                              bg='#e74c3c', fg='white', relief='flat',
                              padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side=tk.LEFT)

        # تأثيرات بصرية
        self.add_button_effects(save_btn, '#27ae60', '#2ecc71')
        self.add_button_effects(cancel_btn, '#e74c3c', '#c0392b')

        # ربط زر Enter
        self.root.bind('<Return>', lambda e: self.change_password())

        # التركيز على أول حقل
        if hasattr(self, 'current_password_entry'):
            self.current_password_entry.focus()
        else:
            self.new_password_entry.focus()

    def add_button_effects(self, button, normal_color, hover_color):
        """إضافة تأثيرات بصرية للأزرار"""
        def on_enter(e):
            button.config(bg=hover_color)

        def on_leave(e):
            button.config(bg=normal_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def change_password(self):
        """تغيير كلمة المرور"""
        new_password = self.new_password_entry.get()
        confirm_password = self.confirm_password_entry.get()

        # فحص الحقول
        if not new_password or not confirm_password:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        # فحص تطابق كلمة المرور
        if new_password != confirm_password:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return

        # فحص طول كلمة المرور
        if len(new_password) < 6:
            messagebox.showerror("خطأ", "يجب أن تكون كلمة المرور 6 أحرف على الأقل")
            return

        # تغيير كلمة المرور
        if self.target_username:
            # المدير يغير لمستخدم آخر
            success, message = self.user_manager.admin_change_user_password(self.target_username, new_password)
        else:
            # المستخدم يغير كلمة مروره
            current_password = self.current_password_entry.get()
            if not current_password:
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور الحالية")
                return

            current_user = self.user_manager.get_user_info()
            if current_user:
                success, message = self.user_manager.change_user_password(
                    current_user['username'], current_password, new_password)
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات المستخدم")
                return

        if success:
            messagebox.showinfo("نجح", message)
            self.root.destroy()
        else:
            messagebox.showerror("خطأ", message)

class AddUserWindow:
    def __init__(self, parent, user_manager):
        self.parent = parent
        self.user_manager = user_manager
        self.root = tk.Toplevel(parent)
        self.root.title("➕ إضافة مستخدم جديد")
        self.root.geometry("500x650")
        self.root.resizable(False, False)

        # جعل النافذة في المقدمة
        self.root.transient(parent)
        self.root.grab_set()

        # توسيط النافذة
        self.center_window()

        self.setup_ui()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.root.winfo_screenheight() // 2) - (650 // 2)
        self.root.geometry(f"500x650+{x}+{y}")

    def setup_ui(self):
        """إعداد واجهة إضافة مستخدم"""
        # تعيين لون الخلفية
        self.root.configure(bg='#f0f8ff')

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#ffffff', relief='raised', bd=2)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # إطار العنوان
        title_frame = tk.Frame(main_frame, bg='#2ecc71', height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 20))
        title_frame.pack_propagate(False)

        # العنوان
        title_label = tk.Label(title_frame, text="➕ إضافة مستخدم جديد",
                              font=("Arial", 14, "bold"), fg='white', bg='#2ecc71')
        title_label.pack(expand=True)

        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg='#ffffff')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # اسم المستخدم
        tk.Label(content_frame, text="اسم المستخدم:",
                font=("Arial", 11, "bold"), fg='#2c3e50', bg='#ffffff').pack(anchor=tk.W, pady=(10, 5))

        self.username_entry = tk.Entry(content_frame, font=("Arial", 12), width=30,
                                      relief='solid', bd=1, bg='#f8f9fa')
        self.username_entry.pack(pady=(0, 15), ipady=5)

        # كلمة المرور
        tk.Label(content_frame, text="كلمة المرور:",
                font=("Arial", 11, "bold"), fg='#2c3e50', bg='#ffffff').pack(anchor=tk.W, pady=(0, 5))

        self.password_entry = tk.Entry(content_frame, font=("Arial", 12), width=30, show="*",
                                      relief='solid', bd=1, bg='#f8f9fa')
        self.password_entry.pack(pady=(0, 15), ipady=5)

        # الاسم الكامل
        tk.Label(content_frame, text="الاسم الكامل:",
                font=("Arial", 11, "bold"), fg='#2c3e50', bg='#ffffff').pack(anchor=tk.W, pady=(0, 5))

        self.fullname_entry = tk.Entry(content_frame, font=("Arial", 12), width=30,
                                      relief='solid', bd=1, bg='#f8f9fa')
        self.fullname_entry.pack(pady=(0, 15), ipady=5)

        # الدور
        tk.Label(content_frame, text="دور المستخدم:",
                font=("Arial", 11, "bold"), fg='#2c3e50', bg='#ffffff').pack(anchor=tk.W, pady=(0, 5))

        self.role_var = tk.StringVar(value="user")
        role_frame = tk.Frame(content_frame, bg='#ffffff')
        role_frame.pack(anchor=tk.W, pady=(0, 15))

        roles = [
            ("admin", "مدير عام (جميع الصلاحيات)"),
            ("manager", "مدير (معظم الصلاحيات)"),
            ("user", "مستخدم عادي (صلاحيات محدودة)"),
            ("viewer", "مشاهد فقط (عرض التقارير فقط)")
        ]

        for role_value, role_text in roles:
            tk.Radiobutton(role_frame, text=role_text, variable=self.role_var, value=role_value,
                          font=("Arial", 10), bg='#ffffff', fg='#2c3e50').pack(anchor=tk.W, pady=2)

        # الصلاحيات المخصصة
        permissions_frame = tk.LabelFrame(content_frame, text="🔒 الصلاحيات المخصصة",
                                         font=("Arial", 10, "bold"), fg='#2c3e50', bg='#ffffff',
                                         relief='groove', bd=2)
        permissions_frame.pack(fill=tk.X, pady=(0, 20))

        perm_content = tk.Frame(permissions_frame, bg='#f8f9fa')
        perm_content.pack(fill=tk.X, padx=10, pady=10)

        self.permissions = {}
        permissions_list = [
            ('add_account', '➕ إضافة حساب'),
            ('edit_account', '✏️ تعديل حساب'),
            ('delete_account', '❌ حذف حساب'),
            ('add_document', '📝 إضافة مستند'),
            ('edit_document', '✏️ تعديل مستند'),
            ('delete_document', '❌ حذف مستند'),
            ('view_reports', '📊 عرض التقارير'),
            ('manage_users', '👥 إدارة المستخدمين'),
            ('backup_restore', '💾 النسخ الاحتياطي')
        ]

        # إنشاء عمودين للصلاحيات
        left_frame = tk.Frame(perm_content, bg='#f8f9fa')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        right_frame = tk.Frame(perm_content, bg='#f8f9fa')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        for i, (perm_key, perm_name) in enumerate(permissions_list):
            var = tk.BooleanVar()
            frame = left_frame if i < len(permissions_list) // 2 else right_frame
            tk.Checkbutton(frame, text=perm_name, variable=var,
                          font=("Arial", 9), bg='#f8f9fa', fg='#2c3e50').pack(anchor=tk.W, pady=2)
            self.permissions[perm_key] = var

        # أزرار
        buttons_frame = tk.Frame(content_frame, bg='#ffffff')
        buttons_frame.pack(pady=15)

        # زر الإضافة
        add_btn = tk.Button(buttons_frame, text="➕ إضافة المستخدم",
                           command=self.add_user, font=("Arial", 11, "bold"),
                           bg='#27ae60', fg='white', relief='flat',
                           padx=20, pady=8, cursor='hand2')
        add_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                              command=self.root.destroy, font=("Arial", 11, "bold"),
                              bg='#e74c3c', fg='white', relief='flat',
                              padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side=tk.LEFT)

        # تأثيرات بصرية
        self.add_button_effects(add_btn, '#27ae60', '#2ecc71')
        self.add_button_effects(cancel_btn, '#e74c3c', '#c0392b')

        # ربط زر Enter
        self.root.bind('<Return>', lambda e: self.add_user())

        # التركيز على أول حقل
        self.username_entry.focus()

    def add_button_effects(self, button, normal_color, hover_color):
        """إضافة تأثيرات بصرية للأزرار"""
        def on_enter(e):
            button.config(bg=hover_color)

        def on_leave(e):
            button.config(bg=normal_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def add_user(self):
        """إضافة المستخدم"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        fullname = self.fullname_entry.get().strip()
        role = self.role_var.get()

        # فحص الحقول
        if not username or not password or not fullname:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        # فحص طول كلمة المرور
        if len(password) < 4:
            messagebox.showerror("خطأ", "يجب أن تكون كلمة المرور 4 أحرف على الأقل")
            return

        # جمع الصلاحيات
        permissions = {}
        for perm_key, var in self.permissions.items():
            permissions[perm_key] = var.get()

        # إضافة المستخدم
        success, message = self.user_manager.add_user(username, password, role, fullname, permissions)

        if success:
            messagebox.showinfo("نجح", message)
            self.root.destroy()
        else:
            messagebox.showerror("خطأ", message)