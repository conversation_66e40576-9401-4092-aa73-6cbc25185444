#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ملف التشغيل والتأكد من صحته
"""

import sys
import os
import subprocess
import importlib.util

def test_python_version():
    """اختبار إصدار Python"""
    print("🔍 فحص إصدار Python...")
    
    version = sys.version_info
    print(f"   الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("✅ إصدار Python مدعوم")
        return True
    else:
        print("❌ إصدار Python غير مدعوم (يتطلب 3.7+)")
        return False

def test_required_modules():
    """اختبار المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")
    
    modules = {
        'tkinter': 'واجهة المستخدم الرسومية',
        'openpyxl': 'مكتبة Excel',
        'os': 'نظام التشغيل',
        'sys': 'نظام Python',
        'datetime': 'التاريخ والوقت',
        'traceback': 'تتبع الأخطاء'
    }
    
    missing_modules = []
    
    for module, description in modules.items():
        try:
            if module == 'tkinter':
                import tkinter
                print(f"✅ {module} - {description}")
            elif module == 'openpyxl':
                import openpyxl
                print(f"✅ {module} {openpyxl.__version__} - {description}")
            else:
                __import__(module)
                print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description} - غير متوفر")
            missing_modules.append(module)
    
    return len(missing_modules) == 0, missing_modules

def test_project_files():
    """اختبار ملفات المشروع"""
    print("\n🔍 فحص ملفات المشروع...")
    
    required_files = {
        'launcher.py': 'ملف التشغيل الرئيسي',
        'app.py': 'التطبيق الرئيسي',
        'excel_manager.py': 'مدير Excel',
        'document_window.py': 'نافذة المستندات',
        'search_window.py': 'نافذة البحث',
        'manage_accounts.py': 'إدارة الحسابات'
    }
    
    missing_files = []
    
    for file, description in required_files.items():
        if os.path.exists(file):
            print(f"✅ {file} - {description}")
        else:
            print(f"❌ {file} - {description} - مفقود")
            missing_files.append(file)
    
    return len(missing_files) == 0, missing_files

def test_launcher_syntax():
    """اختبار صحة بناء ملف launcher.py"""
    print("\n🔍 فحص صحة بناء launcher.py...")
    
    try:
        # فحص صحة البناء
        spec = importlib.util.spec_from_file_location("launcher", "launcher.py")
        if spec is None:
            print("❌ لا يمكن تحميل launcher.py")
            return False
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # فحص وجود الدوال المطلوبة
        if hasattr(module, 'main'):
            print("✅ دالة main موجودة")
        else:
            print("❌ دالة main مفقودة")
            return False
        
        if hasattr(module, 'check_requirements'):
            print("✅ دالة check_requirements موجودة")
        else:
            print("⚠️ دالة check_requirements مفقودة")
        
        print("✅ launcher.py صحيح نحوياً")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ نحوي في launcher.py: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ في launcher.py: {str(e)}")
        return False

def test_app_imports():
    """اختبار استيراد التطبيق"""
    print("\n🔍 فحص استيراد التطبيق...")
    
    try:
        # اختبار استيراد app.py
        spec = importlib.util.spec_from_file_location("app", "app.py")
        if spec is None:
            print("❌ لا يمكن تحميل app.py")
            return False
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # فحص وجود الكلاس الرئيسي
        if hasattr(module, 'AccountingApp'):
            print("✅ كلاس AccountingApp موجود")
        else:
            print("❌ كلاس AccountingApp مفقود")
            return False
        
        print("✅ app.py يمكن استيراده بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد app.py: {str(e)}")
        return False

def test_tkinter_gui():
    """اختبار واجهة tkinter"""
    print("\n🔍 فحص واجهة tkinter...")
    
    try:
        import tkinter as tk
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # اختبار إنشاء عناصر أساسية
        label = tk.Label(root, text="اختبار")
        button = tk.Button(root, text="زر")
        
        # تدمير النافذة
        root.destroy()
        
        print("✅ tkinter يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في tkinter: {str(e)}")
        return False

def test_excel_functionality():
    """اختبار وظائف Excel"""
    print("\n🔍 فحص وظائف Excel...")
    
    try:
        import openpyxl
        
        # إنشاء workbook اختبار
        wb = openpyxl.Workbook()
        ws = wb.active
        ws['A1'] = "اختبار"
        
        # حفظ في ملف مؤقت
        test_file = "test_excel.xlsx"
        wb.save(test_file)
        
        # تحميل الملف
        wb2 = openpyxl.load_workbook(test_file)
        value = wb2.active['A1'].value
        
        # حذف الملف المؤقت
        if os.path.exists(test_file):
            os.remove(test_file)
        
        if value == "اختبار":
            print("✅ openpyxl يعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في حفظ/تحميل Excel")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في openpyxl: {str(e)}")
        return False

def test_launcher_execution():
    """اختبار تشغيل launcher (dry run)"""
    print("\n🔍 اختبار تشغيل launcher...")
    
    try:
        # استيراد launcher
        import launcher
        
        # فحص وجود الدوال
        if hasattr(launcher, 'check_requirements'):
            print("✅ دالة check_requirements متوفرة")
            
            # تشغيل فحص المتطلبات
            result = launcher.check_requirements()
            if result:
                print("✅ فحص المتطلبات نجح")
            else:
                print("❌ فحص المتطلبات فشل")
                return False
        
        print("✅ launcher جاهز للتشغيل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار launcher: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل لملف التشغيل")
    print("=" * 60)
    
    tests = [
        ("إصدار Python", test_python_version),
        ("المكتبات المطلوبة", test_required_modules),
        ("ملفات المشروع", test_project_files),
        ("بناء launcher.py", test_launcher_syntax),
        ("استيراد التطبيق", test_app_imports),
        ("واجهة tkinter", test_tkinter_gui),
        ("وظائف Excel", test_excel_functionality),
        ("تشغيل launcher", test_launcher_execution)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📍 {test_name}:")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ ملف التشغيل جاهز للاستخدام")
        return True
    else:
        print("❌ بعض الاختبارات فشلت")
        print("🔧 يرجى إصلاح المشاكل أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
