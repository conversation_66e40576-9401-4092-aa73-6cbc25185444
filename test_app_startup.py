#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بدء تشغيل التطبيق بعد الإصلاحات
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def test_app_import():
    """اختبار استيراد التطبيق"""
    try:
        print("🔍 اختبار استيراد التطبيق...")
        
        # محاولة استيراد التطبيق
        sys.path.append('.')
        from app import AccountingApp
        
        print("✅ تم استيراد التطبيق بنجاح")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام في الاستيراد: {str(e)}")
        return False

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    try:
        print("\n🔧 اختبار إنشاء التطبيق...")
        
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # استيراد التطبيق
        from app import AccountingApp
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # فحص وجود الدوال المطلوبة
        required_methods = [
            'create_action_buttons',
            'setup_modern_ui',
            'show_account_balances_window',
            'show_summary_report'
        ]
        
        missing_methods = []
        for method in required_methods:
            if hasattr(app, method):
                print(f"✅ الدالة موجودة: {method}")
            else:
                print(f"❌ الدالة مفقودة: {method}")
                missing_methods.append(method)
        
        # إغلاق النافذة
        root.destroy()
        
        if len(missing_methods) == 0:
            print("✅ جميع الدوال المطلوبة موجودة")
            return True
        else:
            print(f"❌ مفقود: {len(missing_methods)} دالة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_buttons_config():
    """اختبار تكوين الأزرار"""
    try:
        print("\n📋 اختبار تكوين الأزرار...")
        
        # قراءة ملف app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص وجود الأزرار المطلوبة
        expected_buttons = [
            "📁 إضافة حساب جديد",
            "📝 إضافة مستند",
            "⚙️ إدارة الحسابات",
            "💰 إضافة حساب مقبوضات",
            "📄 إضافة مستند مقبوضات",
            "⚙️ إدارة حسابات المقبوضات",
            "🔍 بحث في الحسابات",
            "📊 نافذة تقارير الأرصدة",
            "👥 إدارة المستخدمين",
            "🚪 خروج من النظام"
        ]
        
        missing_buttons = []
        for button in expected_buttons:
            if button in content:
                print(f"✅ الزر موجود: {button}")
            else:
                print(f"❌ الزر مفقود: {button}")
                missing_buttons.append(button)
        
        # فحص عدم وجود الدالة المحذوفة
        if "def create_summary_report(self):" in content:
            print("❌ الدالة المحذوفة ما زالت موجودة")
            return False
        else:
            print("✅ تم حذف الدالة غير المستخدمة")
        
        # فحص استدعاء الدالة الصحيحة في القائمة
        if "command=self.show_summary_report" in content:
            print("✅ القائمة تستدعي الدالة الصحيحة")
        else:
            print("❌ القائمة لا تستدعي الدالة الصحيحة")
            return False
        
        if len(missing_buttons) == 0:
            print("✅ جميع الأزرار موجودة")
            return True
        else:
            print(f"❌ مفقود: {len(missing_buttons)} زر")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص التكوين: {str(e)}")
        return False

def create_startup_script():
    """إنشاء سكريبت بدء تشغيل محسن"""
    try:
        print("\n📝 إنشاء سكريبت بدء تشغيل محسن...")
        
        script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بدء تشغيل محسن للتطبيق
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """تشغيل التطبيق مع معالجة الأخطاء"""
    try:
        print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية...")
        
        # التحقق من المتطلبات
        try:
            import openpyxl
            print("✅ مكتبة openpyxl متاحة")
        except ImportError:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\\nيرجى تثبيتها: pip install openpyxl")
            return
        
        # استيراد التطبيق
        try:
            from app import AccountingApp
            print("✅ تم استيراد التطبيق")
        except ImportError as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد التطبيق: {str(e)}")
            return
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")
        
        print("✅ تم إعداد النافذة الرئيسية")
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        print("🎉 النظام جاهز للاستخدام!")
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل النظام: {str(e)}"
        print(f"❌ {error_msg}")
        messagebox.showerror("خطأ في التشغيل", error_msg)
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''
        
        with open('run_app_fixed.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("✅ تم إنشاء run_app_fixed.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء السكريبت: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار بدء تشغيل التطبيق بعد الإصلاحات")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # اختبار الاستيراد
    if test_app_import():
        success_count += 1
    
    # اختبار إنشاء التطبيق
    if test_app_creation():
        success_count += 1
    
    # اختبار تكوين الأزرار
    if test_buttons_config():
        success_count += 1
    
    # إنشاء سكريبت التشغيل
    if create_startup_script():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات بدء التشغيل!")
        print("\n📝 الإصلاحات المطبقة:")
        print("✅ إصلاح خطأ create_summary_report المفقودة")
        print("✅ تحديث القائمة لتستخدم show_summary_report")
        print("✅ التأكد من وجود جميع الدوال المطلوبة")
        print("✅ إنشاء سكريبت تشغيل محسن")
        
        print("\n🚀 لتشغيل التطبيق:")
        print("python run_app_fixed.py")
        print("أو")
        print("python app.py")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات بدء التشغيل")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
