import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/auth_provider.dart';
import '../providers/accounting_provider.dart';
import '../utils/app_theme.dart';
import 'accounts_screen.dart';
import 'add_account_screen.dart';
import 'add_document_screen.dart';
import 'search_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  
  final List<Widget> _screens = [
    const AccountsScreen(),
    const AddDocumentScreen(),
    const AddAccountScreen(),
    const SearchScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند بدء التطبيق
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AccountingProvider>(context, listen: false).loadAccounts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام إدارة المستندات المحاسبية'),
        actions: [
          // معلومات المستخدم
          Consumer<AuthProvider>(
            builder: (context, authProvider, _) {
              return PopupMenuButton<String>(
                icon: const Icon(FontAwesomeIcons.userCircle),
                onSelected: (value) {
                  if (value == 'logout') {
                    _logout();
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    enabled: false,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مرحباً',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        Text(
                          authProvider.username,
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(FontAwesomeIcons.signOutAlt, size: 16),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج'),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      
      body: IndexedStack(
        index: _selectedIndex,
        children: _screens,
      ),
      
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: AppTheme.subtitleColor,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.list),
            label: 'الحسابات',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.plus),
            label: 'إضافة مستند',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.userPlus),
            label: 'إضافة حساب',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.search),
            label: 'البحث',
          ),
        ],
      ),
      
      // زر عائم للإجراءات السريعة
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActions();
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(FontAwesomeIcons.plus, color: Colors.white),
      ),
    );
  }

  void _logout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    // إظهار تأكيد تسجيل الخروج
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (shouldLogout == true) {
      await authProvider.logout();
    }
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'الإجراءات السريعة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            
            const SizedBox(height: 16),
            
            // قائمة الإجراءات
            ListTile(
              leading: const Icon(FontAwesomeIcons.plus, color: AppTheme.primaryColor),
              title: const Text('إضافة مستند جديد'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _selectedIndex = 1;
                });
              },
            ),
            
            ListTile(
              leading: const Icon(FontAwesomeIcons.userPlus, color: AppTheme.accentColor),
              title: const Text('إضافة حساب جديد'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _selectedIndex = 2;
                });
              },
            ),
            
            ListTile(
              leading: const Icon(FontAwesomeIcons.search, color: AppTheme.secondaryColor),
              title: const Text('البحث في المستندات'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _selectedIndex = 3;
                });
              },
            ),
            
            ListTile(
              leading: const Icon(FontAwesomeIcons.sync, color: AppTheme.warningColor),
              title: const Text('تحديث البيانات'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<AccountingProvider>(context, listen: false).loadAccounts();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديث البيانات'),
                    backgroundColor: AppTheme.accentColor,
                  ),
                );
              },
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
