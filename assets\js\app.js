// التطبيق الرئيسي لنظام إدارة المستندات المحاسبية

// متغيرات البيانات
let accounts = [];
let documents = [];
let currentAccountId = 1;
let currentDocumentId = 1;

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    updateDashboard();
});

// تحميل البيانات من التخزين المحلي
function loadData() {
    try {
        const savedAccounts = localStorage.getItem('accounts');
        const savedDocuments = localStorage.getItem('documents');

        if (savedAccounts) {
            accounts = JSON.parse(savedAccounts);
        } else {
            // إنشاء بيانات تجريبية
            createSampleData();
        }

        if (savedDocuments) {
            documents = JSON.parse(savedDocuments);
        }

        // تحديث معرفات العناصر
        updateIds();

        console.log('تم تحميل البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        createSampleData();
    }
}

// حفظ البيانات في التخزين المحلي
function saveData() {
    try {
        localStorage.setItem('accounts', JSON.stringify(accounts));
        localStorage.setItem('documents', JSON.stringify(documents));
        console.log('تم حفظ البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        showAlert('خطأ في حفظ البيانات', 'danger');
    }
}

// إنشاء بيانات تجريبية
function createSampleData() {
    accounts = [
        {
            id: 1,
            number: 1001,
            name: 'حساب المرضى الداخليين',
            openingBalance: 5000,
            createdAt: new Date().toISOString()
        },
        {
            id: 2,
            number: 1002,
            name: 'حساب المرضى الخارجيين',
            openingBalance: 3000,
            createdAt: new Date().toISOString()
        },
        {
            id: 3,
            number: 1003,
            name: 'حساب الأدوية والمستلزمات',
            openingBalance: 10000,
            createdAt: new Date().toISOString()
        }
    ];

    documents = [
        {
            id: 1,
            accountId: 1,
            amount: 1500,
            documentNumber: 'DOC001',
            paymentNumber: 'PAY001',
            description: 'فاتورة علاج مريض',
            createdAt: new Date().toISOString()
        },
        {
            id: 2,
            accountId: 1,
            amount: 800,
            documentNumber: 'DOC002',
            paymentNumber: 'PAY002',
            description: 'رسوم إقامة',
            createdAt: new Date().toISOString()
        },
        {
            id: 3,
            accountId: 2,
            amount: 300,
            documentNumber: 'DOC003',
            paymentNumber: 'PAY003',
            description: 'كشف طبي',
            createdAt: new Date().toISOString()
        }
    ];

    currentAccountId = 4;
    currentDocumentId = 4;

    saveData();
}

// تحديث معرفات العناصر
function updateIds() {
    if (accounts.length > 0) {
        currentAccountId = Math.max(...accounts.map(a => a.id)) + 1;
    }
    if (documents.length > 0) {
        currentDocumentId = Math.max(...documents.map(d => d.id)) + 1;
    }
}

// عرض لوحة التحكم
function showDashboard() {
    updateActiveNavItem('showDashboard');

    const totalAccounts = accounts.length;
    const totalDocuments = documents.length;
    const totalBalance = calculateTotalBalance();
    const recentDocuments = documents.slice(-5).reverse();

    const html = `
        <div class="fade-in">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="text-gradient">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم الرئيسية
                    </h2>
                    <p class="text-muted">نظرة عامة على النظام المحاسبي</p>
                </div>
            </div>

            <!-- بطاقات الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="stat-number">${totalAccounts}</div>
                        <div class="stat-label">
                            <i class="fas fa-users me-1"></i>
                            إجمالي الحسابات
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        <div class="stat-number">${totalDocuments}</div>
                        <div class="stat-label">
                            <i class="fas fa-file-alt me-1"></i>
                            إجمالي المستندات
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <div class="stat-number">${totalBalance.toFixed(2)}</div>
                        <div class="stat-label">
                            <i class="fas fa-coins me-1"></i>
                            إجمالي الأرصدة
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                        <div class="stat-number">${new Date().toLocaleDateString('ar-JO')}</div>
                        <div class="stat-label">
                            <i class="fas fa-calendar me-1"></i>
                            التاريخ الحالي
                        </div>
                    </div>
                </div>
            </div>

            <!-- الحسابات والمستندات الحديثة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-users me-2"></i>الحسابات النشطة</h5>
                        </div>
                        <div class="card-body">
                            ${generateAccountsList()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-clock me-2"></i>المستندات الحديثة</h5>
                        </div>
                        <div class="card-body">
                            ${generateRecentDocuments(recentDocuments)}
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار سريعة -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-primary w-100" onclick="showAddAccount()">
                                        <i class="fas fa-user-plus me-2"></i>إضافة حساب
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-success w-100" onclick="showAddDocument()">
                                        <i class="fas fa-plus me-2"></i>إضافة مستند
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-info w-100" onclick="showSearch()">
                                        <i class="fas fa-search me-2"></i>البحث
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-warning w-100" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('mainContent').innerHTML = html;
}

// حساب إجمالي الأرصدة
function calculateTotalBalance() {
    return accounts.reduce((total, account) => {
        const accountDocuments = documents.filter(doc => doc.accountId === account.id);
        const documentsTotal = accountDocuments.reduce((sum, doc) => sum + doc.amount, 0);
        return total + account.openingBalance + documentsTotal;
    }, 0);
}

// إنشاء قائمة الحسابات للوحة التحكم
function generateAccountsList() {
    if (accounts.length === 0) {
        return '<p class="text-muted">لا توجد حسابات</p>';
    }

    return accounts.slice(0, 5).map(account => {
        const balance = calculateAccountBalance(account.id);
        return `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                <div>
                    <strong>${account.name}</strong><br>
                    <small class="text-muted">رقم: ${account.number}</small>
                </div>
                <div class="text-end">
                    <span class="badge ${balance >= 0 ? 'bg-success' : 'bg-danger'}">
                        ${balance.toFixed(2)} د.أ
                    </span>
                </div>
            </div>
        `;
    }).join('');
}

// إنشاء قائمة المستندات الحديثة
function generateRecentDocuments(recentDocs) {
    if (recentDocs.length === 0) {
        return '<p class="text-muted">لا توجد مستندات</p>';
    }

    return recentDocs.map(doc => {
        const account = accounts.find(a => a.id === doc.accountId);
        return `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                <div>
                    <strong>مستند ${doc.documentNumber}</strong><br>
                    <small class="text-muted">${account ? account.name : 'حساب محذوف'}</small>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary">${doc.amount.toFixed(2)} د.أ</span><br>
                    <small class="text-muted">${new Date(doc.createdAt).toLocaleDateString('ar-JO')}</small>
                </div>
            </div>
        `;
    }).join('');
}

// حساب رصيد حساب معين
function calculateAccountBalance(accountId) {
    const account = accounts.find(a => a.id === accountId);
    if (!account) return 0;

    const accountDocuments = documents.filter(doc => doc.accountId === accountId);
    const documentsTotal = accountDocuments.reduce((sum, doc) => sum + doc.amount, 0);

    return account.openingBalance + documentsTotal;
}

// عرض قائمة الحسابات
function showAccounts() {
    updateActiveNavItem('showAccounts');

    const html = `
        <div class="fade-in">
            <div class="row mb-4">
                <div class="col-md-8">
                    <h2 class="text-gradient">
                        <i class="fas fa-users me-2"></i>
                        إدارة الحسابات
                    </h2>
                    <p class="text-muted">عرض وإدارة جميع الحسابات المحاسبية</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-primary" onclick="showAddAccount()">
                        <i class="fas fa-plus me-2"></i>إضافة حساب جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الحساب</th>
                                    <th>اسم الحساب</th>
                                    <th>الرصيد الافتتاحي</th>
                                    <th>الرصيد الحالي</th>
                                    <th>عدد المستندات</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateAccountsTable()}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('mainContent').innerHTML = html;
}

// إنشاء جدول الحسابات
function generateAccountsTable() {
    if (accounts.length === 0) {
        return `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-folder-open fa-3x mb-3"></i><br>
                    لا توجد حسابات مسجلة
                </td>
            </tr>
        `;
    }

    return accounts.map(account => {
        const currentBalance = calculateAccountBalance(account.id);
        const documentsCount = documents.filter(doc => doc.accountId === account.id).length;

        return `
            <tr>
                <td><strong>${account.number}</strong></td>
                <td>${account.name}</td>
                <td class="arabic-numbers">${account.openingBalance.toFixed(2)} د.أ</td>
                <td class="arabic-numbers">
                    <span class="badge ${currentBalance >= 0 ? 'bg-success' : 'bg-danger'}">
                        ${currentBalance.toFixed(2)} د.أ
                    </span>
                </td>
                <td class="arabic-numbers">${documentsCount}</td>
                <td>${new Date(account.createdAt).toLocaleDateString('ar-JO')}</td>
                <td>
                    <button class="btn btn-sm btn-info me-1" onclick="showAccountDetails(${account.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning me-1" onclick="editAccount(${account.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteAccount(${account.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// عرض تفاصيل الحساب
function showAccountDetails(accountId) {
    const account = accounts.find(a => a.id === accountId);
    if (!account) {
        showAlert('الحساب غير موجود', 'danger');
        return;
    }

    const accountDocuments = documents.filter(doc => doc.accountId === accountId);
    const currentBalance = calculateAccountBalance(accountId);
    const totalAmount = accountDocuments.reduce((sum, doc) => sum + doc.amount, 0);

    const modalContent = `
        <div class="row">
            <div class="col-md-6">
                <h5 class="text-primary">معلومات الحساب</h5>
                <table class="table table-borderless">
                    <tr><td><strong>رقم الحساب:</strong></td><td>${account.number}</td></tr>
                    <tr><td><strong>اسم الحساب:</strong></td><td>${account.name}</td></tr>
                    <tr><td><strong>الرصيد الافتتاحي:</strong></td><td>${account.openingBalance.toFixed(2)} د.أ</td></tr>
                    <tr><td><strong>الرصيد الحالي:</strong></td><td class="${currentBalance >= 0 ? 'text-success' : 'text-danger'}">${currentBalance.toFixed(2)} د.أ</td></tr>
                    <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${new Date(account.createdAt).toLocaleDateString('ar-JO')}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5 class="text-info">إحصائيات</h5>
                <table class="table table-borderless">
                    <tr><td><strong>عدد المستندات:</strong></td><td>${accountDocuments.length}</td></tr>
                    <tr><td><strong>إجمالي المبالغ:</strong></td><td>${totalAmount.toFixed(2)} د.أ</td></tr>
                    <tr><td><strong>متوسط المبلغ:</strong></td><td>${accountDocuments.length > 0 ? (totalAmount / accountDocuments.length).toFixed(2) : '0.00'} د.أ</td></tr>
                </table>
            </div>
        </div>

        <hr>

        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="text-success">المستندات (${accountDocuments.length})</h5>
            <button class="btn btn-sm btn-success" onclick="exportAccountToExcel(${accountId})">
                <i class="fas fa-file-excel me-1"></i>تصدير Excel
            </button>
        </div>

        ${accountDocuments.length > 0 ? `
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>المبلغ</th>
                            <th>رقم المستند</th>
                            <th>رقم التأدية</th>
                            <th>الوصف</th>
                            <th>التاريخ</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${accountDocuments.map(doc => `
                            <tr>
                                <td class="arabic-numbers">${doc.amount.toFixed(2)} د.أ</td>
                                <td>${doc.documentNumber}</td>
                                <td>${doc.paymentNumber}</td>
                                <td>${doc.description || '-'}</td>
                                <td>${new Date(doc.createdAt).toLocaleDateString('ar-JO')}</td>
                                <td>
                                    <button class="btn btn-sm btn-warning me-1" onclick="editDocument(${doc.id})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteDocument(${doc.id})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        ` : '<p class="text-muted text-center py-3">لا توجد مستندات لهذا الحساب</p>'}
    `;

    document.getElementById('accountDetailsContent').innerHTML = modalContent;
    const modal = new bootstrap.Modal(document.getElementById('accountDetailsModal'));
    modal.show();
}

// تحديث العنصر النشط في القائمة الجانبية
function updateActiveNavItem(functionName) {
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });

    const activeItem = document.querySelector(`[onclick="${functionName}()"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
}

// تحديث لوحة التحكم
function updateDashboard() {
    // يمكن إضافة تحديثات دورية هنا
    console.log('تم تحديث لوحة التحكم');
}

// عرض التنبيهات
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();

    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    alertContainer.insertAdjacentHTML('beforeend', alertHtml);

    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            alertElement.remove();
        }
    }, duration);
}

// الحصول على أيقونة التنبيه
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// عرض نموذج إضافة حساب
function showAddAccount() {
    updateActiveNavItem('showAddAccount');

    const html = `
        <div class="fade-in">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="text-gradient">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة حساب جديد
                    </h2>
                    <p class="text-muted">إنشاء حساب محاسبي جديد في النظام</p>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <form id="addAccountForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountNumber" class="form-label">رقم الحساب *</label>
                                    <input type="number" class="form-control" id="accountNumber" required>
                                    <div class="form-text">رقم فريد للحساب</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountName" class="form-label">اسم الحساب *</label>
                                    <input type="text" class="form-control" id="accountName" required>
                                    <div class="form-text">اسم وصفي للحساب</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="openingBalance" class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" step="0.01" class="form-control" id="openingBalance" value="0">
                                    <div class="form-text">الرصيد الأولي للحساب</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الحساب
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="showAccounts()">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.getElementById('mainContent').innerHTML = html;

    // إضافة مستمع للنموذج
    document.getElementById('addAccountForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addAccount();
    });
}

// إضافة حساب جديد
function addAccount() {
    const accountNumber = parseInt(document.getElementById('accountNumber').value);
    const accountName = document.getElementById('accountName').value.trim();
    const openingBalance = parseFloat(document.getElementById('openingBalance').value) || 0;

    // التحقق من البيانات
    if (!accountNumber || !accountName) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    // التحقق من عدم تكرار رقم الحساب
    if (accounts.find(a => a.number === accountNumber)) {
        showAlert('رقم الحساب موجود مسبقاً', 'danger');
        return;
    }

    // إنشاء الحساب الجديد
    const newAccount = {
        id: currentAccountId++,
        number: accountNumber,
        name: accountName,
        openingBalance: openingBalance,
        createdAt: new Date().toISOString()
    };

    accounts.push(newAccount);
    saveData();

    showAlert('تم إضافة الحساب بنجاح', 'success');
    showAccounts();
}

// عرض نموذج إضافة مستند
function showAddDocument() {
    updateActiveNavItem('showAddDocument');

    if (accounts.length === 0) {
        showAlert('يجب إضافة حساب واحد على الأقل قبل إضافة المستندات', 'warning');
        showAddAccount();
        return;
    }

    const html = `
        <div class="fade-in">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="text-gradient">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مستند جديد
                    </h2>
                    <p class="text-muted">إضافة مستند محاسبي جديد لأحد الحسابات</p>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <form id="addDocumentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="documentAccount" class="form-label">الحساب *</label>
                                    <select class="form-control" id="documentAccount" required>
                                        <option value="">اختر الحساب</option>
                                        ${accounts.map(account =>
                                            `<option value="${account.id}">${account.number} - ${account.name}</option>`
                                        ).join('')}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="documentAmount" class="form-label">المبلغ *</label>
                                    <input type="number" step="0.01" class="form-control" id="documentAmount" required>
                                    <div class="form-text">المبلغ بالدينار الأردني</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="documentNumber" class="form-label">رقم المستند *</label>
                                    <input type="text" class="form-control" id="documentNumber" required>
                                    <div class="form-text">رقم المستند الرسمي</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="paymentNumber" class="form-label">رقم التأدية *</label>
                                    <input type="text" class="form-control" id="paymentNumber" required>
                                    <div class="form-text">رقم التأدية أو الإيصال</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="documentDescription" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="documentDescription" rows="3" placeholder="وصف اختياري للمستند"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ المستند
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="showDashboard()">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.getElementById('mainContent').innerHTML = html;

    // إضافة مستمع للنموذج
    document.getElementById('addDocumentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addDocument();
    });
}

// إضافة مستند جديد
function addDocument() {
    const accountId = parseInt(document.getElementById('documentAccount').value);
    const amount = parseFloat(document.getElementById('documentAmount').value);
    const documentNumber = document.getElementById('documentNumber').value.trim();
    const paymentNumber = document.getElementById('paymentNumber').value.trim();
    const description = document.getElementById('documentDescription').value.trim();

    // التحقق من البيانات
    if (!accountId || !amount || !documentNumber || !paymentNumber) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    if (amount <= 0) {
        showAlert('يجب أن يكون المبلغ أكبر من صفر', 'warning');
        return;
    }

    // التحقق من عدم تكرار رقم المستند
    if (documents.find(d => d.documentNumber === documentNumber)) {
        showAlert('رقم المستند موجود مسبقاً', 'danger');
        return;
    }

    // إنشاء المستند الجديد
    const newDocument = {
        id: currentDocumentId++,
        accountId: accountId,
        amount: amount,
        documentNumber: documentNumber,
        paymentNumber: paymentNumber,
        description: description,
        createdAt: new Date().toISOString()
    };

    documents.push(newDocument);
    saveData();

    showAlert('تم إضافة المستند بنجاح', 'success');

    // إعادة تعيين النموذج
    document.getElementById('addDocumentForm').reset();
}

// عرض صفحة البحث
function showSearch() {
    updateActiveNavItem('showSearch');

    const html = `
        <div class="fade-in">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="text-gradient">
                        <i class="fas fa-search me-2"></i>
                        البحث في المستندات
                    </h2>
                    <p class="text-muted">البحث في جميع المستندات والحسابات</p>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <form id="searchForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="searchQuery" class="form-label">كلمة البحث</label>
                                    <input type="text" class="form-control" id="searchQuery" placeholder="ابحث في رقم المستند، رقم التأدية، أو اسم الحساب...">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="searchType" class="form-label">نوع البحث</label>
                                    <select class="form-control" id="searchType">
                                        <option value="all">البحث في الكل</option>
                                        <option value="document">رقم المستند</option>
                                        <option value="payment">رقم التأدية</option>
                                        <option value="account">اسم الحساب</option>
                                        <option value="amount">المبلغ</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearSearch()">
                            <i class="fas fa-times me-2"></i>مسح
                        </button>
                    </form>
                </div>
            </div>

            <div id="searchResults"></div>
        </div>
    `;

    document.getElementById('mainContent').innerHTML = html;

    // إضافة مستمع للنموذج
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch();
    });
}

// تنفيذ البحث
function performSearch() {
    const query = document.getElementById('searchQuery').value.trim().toLowerCase();
    const searchType = document.getElementById('searchType').value;

    if (!query) {
        showAlert('يرجى إدخال كلمة البحث', 'warning');
        return;
    }

    const results = documents.filter(doc => {
        const account = accounts.find(a => a.id === doc.accountId);

        switch (searchType) {
            case 'document':
                return doc.documentNumber.toLowerCase().includes(query);
            case 'payment':
                return doc.paymentNumber.toLowerCase().includes(query);
            case 'account':
                return account && account.name.toLowerCase().includes(query);
            case 'amount':
                return doc.amount.toString().includes(query);
            default: // all
                return (
                    doc.documentNumber.toLowerCase().includes(query) ||
                    doc.paymentNumber.toLowerCase().includes(query) ||
                    doc.amount.toString().includes(query) ||
                    (account && account.name.toLowerCase().includes(query)) ||
                    (doc.description && doc.description.toLowerCase().includes(query))
                );
        }
    });

    displaySearchResults(results, query);
}

// عرض نتائج البحث
function displaySearchResults(results, query) {
    const resultsContainer = document.getElementById('searchResults');

    if (results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-search-minus fa-3x text-muted mb-3"></i>
                    <h5>لا توجد نتائج</h5>
                    <p class="text-muted">لم يتم العثور على مستندات تطابق "${query}"</p>
                </div>
            </div>
        `;
        return;
    }

    const html = `
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>نتائج البحث (${results.length} نتيجة)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم المستند</th>
                                <th>رقم التأدية</th>
                                <th>المبلغ</th>
                                <th>الحساب</th>
                                <th>الوصف</th>
                                <th>التاريخ</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${results.map(doc => {
                                const account = accounts.find(a => a.id === doc.accountId);
                                return `
                                    <tr>
                                        <td><strong>${doc.documentNumber}</strong></td>
                                        <td>${doc.paymentNumber}</td>
                                        <td class="arabic-numbers">${doc.amount.toFixed(2)} د.أ</td>
                                        <td>${account ? account.name : 'حساب محذوف'}</td>
                                        <td>${doc.description || '-'}</td>
                                        <td>${new Date(doc.createdAt).toLocaleDateString('ar-JO')}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="showAccountDetails(${doc.accountId})" title="عرض الحساب">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    resultsContainer.innerHTML = html;
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchQuery').value = '';
    document.getElementById('searchType').value = 'all';
    document.getElementById('searchResults').innerHTML = '';
}

// عرض التقارير
function showReports() {
    updateActiveNavItem('showReports');

    const totalAccounts = accounts.length;
    const totalDocuments = documents.length;
    const totalBalance = calculateTotalBalance();
    const avgBalance = totalAccounts > 0 ? totalBalance / totalAccounts : 0;

    const html = `
        <div class="fade-in">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="text-gradient">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h2>
                    <p class="text-muted">تقارير شاملة عن النظام المحاسبي</p>
                </div>
            </div>

            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary">${totalAccounts}</h4>
                            <p class="mb-0">إجمالي الحسابات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                            <h4 class="text-success">${totalDocuments}</h4>
                            <p class="mb-0">إجمالي المستندات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning">${totalBalance.toFixed(2)}</h4>
                            <p class="mb-0">إجمالي الأرصدة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-calculator fa-2x text-info mb-2"></i>
                            <h4 class="text-info">${avgBalance.toFixed(2)}</h4>
                            <p class="mb-0">متوسط الرصيد</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقرير الحسابات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-users me-2"></i>تقرير الحسابات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم الحساب</th>
                                    <th>اسم الحساب</th>
                                    <th>الرصيد الافتتاحي</th>
                                    <th>عدد المستندات</th>
                                    <th>إجمالي المبالغ</th>
                                    <th>الرصيد الحالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${accounts.map(account => {
                                    const accountDocs = documents.filter(d => d.accountId === account.id);
                                    const docsTotal = accountDocs.reduce((sum, doc) => sum + doc.amount, 0);
                                    const currentBalance = account.openingBalance + docsTotal;

                                    return `
                                        <tr>
                                            <td>${account.number}</td>
                                            <td>${account.name}</td>
                                            <td class="arabic-numbers">${account.openingBalance.toFixed(2)} د.أ</td>
                                            <td>${accountDocs.length}</td>
                                            <td class="arabic-numbers">${docsTotal.toFixed(2)} د.أ</td>
                                            <td class="arabic-numbers ${currentBalance >= 0 ? 'text-success' : 'text-danger'}">
                                                ${currentBalance.toFixed(2)} د.أ
                                            </td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- أزرار التصدير -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-download me-2"></i>تصدير التقارير</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-success w-100" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel شامل
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-info w-100" onclick="exportAccountsReport()">
                                <i class="fas fa-users me-2"></i>تقرير الحسابات
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-warning w-100" onclick="exportDocumentsReport()">
                                <i class="fas fa-file-alt me-2"></i>تقرير المستندات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('mainContent').innerHTML = html;
}
