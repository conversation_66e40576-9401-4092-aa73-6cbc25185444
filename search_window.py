import tkinter as tk
from tkinter import ttk, messagebox
import openpyxl
import os
from datetime import datetime

class SearchWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("🔍 البحث في الحسابات")
        self.parent = parent

        # تكوين النافذة
        self.geometry("1000x700")
        self.configure(bg='#f0f8ff')
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent.root)
        self.grab_set()

        # متغيرات البحث
        self.search_type_var = tk.StringVar(value="رقم المستند")
        self.search_value_var = tk.StringVar()
        self.selected_account_var = tk.StringVar(value="جميع الحسابات")

        # قائمة النتائج والحسابات
        self.search_results = []
        self.available_accounts = []

        # تحميل قائمة الحسابات
        self.load_accounts_list()

        # إنشاء الواجهة
        self.create_interface()

        # توسيط النافذة
        self.center_window()

        # ربط حدث الإغلاق
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_accounts_list(self):
        """تحميل قائمة الحسابات من ملف الحسابات"""
        try:
            accounting_file = "accounting_system.xlsx"
            if not os.path.exists(accounting_file):
                self.available_accounts = ["جميع الحسابات"]
                return

            workbook = openpyxl.load_workbook(accounting_file)
            accounts = ["جميع الحسابات"]

            for sheet_name in workbook.sheetnames:
                if sheet_name not in ["مرحباً", "تقرير حسابات المواد"]:  # تجاهل الأوراق الخاصة
                    accounts.append(sheet_name)

            workbook.close()
            self.available_accounts = accounts

        except Exception as e:
            print(f"خطأ في تحميل قائمة الحسابات: {str(e)}")
            self.available_accounts = ["جميع الحسابات"]

    def refresh_accounts_list(self):
        """تحديث قائمة الحسابات"""
        self.load_accounts_list()
        if hasattr(self, 'account_combo'):
            self.account_combo['values'] = self.available_accounts
            # إعادة تعيين القيمة إذا لم تعد موجودة
            current_value = self.selected_account_var.get()
            if current_value not in self.available_accounts:
                self.selected_account_var.set("جميع الحسابات")
        messagebox.showinfo("تم", f"تم تحديث قائمة الحسابات. عدد الحسابات: {len(self.available_accounts)-1}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # عنوان النافذة
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        title_label = ttk.Label(title_frame,
                               text="🔍 البحث في الحسابات",
                               font=('Arial', 16, 'bold'))
        title_label.pack()

        subtitle_label = ttk.Label(title_frame,
                                  text="البحث عن المستندات في ملف نظام المحاسبة",
                                  font=('Arial', 10))
        subtitle_label.pack()

        # إطار البحث
        self.create_search_frame(main_frame)

        # إطار النتائج
        self.create_results_frame(main_frame)

        # إطار الأزرار
        self.create_buttons_frame(main_frame)

    def create_search_frame(self, parent):
        """إنشاء إطار البحث"""
        search_frame = ttk.LabelFrame(parent, text="🔎 معايير البحث", padding="15")
        search_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        search_frame.grid_columnconfigure(1, weight=1)
        search_frame.grid_columnconfigure(3, weight=1)
        search_frame.grid_columnconfigure(5, weight=1)

        # الصف الأول: نوع البحث وقيمة البحث
        # نوع البحث
        ttk.Label(search_frame, text="نوع البحث:",
                 font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=(0, 10), pady=5, sticky=tk.W)

        search_type_combo = ttk.Combobox(search_frame,
                                        textvariable=self.search_type_var,
                                        values=["رقم المستند", "رقم التأدية", "قيمة المستند", "اسم الحساب"],
                                        state='readonly',
                                        font=('Arial', 10),
                                        width=15)
        search_type_combo.grid(row=0, column=1, padx=(0, 20), pady=5, sticky=(tk.W, tk.E))

        # قيمة البحث
        ttk.Label(search_frame, text="قيمة البحث:",
                 font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=(0, 10), pady=5, sticky=tk.W)

        search_entry = ttk.Entry(search_frame,
                                textvariable=self.search_value_var,
                                font=('Arial', 10))
        search_entry.grid(row=0, column=3, padx=(0, 20), pady=5, sticky=(tk.W, tk.E))

        # ربط Enter للبحث
        search_entry.bind('<Return>', lambda e: self.perform_search())

        # زر البحث
        search_btn = ttk.Button(search_frame,
                               text="🔍 بحث",
                               command=self.perform_search,
                               style='Accent.TButton')
        search_btn.grid(row=0, column=4, padx=(10, 0), pady=5)

        # زر مسح
        clear_btn = ttk.Button(search_frame,
                              text="🗑️ مسح",
                              command=self.clear_search)
        clear_btn.grid(row=0, column=5, padx=(10, 0), pady=5)

        # الصف الثاني: اختيار الحساب
        ttk.Label(search_frame, text="الحساب:",
                 font=('Arial', 10, 'bold')).grid(row=1, column=0, padx=(0, 10), pady=(10, 5), sticky=tk.W)

        self.account_combo = ttk.Combobox(search_frame,
                                         textvariable=self.selected_account_var,
                                         values=self.available_accounts,
                                         state='readonly',
                                         font=('Arial', 10),
                                         width=30)
        self.account_combo.grid(row=1, column=1, columnspan=2, padx=(0, 20), pady=(10, 5), sticky=(tk.W, tk.E))

        # زر تحديث قائمة الحسابات
        refresh_accounts_btn = ttk.Button(search_frame,
                                         text="🔄 تحديث الحسابات",
                                         command=self.refresh_accounts_list)
        refresh_accounts_btn.grid(row=1, column=3, columnspan=2, padx=(10, 0), pady=(10, 5))

    def create_results_frame(self, parent):
        """إنشاء إطار النتائج"""
        results_frame = ttk.LabelFrame(parent, text="📋 نتائج البحث", padding="10")
        results_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(0, weight=1)

        # إنشاء Treeview
        columns = ('الحساب', 'رقم المستند', 'رقم التأدية', 'قيمة المستند', 'الموقع', 'القسم', 'الجدول')
        self.tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين والعرض
        column_widths = {
            'الحساب': 200,
            'رقم المستند': 120,
            'رقم التأدية': 120,
            'قيمة المستند': 120,
            'الموقع': 80,
            'القسم': 80,
            'الجدول': 80
        }

        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER)
            self.tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # وضع العناصر
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # ربط النقر المزدوج
        self.tree.bind('<Double-1>', self.on_item_double_click)

        # ربط النقر الأيمن لقائمة السياق
        self.tree.bind('<Button-3>', self.show_context_menu)

        # إنشاء قائمة السياق
        self.create_context_menu()

    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="✏️ تعديل المستند", command=self.edit_document)
        self.context_menu.add_command(label="🗑️ حذف المستند", command=self.delete_document)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 نسخ رقم المستند", command=self.copy_document_number)
        self.context_menu.add_command(label="📄 عرض تفاصيل الحساب", command=self.show_account_details)

    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # إحصائيات البحث
        self.stats_label = ttk.Label(buttons_frame,
                                    text="جاهز للبحث...",
                                    font=('Arial', 10))
        self.stats_label.pack(side=tk.LEFT)

        # أزرار العمليات
        btn_frame = ttk.Frame(buttons_frame)
        btn_frame.pack(side=tk.RIGHT)

        ttk.Button(btn_frame,
                  text="✏️ تعديل المحدد",
                  command=self.edit_document).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(btn_frame,
                  text="🗑️ حذف المحدد",
                  command=self.delete_document).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(btn_frame,
                  text="🔄 تحديث",
                  command=self.refresh_search).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(btn_frame,
                  text="❌ إغلاق",
                  command=self.on_closing).pack(side=tk.LEFT)

    def perform_search(self):
        """تنفيذ عملية البحث"""
        search_type = self.search_type_var.get()
        search_value = self.search_value_var.get().strip()

        if not search_value:
            messagebox.showwarning("تنبيه", "الرجاء إدخال قيمة للبحث")
            return

        # مسح النتائج السابقة
        for item in self.tree.get_children():
            self.tree.delete(item)

        self.search_results = []

        try:
            # البحث في ملف الحسابات
            results = self.search_in_accounts_file(search_type, search_value)

            if results:
                for result in results:
                    # إضافة النتيجة للجدول
                    item_id = self.tree.insert('', 'end', values=(
                        result['account'],
                        result['document_num'],
                        result['voucher_num'],
                        f"{result['amount']:.3f}" if result['amount'] else '',
                        result['position'],
                        result['section'],
                        result['table']
                    ))

                    # حفظ البيانات الكاملة
                    self.search_results.append({
                        'item_id': item_id,
                        'data': result
                    })

                # تحديث الإحصائيات
                self.stats_label.config(text=f"تم العثور على {len(results)} نتيجة")

                # تحديد أول عنصر
                if self.tree.get_children():
                    self.tree.selection_set(self.tree.get_children()[0])
                    self.tree.focus(self.tree.get_children()[0])
            else:
                self.stats_label.config(text="لم يتم العثور على نتائج")
                messagebox.showinfo("نتائج البحث", "لم يتم العثور على نتائج مطابقة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
            self.stats_label.config(text="خطأ في البحث")

    def search_in_accounts_file(self, search_type, search_value):
        """البحث في ملف الحسابات"""
        results = []
        accounting_file = "accounting_system.xlsx"

        if not os.path.exists(accounting_file):
            messagebox.showerror("خطأ", f"ملف الحسابات غير موجود: {accounting_file}")
            return results

        try:
            workbook = openpyxl.load_workbook(accounting_file)
            selected_account = self.selected_account_var.get()

            # تحديد الحسابات المراد البحث فيها
            if selected_account == "جميع الحسابات":
                sheets_to_search = [name for name in workbook.sheetnames
                                  if name not in ["مرحباً", "تقرير حسابات المواد"]]
            else:
                if selected_account in workbook.sheetnames:
                    sheets_to_search = [selected_account]
                else:
                    messagebox.showerror("خطأ", f"الحساب '{selected_account}' غير موجود")
                    return results

            # البحث في الحسابات المحددة
            for sheet_name in sheets_to_search:
                ws = workbook[sheet_name]
                sheet_results = self.search_in_account_sheet(ws, sheet_name, search_type, search_value)
                results.extend(sheet_results)

            workbook.close()

        except Exception as e:
            raise Exception(f"خطأ في قراءة ملف الحسابات: {str(e)}")

        return results

    def search_in_account_sheet(self, ws, sheet_name, search_type, search_value):
        """البحث الشامل في ورقة حساب معينة"""
        results = []

        # أعمدة البيانات (قيمة، رقم مستند، رقم تأدية) لكل قسم
        sections_columns = [
            ('A', 'B', 'C'),  # القسم الأول
            ('D', 'E', 'F'),  # القسم الثاني
            ('G', 'H', 'I'),  # القسم الثالث
            ('J', 'K', 'L'),  # القسم الرابع
            ('M', 'N', 'O'),  # القسم الخامس
            ('P', 'Q', 'R')   # القسم السادس
        ]

        section_names = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس']

        try:
            # البحث الشامل في جميع الخلايا
            for row in range(1, ws.max_row + 1):
                for section_idx, (amount_col, doc_col, voucher_col) in enumerate(sections_columns):
                    section_name = section_names[section_idx]

                    try:
                        amount_value = ws[f'{amount_col}{row}'].value
                        doc_value = ws[f'{doc_col}{row}'].value
                        voucher_value = ws[f'{voucher_col}{row}'].value

                        if not amount_value and not doc_value and not voucher_value:
                            continue

                        if self.is_header_or_label(amount_value, doc_value, voucher_value):
                            continue

                        match_found = False

                        if search_type == "رقم المستند" and doc_value:
                            if str(doc_value).strip() == str(search_value).strip():
                                match_found = True
                        elif search_type == "رقم التأدية" and voucher_value:
                            if str(voucher_value).strip() == str(search_value).strip():
                                match_found = True
                        elif search_type == "قيمة المستند" and amount_value:
                            try:
                                if abs(float(amount_value) - float(search_value)) < 0.001:
                                    match_found = True
                            except (ValueError, TypeError):
                                if str(amount_value).strip() == str(search_value).strip():
                                    match_found = True
                        elif search_type == "اسم الحساب":
                            if search_value.lower() in sheet_name.lower():
                                match_found = True

                        if match_found:
                            table_num = self.determine_table_number(row)

                            results.append({
                                'account': sheet_name,
                                'document_num': str(doc_value) if doc_value else '',
                                'voucher_num': str(voucher_value) if voucher_value else '',
                                'amount': self.safe_float_conversion(amount_value),
                                'position': f'{amount_col}{row}',
                                'doc_position': f'{doc_col}{row}',
                                'voucher_position': f'{voucher_col}{row}',
                                'section': section_name,
                                'table': table_num,
                                'row': row,
                                'amount_col': amount_col,
                                'doc_col': doc_col,
                                'voucher_col': voucher_col
                            })

                    except Exception:
                        continue

        except Exception as e:
            print(f"خطأ في البحث في {sheet_name}: {str(e)}")

        return results

    def is_header_or_label(self, amount_value, doc_value, voucher_value):
        """فحص إذا كانت القيم عبارة عن عناوين"""
        try:
            header_keywords = [
                'المبلغ', 'مبلغ', 'رقم', 'مستند', 'تأدية',
                'الحساب', 'رصيد', 'قسم', 'جدول',
                'مجموع', 'إجمالي', 'total', 'sum'
            ]

            for value in [amount_value, doc_value, voucher_value]:
                if value:
                    value_str = str(value).strip().lower()
                    for keyword in header_keywords:
                        if keyword in value_str:
                            return True
            return False
        except:
            return False

    def determine_table_number(self, row):
        """تحديد رقم الجدول"""
        try:
            if row <= 35:
                return 1
            elif row <= 60:
                return 2
            elif row <= 85:
                return 3
            else:
                return ((row - 10) // 25) + 1
        except:
            return 1

    def safe_float_conversion(self, value):
        """تحويل آمن للقيم العددية"""
        try:
            if value is None:
                return 0.0
            if isinstance(value, (int, float)):
                return float(value)
            value_str = str(value).strip()
            if not value_str:
                return 0.0
            cleaned_value = ''.join(c for c in value_str if c.isdigit() or c in '.-')
            return float(cleaned_value) if cleaned_value else 0.0
        except:
            return 0.0

    def clear_search(self):
        """مسح البحث"""
        self.search_value_var.set("")
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.search_results = []
        self.stats_label.config(text="جاهز للبحث...")

    def refresh_search(self):
        """تحديث البحث"""
        if self.search_value_var.get().strip():
            self.perform_search()

    def center_window(self):
        """توسيط النافذة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """معالج إغلاق النافذة"""
        try:
            if hasattr(self.parent, 'close_window'):
                self.parent.close_window('search', self)
        except:
            pass
        self.destroy()

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.tree.focus(item)
            self.context_menu.post(event.x_root, event.y_root)

    def on_item_double_click(self, event):
        """معالج النقر المزدوج"""
        self.edit_document()

    def get_selected_result(self):
        """الحصول على النتيجة المحددة"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء تحديد مستند من القائمة")
            return None

        item_id = selection[0]
        for result in self.search_results:
            if result['item_id'] == item_id:
                return result['data']
        return None

    def edit_document(self):
        """تعديل المستند المحدد"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        # إنشاء نافذة التعديل
        self.show_edit_dialog(selected_data)

    def show_edit_dialog(self, data):
        """عرض نافذة تعديل المستند"""
        edit_window = tk.Toplevel(self)
        edit_window.title(f"تعديل مستند - {data['account']}")
        edit_window.geometry("500x400")
        edit_window.configure(bg='#f0f0f0')
        edit_window.transient(self)
        edit_window.grab_set()

        # توسيط النافذة
        edit_window.update_idletasks()
        x = (edit_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (edit_window.winfo_screenheight() // 2) - (400 // 2)
        edit_window.geometry(f"500x400+{x}+{y}")

        # الإطار الرئيسي
        main_frame = ttk.Frame(edit_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = ttk.Label(main_frame, text="تعديل بيانات المستند",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))

        # معلومات الحساب
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الحساب", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(info_frame, text=f"اسم الحساب: {data['account']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"الموقع: {data['position']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"القسم: {data['section']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"رقم الجدول: {data['table']}").pack(anchor=tk.W)

        # حقول التعديل
        edit_frame = ttk.LabelFrame(main_frame, text="بيانات التعديل", padding="10")
        edit_frame.pack(fill=tk.X, pady=(0, 20))

        # رقم المستند
        ttk.Label(edit_frame, text="رقم المستند:").grid(row=0, column=0, sticky=tk.W, pady=5)
        doc_var = tk.StringVar(value=data['document_num'])
        doc_entry = ttk.Entry(edit_frame, textvariable=doc_var, width=30)
        doc_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # رقم التأدية
        ttk.Label(edit_frame, text="رقم التأدية:").grid(row=1, column=0, sticky=tk.W, pady=5)
        voucher_var = tk.StringVar(value=data['voucher_num'])
        voucher_entry = ttk.Entry(edit_frame, textvariable=voucher_var, width=30)
        voucher_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # قيمة المستند
        ttk.Label(edit_frame, text="قيمة المستند:").grid(row=2, column=0, sticky=tk.W, pady=5)
        amount_var = tk.StringVar(value=f"{data['amount']:.3f}")
        amount_entry = ttk.Entry(edit_frame, textvariable=amount_var, width=30)
        amount_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        edit_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        def save_changes():
            try:
                new_amount = float(amount_var.get())
                if new_amount <= 0:
                    messagebox.showerror("خطأ", "يجب أن تكون قيمة المستند أكبر من صفر")
                    return

                # تحديث البيانات
                updated_data = {
                    'account': data['account'],
                    'position': data['position'],
                    'document_num': doc_var.get().strip(),
                    'voucher_num': voucher_var.get().strip(),
                    'amount': new_amount
                }

                if self.update_document_in_file(updated_data):
                    messagebox.showinfo("نجح", "تم تحديث المستند بنجاح")
                    edit_window.destroy()
                    self.refresh_search()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث المستند")

            except ValueError:
                messagebox.showerror("خطأ", "قيمة المستند غير صحيحة")

        ttk.Button(buttons_frame, text="✅ حفظ التغييرات",
                  command=save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="❌ إلغاء",
                  command=edit_window.destroy).pack(side=tk.LEFT)

    def update_document_in_file(self, data):
        """تحديث المستند في الملف"""
        try:
            accounting_file = "accounting_system.xlsx"

            if not self.check_file_access(accounting_file):
                return False

            workbook = openpyxl.load_workbook(accounting_file)

            if data['account'] not in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{data['account']}' غير موجود")
                return False

            ws = workbook[data['account']]

            # استخراج معلومات الموقع
            position = data['position']
            amount_col = position[0]
            row = int(position[1:])

            # تحديد عمود رقم المستند (العمود التالي)
            doc_col = chr(ord(amount_col) + 1)

            # تحديث البيانات
            ws[f'{amount_col}{row}'] = data['amount']
            ws[f'{amount_col}{row}'].number_format = '0.000'
            ws[f'{doc_col}{row}'] = data['document_num']

            # حفظ الملف
            workbook.save(accounting_file)
            workbook.close()

            print(f"✅ تم تحديث المستند في {data['account']} - {position}")
            return True

        except Exception as e:
            print(f"❌ خطأ في تحديث المستند: {str(e)}")
            return False

    def copy_document_number(self):
        """نسخ رقم المستند إلى الحافظة"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        self.clipboard_clear()
        self.clipboard_append(selected_data['document_num'])
        messagebox.showinfo("تم", f"تم نسخ رقم المستند: {selected_data['document_num']}")

    def show_account_details(self):
        """عرض تفاصيل الحساب"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        # فتح نافذة تفاصيل الحساب
        try:
            from account_details_window import AccountDetailsWindow
            details_window = AccountDetailsWindow(self, selected_data['account'], "accounting_system.xlsx")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التفاصيل: {str(e)}")

    def check_file_access(self, file_path):
        """فحص إمكانية الوصول للملف"""
        try:
            if not os.path.exists(file_path):
                messagebox.showerror("خطأ", f"الملف غير موجود: {file_path}")
                return False

            # فحص إذا كان الملف مفتوح في برنامج آخر
            try:
                with open(file_path, 'r+b'):
                    pass
                return True
            except PermissionError:
                messagebox.showerror("خطأ",
                                   f"الملف مفتوح في برنامج آخر.\n\n"
                                   f"الرجاء إغلاق الملف في Excel أو أي برنامج آخر والمحاولة مرة أخرى.")
                return False

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فحص الملف: {str(e)}")
            return False

    def delete_document(self):
        """حذف المستند المحدد مع الإزاحة التلقائية"""
        selected_data = self.get_selected_result()
        if not selected_data:
            return

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف المستند؟\n\n"
                              f"الحساب: {selected_data['account']}\n"
                              f"رقم المستند: {selected_data['document_num']}\n"
                              f"رقم التأدية: {selected_data['voucher_num']}\n"
                              f"القيمة: {selected_data['amount']:.3f}\n"
                              f"الموقع: {selected_data['position']}\n\n"
                              f"⚠️ تحذير: سيتم حذف البيانات وإزاحة البيانات التالية تلقائياً"):

            if self.delete_document_from_file(selected_data):
                messagebox.showinfo("نجح", "تم حذف المستند بنجاح مع إزاحة البيانات")
                self.refresh_search()  # تحديث النتائج
            else:
                messagebox.showerror("خطأ", "فشل في حذف المستند")

    def delete_document_from_file(self, data):
        """حذف المستند من الملف مع الإزاحة التلقائية"""
        try:
            accounting_file = "accounting_system.xlsx"

            # فحص إمكانية الوصول للملف
            if not self.check_file_access(accounting_file):
                return False

            workbook = openpyxl.load_workbook(accounting_file)

            if data['account'] not in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{data['account']}' غير موجود")
                return False

            ws = workbook[data['account']]

            print(f"🗑️ بدء حذف المستند من {data['account']}")
            print(f"📍 الموقع: {data['position']} - الصف {data['row']}")

            # حذف البيانات مع الإزاحة التلقائية
            success = self.delete_and_shift_data(ws, data)

            if success:
                # حفظ الملف
                workbook.save(accounting_file)
                workbook.close()
                print(f"✅ تم حذف المستند بنجاح مع الإزاحة")
                return True
            else:
                workbook.close()
                return False

        except Exception as e:
            print(f"❗ خطأ في حذف المستند: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في حذف المستند: {str(e)}")
            return False

    def delete_and_shift_data(self, ws, data):
        """حذف المستند وإزاحة البيانات تلقائياً"""
        try:
            row_to_delete = data['row']
            amount_col = data['amount_col']
            doc_col = data['doc_col']
            voucher_col = data['voucher_col']

            print(f"🔄 بدء إزاحة البيانات من الصف {row_to_delete}")

            # العثور على آخر صف يحتوي على بيانات في هذا القسم
            last_row = self.find_last_data_row_in_section(ws, amount_col, doc_col, voucher_col, row_to_delete)

            if last_row <= row_to_delete:
                # لا توجد بيانات للإزاحة، فقط امسح الصف الحالي
                self.clear_row_data(ws, row_to_delete, amount_col, doc_col, voucher_col)
                print(f"✅ تم مسح الصف {row_to_delete} (لا توجد بيانات للإزاحة)")
                return True

            # إزاحة البيانات لأعلى
            for current_row in range(row_to_delete, last_row):
                next_row = current_row + 1

                # نسخ البيانات من الصف التالي إلى الصف الحالي
                self.copy_row_data_with_formatting(ws, next_row, current_row, amount_col, doc_col, voucher_col)
                print(f"📋 تم نسخ البيانات من الصف {next_row} إلى الصف {current_row}")

            # مسح الصف الأخير (الذي أصبح مكرراً)
            self.clear_row_data(ws, last_row, amount_col, doc_col, voucher_col)
            print(f"🧹 تم مسح الصف الأخير {last_row}")

            print(f"✅ تمت الإزاحة بنجاح من الصف {row_to_delete} إلى الصف {last_row}")
            return True

        except Exception as e:
            print(f"❗ خطأ في إزاحة البيانات: {str(e)}")
            return False

    def find_last_data_row_in_section(self, ws, amount_col, doc_col, voucher_col, start_row):
        """العثور على آخر صف يحتوي على بيانات في القسم"""
        try:
            last_row = start_row

            # البحث من الصف الحالي إلى آخر صف في الورقة
            for row in range(start_row, ws.max_row + 1):
                amount_value = ws[f'{amount_col}{row}'].value
                doc_value = ws[f'{doc_col}{row}'].value
                voucher_value = ws[f'{voucher_col}{row}'].value

                # إذا وجدت بيانات، حدث آخر صف
                if amount_value or doc_value or voucher_value:
                    # تجاهل العناوين
                    if not self.is_header_or_label(amount_value, doc_value, voucher_value):
                        last_row = row

            return last_row

        except Exception as e:
            print(f"❗ خطأ في العثور على آخر صف: {str(e)}")
            return start_row

    def copy_row_data_with_formatting(self, ws, source_row, target_row, amount_col, doc_col, voucher_col):
        """نسخ بيانات الصف مع الحفاظ على التنسيق"""
        try:
            # نسخ كل خلية مع تنسيقها
            for col in [amount_col, doc_col, voucher_col]:
                source_cell = ws[f'{col}{source_row}']
                target_cell = ws[f'{col}{target_row}']

                # نسخ القيمة
                target_cell.value = source_cell.value

                # نسخ التنسيق
                if source_cell.font:
                    target_cell.font = source_cell.font
                if source_cell.border:
                    target_cell.border = source_cell.border
                if source_cell.fill:
                    target_cell.fill = source_cell.fill
                if source_cell.alignment:
                    target_cell.alignment = source_cell.alignment
                if source_cell.number_format:
                    target_cell.number_format = source_cell.number_format

        except Exception as e:
            print(f"❗ خطأ في نسخ بيانات الصف: {str(e)}")
            raise e

    def clear_row_data(self, ws, row, amount_col, doc_col, voucher_col):
        """مسح بيانات الصف مع الحفاظ على التنسيق"""
        try:
            for col in [amount_col, doc_col, voucher_col]:
                cell = ws[f'{col}{row}']

                # حفظ التنسيق
                original_font = cell.font
                original_border = cell.border
                original_fill = cell.fill
                original_alignment = cell.alignment
                original_number_format = cell.number_format

                # مسح القيمة فقط
                cell.value = None

                # استعادة التنسيق
                cell.font = original_font
                cell.border = original_border
                cell.fill = original_fill
                cell.alignment = original_alignment
                cell.number_format = original_number_format

        except Exception as e:
            print(f"❗ خطأ في مسح بيانات الصف: {str(e)}")
            raise e

    def copy_document_number(self):
        """نسخ رقم المستند"""
        selected_data = self.get_selected_result()
        if selected_data:
            self.clipboard_clear()
            self.clipboard_append(str(selected_data['document_num']))
            messagebox.showinfo("تم", f"تم نسخ رقم المستند: {selected_data['document_num']}")

    def show_account_details(self):
        """عرض تفاصيل الحساب"""
        selected_data = self.get_selected_result()
        if selected_data:
            messagebox.showinfo("تفاصيل الحساب",
                               f"اسم الحساب: {selected_data['account']}\n"
                               f"الموقع: {selected_data['position']}\n"
                               f"القسم: {selected_data['section']}\n"
                               f"الجدول: {selected_data['table']}\n"
                               f"رقم المستند: {selected_data['document_num']}\n"
                               f"رقم التأدية: {selected_data['voucher_num']}")

    def show_edit_dialog(self, data):
        """عرض نافذة تعديل المستند"""
        edit_window = tk.Toplevel(self)
        edit_window.title("✏️ تعديل المستند")
        edit_window.geometry("550x500")
        edit_window.minsize(500, 450)
        edit_window.configure(bg='#f0f8ff')
        edit_window.transient(self)
        edit_window.grab_set()
        edit_window.resizable(True, True)

        # توسيط النافذة
        edit_window.update_idletasks()
        width = edit_window.winfo_reqwidth()
        height = edit_window.winfo_reqheight()
        x = (edit_window.winfo_screenwidth() // 2) - (width // 2)
        y = (edit_window.winfo_screenheight() // 2) - (height // 2)
        edit_window.geometry(f"{width}x{height}+{x}+{y}")

        # الإطار الرئيسي
        main_frame = ttk.Frame(edit_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = ttk.Label(main_frame, text="✏️ تعديل بيانات المستند",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # معلومات الحساب
        info_frame = ttk.LabelFrame(main_frame, text="📁 معلومات الحساب", padding="15")
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame, text=f"الحساب: {data['account']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"الموقع: {data['position']} - {data['section']} - الجدول {data['table']}").pack(anchor=tk.W)

        # حقول التعديل
        edit_frame = ttk.LabelFrame(main_frame, text="📝 البيانات الجديدة", padding="15")
        edit_frame.pack(fill=tk.X, pady=(0, 15))

        # رقم المستند
        ttk.Label(edit_frame, text="📄 رقم المستند:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        doc_var = tk.StringVar(value=str(data['document_num']) if data['document_num'] else "")
        doc_entry = ttk.Entry(edit_frame, textvariable=doc_var, font=('Arial', 11))
        doc_entry.pack(fill=tk.X, pady=(5, 10))

        # رقم التأدية
        ttk.Label(edit_frame, text="📎 رقم التأدية:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        voucher_var = tk.StringVar(value=str(data['voucher_num']) if data['voucher_num'] else "")
        voucher_entry = ttk.Entry(edit_frame, textvariable=voucher_var, font=('Arial', 11))
        voucher_entry.pack(fill=tk.X, pady=(5, 10))

        # قيمة المستند
        ttk.Label(edit_frame, text="💰 قيمة المستند:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        amount_frame = ttk.Frame(edit_frame)
        amount_frame.pack(fill=tk.X, pady=(5, 10))

        amount_var = tk.StringVar(value=f"{data['amount']:.3f}" if data['amount'] else "0.000")
        amount_entry = ttk.Entry(amount_frame, textvariable=amount_var, font=('Arial', 11), width=20)
        amount_entry.pack(side=tk.LEFT)
        ttk.Label(amount_frame, text="دينار", font=('Arial', 10)).pack(side=tk.LEFT, padx=(10, 0))

        # تركيز على حقل رقم المستند
        doc_entry.focus_set()

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        def save_changes():
            try:
                new_doc = doc_var.get().strip()
                new_voucher = voucher_var.get().strip()
                new_amount_str = amount_var.get().strip()

                if not new_amount_str:
                    messagebox.showwarning("تنبيه", "الرجاء إدخال قيمة المستند")
                    return

                try:
                    new_amount = float(new_amount_str)
                except ValueError:
                    messagebox.showerror("خطأ", "الرجاء إدخال قيمة رقمية صحيحة")
                    return

                if self.update_account_document(data, new_doc, new_voucher, new_amount):
                    messagebox.showinfo("✅ نجح", "تم تحديث المستند بنجاح!")
                    edit_window.destroy()
                    self.refresh_search()
                else:
                    messagebox.showerror("❌ خطأ", "فشل في تحديث المستند")

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ غير متوقع: {str(e)}")

        ttk.Button(buttons_frame, text="💾 حفظ", command=save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="❌ إلغاء", command=edit_window.destroy).pack(side=tk.LEFT)

    def update_account_document(self, data, new_doc, new_voucher, new_amount):
        """تحديث المستند في ملف الحسابات مع الحفاظ على التنسيق"""
        try:
            accounting_file = "accounting_system.xlsx"

            # فحص إمكانية الوصول للملف
            if not self.check_file_access(accounting_file):
                return False

            workbook = openpyxl.load_workbook(accounting_file)

            if data['account'] not in workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{data['account']}' غير موجود")
                return False

            ws = workbook[data['account']]

            print(f"🔄 بدء تحديث المستند في {data['account']}")
            print(f"📍 المواقع: {data['position']} - {data['doc_position']} - {data['voucher_position']}")
            print(f"🔄 القيم الجديدة: {new_doc} - {new_voucher} - {new_amount}")

            # تحديث البيانات مع الحفاظ على التنسيق
            self.update_cell_with_formatting(ws, data['position'], new_amount, 'amount')
            self.update_cell_with_formatting(ws, data['doc_position'], new_doc, 'document')
            self.update_cell_with_formatting(ws, data['voucher_position'], new_voucher, 'voucher')

            # حفظ الملف
            workbook.save(accounting_file)
            workbook.close()

            print(f"✅ تم تحديث المستند بنجاح")
            return True

        except Exception as e:
            print(f"❗ خطأ في تحديث المستند: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في تحديث المستند: {str(e)}")
            return False

    def check_file_access(self, file_path):
        """فحص إمكانية الوصول للملف للكتابة"""
        try:
            if not os.path.exists(file_path):
                messagebox.showerror("خطأ", f"الملف غير موجود: {file_path}")
                return False

            # محاولة فتح الملف للكتابة
            with open(file_path, 'r+b'):
                pass
            return True

        except PermissionError:
            messagebox.showerror("خطأ",
                               f"الملف مفتوح في تطبيق آخر (مثل Excel).\n"
                               f"الرجاء إغلاق الملف والمحاولة مرة أخرى.\n\n"
                               f"الملف: {file_path}")
            return False
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الوصول للملف: {str(e)}")
            return False

    def update_cell_with_formatting(self, ws, cell_address, value, cell_type):
        """تحديث خلية مع الحفاظ على التنسيق"""
        try:
            from openpyxl.styles import Font, Border, PatternFill, Alignment

            cell = ws[cell_address]

            # إنشاء نسخ من التنسيق الحالي لتجنب مشكلة StyleProxy
            try:
                original_font = Font(
                    name=cell.font.name,
                    size=cell.font.size,
                    bold=cell.font.bold,
                    italic=cell.font.italic,
                    color=cell.font.color
                )
            except:
                original_font = Font()

            try:
                original_border = Border(
                    left=cell.border.left,
                    right=cell.border.right,
                    top=cell.border.top,
                    bottom=cell.border.bottom
                )
            except:
                original_border = Border()

            try:
                original_fill = PatternFill(
                    fill_type=cell.fill.fill_type,
                    start_color=cell.fill.start_color,
                    end_color=cell.fill.end_color
                )
            except:
                original_fill = PatternFill()

            try:
                original_alignment = Alignment(
                    horizontal=cell.alignment.horizontal,
                    vertical=cell.alignment.vertical,
                    wrap_text=cell.alignment.wrap_text
                )
            except:
                original_alignment = Alignment()

            original_number_format = cell.number_format

            # تحديث القيمة
            if cell_type == 'amount':
                cell.value = float(value) if value else None
            else:
                cell.value = str(value) if value else None

            # استعادة التنسيق
            cell.font = original_font
            cell.border = original_border
            cell.fill = original_fill
            cell.alignment = original_alignment
            cell.number_format = original_number_format

            print(f"✅ تم تحديث الخلية {cell_address} بالقيمة: {value}")

        except Exception as e:
            print(f"❗ خطأ في تحديث الخلية {cell_address}: {str(e)}")
            # في حالة فشل التنسيق، قم بتحديث القيمة فقط
            try:
                cell = ws[cell_address]
                if cell_type == 'amount':
                    cell.value = float(value) if value else None
                    cell.number_format = '0.000'
                else:
                    cell.value = str(value) if value else None
                print(f"✅ تم تحديث الخلية {cell_address} بالقيمة فقط: {value}")
            except Exception as e2:
                print(f"❗ خطأ نهائي في تحديث الخلية {cell_address}: {str(e2)}")
                raise e2

    def show_edit_dialog(self, data):
        """عرض نافذة تعديل المستند"""
        edit_window = tk.Toplevel(self)
        edit_window.title("✏️ تعديل المستند")

        # حجم مناسب للنافذة
        edit_window.geometry("550x500")
        edit_window.minsize(500, 450)
        edit_window.configure(bg='#f0f8ff')
        edit_window.transient(self)
        edit_window.grab_set()
        edit_window.resizable(True, True)

        # توسيط النافذة
        edit_window.update_idletasks()
        width = edit_window.winfo_reqwidth()
        height = edit_window.winfo_reqheight()
        x = (edit_window.winfo_screenwidth() // 2) - (width // 2)
        y = (edit_window.winfo_screenheight() // 2) - (height // 2)
        edit_window.geometry(f"{width}x{height}+{x}+{y}")

        # الإطار الرئيسي
        main_frame = ttk.Frame(edit_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # تكوين الشبكة
        main_frame.grid_rowconfigure(3, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # عنوان
        title_label = ttk.Label(main_frame, text="✏️ تعديل بيانات المستند",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20), sticky='ew')

        # معلومات الحساب
        info_frame = ttk.LabelFrame(main_frame, text="📁 معلومات الحساب", padding="15")
        info_frame.grid(row=1, column=0, sticky='ew', pady=(0, 15))

        ttk.Label(info_frame, text=f"الحساب: {data['account']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"الموقع: {data['position']} - {data['section']} - الجدول {data['table']}").pack(anchor=tk.W)

        # حقول التعديل
        edit_frame = ttk.LabelFrame(main_frame, text="📝 البيانات الجديدة", padding="15")
        edit_frame.grid(row=2, column=0, sticky='ew', pady=(0, 15))

        # رقم المستند
        doc_frame = ttk.Frame(edit_frame)
        doc_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(doc_frame, text="📄 رقم المستند:",
                 font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        doc_var = tk.StringVar(value=str(data['document_num']) if data['document_num'] else "")
        doc_entry = ttk.Entry(doc_frame, textvariable=doc_var,
                             font=('Arial', 11), width=30)
        doc_entry.pack(fill=tk.X, pady=(5, 0))

        # رقم التأدية
        voucher_frame = ttk.Frame(edit_frame)
        voucher_frame.pack(fill=tk.X, pady=(10, 10))

        ttk.Label(voucher_frame, text="📎 رقم التأدية:",
                 font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        voucher_var = tk.StringVar(value=str(data['voucher_num']) if data['voucher_num'] else "")
        voucher_entry = ttk.Entry(voucher_frame, textvariable=voucher_var,
                                 font=('Arial', 11), width=30)
        voucher_entry.pack(fill=tk.X, pady=(5, 0))

        # قيمة المستند
        amount_frame = ttk.Frame(edit_frame)
        amount_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(amount_frame, text="💰 قيمة المستند:",
                 font=('Arial', 10, 'bold')).pack(anchor=tk.W)

        amount_input_frame = ttk.Frame(amount_frame)
        amount_input_frame.pack(fill=tk.X, pady=(5, 0))

        amount_var = tk.StringVar(value=f"{data['amount']:.3f}" if data['amount'] else "0.000")
        amount_entry = ttk.Entry(amount_input_frame, textvariable=amount_var,
                               font=('Arial', 11), width=20)
        amount_entry.pack(side=tk.LEFT)

        ttk.Label(amount_input_frame, text="دينار",
                 font=('Arial', 10)).pack(side=tk.LEFT, padx=(10, 0))

        # تلميح
        hint_label = ttk.Label(edit_frame,
                              text="💡 تلميح: يمكنك تعديل جميع الحقول",
                              font=('Arial', 9),
                              foreground='gray')
        hint_label.pack(anchor=tk.W, pady=(10, 0))

        # تركيز على حقل رقم المستند
        doc_entry.focus_set()
        doc_entry.select_range(0, tk.END)

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, sticky='ew', pady=(20, 0))

        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)

        def save_changes():
            try:
                new_doc = doc_var.get().strip()
                new_voucher = voucher_var.get().strip()
                new_amount_str = amount_var.get().strip()

                # فحص صحة البيانات
                if not new_amount_str:
                    messagebox.showwarning("تنبيه", "الرجاء إدخال قيمة المستند")
                    amount_entry.focus_set()
                    return

                try:
                    new_amount = float(new_amount_str)
                    if new_amount < 0:
                        messagebox.showwarning("تنبيه", "قيمة المستند يجب أن تكون موجبة")
                        amount_entry.focus_set()
                        return
                except ValueError:
                    messagebox.showerror("خطأ", "الرجاء إدخال قيمة رقمية صحيحة")
                    amount_entry.focus_set()
                    return

                # عرض معلومات التغيير
                changes_text = f"هل أنت متأكد من حفظ التغييرات؟\n\n"
                changes_text += f"رقم المستند: {data['document_num']} → {new_doc}\n"
                changes_text += f"رقم التأدية: {data['voucher_num']} → {new_voucher}\n"
                changes_text += f"قيمة المستند: {data['amount']:.3f} → {new_amount:.3f}"

                if not messagebox.askyesno("تأكيد التحديث", changes_text):
                    return

                # تنفيذ التحديث
                if self.update_account_document(data, new_doc, new_voucher, new_amount):
                    messagebox.showinfo("✅ نجح",
                                       f"تم تحديث المستند بنجاح!\n\n"
                                       f"رقم المستند: {new_doc}\n"
                                       f"رقم التأدية: {new_voucher}\n"
                                       f"قيمة المستند: {new_amount:.3f} دينار")
                    edit_window.destroy()
                    self.refresh_search()
                else:
                    messagebox.showerror("❌ خطأ", "فشل في تحديث المستند")

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ غير متوقع: {str(e)}")

        save_btn = ttk.Button(buttons_frame, text="💾 حفظ التغييرات",
                             command=save_changes,
                             style='Accent.TButton')
        save_btn.grid(row=0, column=0, padx=(0, 10), pady=10, sticky='ew')

        cancel_btn = ttk.Button(buttons_frame, text="❌ إلغاء",
                               command=edit_window.destroy)
        cancel_btn.grid(row=0, column=1, padx=(10, 0), pady=10, sticky='ew')

        # اختصارات لوحة المفاتيح
        save_btn.focus_set()
        edit_window.bind('<Return>', lambda e: save_changes())
        edit_window.bind('<Escape>', lambda e: edit_window.destroy())