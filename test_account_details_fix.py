#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة AccountDetailsDialog
"""

import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_account_details_fix():
    """اختبار إصلاح مشكلة AccountDetailsDialog"""
    print("🧪 اختبار إصلاح مشكلة AccountDetailsDialog")
    print("=" * 50)
    
    try:
        # حذف الملف إذا كان موجوداً
        if os.path.exists("accounting_system.xlsx"):
            os.remove("accounting_system.xlsx")
            print("🗑️ تم حذف الملف السابق")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        from manage_accounts import AccountDetailsDialog
        import tkinter as tk
        
        # إنشاء ExcelManager جديد
        print("📝 إنشاء ExcelManager جديد...")
        excel = ExcelManager()
        
        if excel.workbook:
            print("✅ تم إنشاء الملف بنجاح")
            
            # إنشاء حساب اختبار
            print("💼 إنشاء حساب اختبار...")
            result = excel.create_account_sheet("TEST001", "حساب اختبار", 5000.0)
            print(f"إنشاء الحساب: {'نجح' if result else 'فشل'}")
            
            if result:
                # إضافة مستندات
                print("📄 إضافة مستندات...")
                for i in range(3):
                    doc_result = excel.add_document("TEST001-حساب اختبار", 1000.0 * (i+1), f"DOC{i+1:03d}", f"PAY{i+1:03d}")
                    print(f"   مستند {i+1}: {'نجح' if doc_result else 'فشل'}")
                
                # حفظ الملف
                save_result = excel.save_workbook()
                print(f"حفظ الملف: {'نجح' if save_result else 'فشل'}")
                
                # اختبار الدوال الآمنة
                print("🔍 اختبار الدوال الآمنة...")
                
                # إنشاء نافذة وهمية للاختبار
                root = tk.Tk()
                root.withdraw()  # إخفاء النافذة
                
                # إنشاء AccountDetailsDialog
                print("🪟 إنشاء AccountDetailsDialog...")
                
                # اختبار إنشاء الكائن
                try:
                    details_dialog = AccountDetailsDialog(
                        parent=root,
                        excel=excel,
                        sheet_name="TEST001-حساب اختبار",
                        account_num="TEST001",
                        account_name="حساب اختبار"
                    )
                    
                    print("✅ تم إنشاء AccountDetailsDialog بنجاح")
                    
                    # اختبار الدالة الآمنة
                    ws = excel.workbook["TEST001-حساب اختبار"]
                    test_cell = ws['A9']  # خلية الرصيد الافتتاحي
                    
                    safe_value = details_dialog._safe_get_numeric_value(test_cell)
                    print(f"✅ الدالة الآمنة تعمل: {safe_value}")
                    
                    # اختبار خلية تحتوي على صيغة
                    sum_cell = ws['A33']  # خلية المجموع
                    sum_value = details_dialog._safe_get_numeric_value(sum_cell)
                    print(f"✅ معالجة الصيغ تعمل: {sum_value}")
                    
                    # إغلاق النافذة
                    details_dialog.destroy()
                    root.destroy()
                    
                    print("🎉 جميع الاختبارات نجحت!")
                    return True
                    
                except Exception as dialog_error:
                    print(f"❌ خطأ في AccountDetailsDialog: {str(dialog_error)}")
                    root.destroy()
                    return False
            else:
                print("❌ فشل في إنشاء الحساب")
                return False
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # تنظيف الملفات
        try:
            if os.path.exists("accounting_system.xlsx"):
                os.remove("accounting_system.xlsx")
                print("🧹 تم تنظيف ملف الاختبار")
        except:
            pass

def test_safe_functions():
    """اختبار الدوال الآمنة بشكل منفصل"""
    print("\n🔧 اختبار الدوال الآمنة")
    print("-" * 30)
    
    try:
        import tkinter as tk
        from excel_manager import ExcelManager
        from manage_accounts import AccountDetailsDialog
        
        # إنشاء ملف اختبار بسيط
        excel = ExcelManager()
        excel.create_account_sheet("SAFE001", "اختبار الدوال", 1000.0)
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء dialog للاختبار
        dialog = AccountDetailsDialog(
            parent=root,
            excel=excel,
            sheet_name="SAFE001-اختبار الدوال",
            account_num="SAFE001",
            account_name="اختبار الدوال"
        )
        
        # اختبار قيم مختلفة
        ws = excel.workbook["SAFE001-اختبار الدوال"]
        
        # اختبار 1: قيمة رقمية عادية
        ws['B1'].value = 100.5
        result1 = dialog._safe_get_numeric_value(ws['B1'])
        print(f"قيمة رقمية عادية: {result1}")
        
        # اختبار 2: قيمة نصية رقمية
        ws['B2'].value = "200.75"
        result2 = dialog._safe_get_numeric_value(ws['B2'])
        print(f"قيمة نصية رقمية: {result2}")
        
        # اختبار 3: قيمة فارغة
        ws['B3'].value = None
        result3 = dialog._safe_get_numeric_value(ws['B3'])
        print(f"قيمة فارغة: {result3}")
        
        # اختبار 4: نص غير رقمي
        ws['B4'].value = "نص غير رقمي"
        result4 = dialog._safe_get_numeric_value(ws['B4'])
        print(f"نص غير رقمي: {result4}")
        
        # اختبار 5: صيغة SUM
        ws['B5'].value = "=SUM(B1:B2)"
        result5 = dialog._safe_get_numeric_value(ws['B5'])
        print(f"صيغة SUM: {result5}")
        
        # تنظيف
        dialog.destroy()
        root.destroy()
        
        print("✅ جميع اختبارات الدوال الآمنة نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال الآمنة: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاح AccountDetailsDialog")
    print("=" * 60)
    
    # تشغيل الاختبارات
    test1_result = test_account_details_fix()
    test2_result = test_safe_functions()
    
    print("\n" + "=" * 60)
    print(f"🏁 نتائج الاختبارات:")
    print(f"   الاختبار الأساسي: {'✅ نجح' if test1_result else '❌ فشل'}")
    print(f"   اختبار الدوال الآمنة: {'✅ نجح' if test2_result else '❌ فشل'}")
    
    if test1_result and test2_result:
        print("\n🎉 جميع الاختبارات نجحت! مشكلة AccountDetailsDialog تم حلها.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الكود.")
    
    input("\nاضغط Enter للإغلاق...")
