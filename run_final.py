#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
السكريبت النهائي لتشغيل نظام إدارة المستندات المحاسبية
مع معالجة شاملة للأخطاء وحفظ تسجيل الدخول
"""

import sys
import traceback
import json
import os
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

# ملف حفظ معلومات تسجيل الدخول
LOGIN_SAVE_FILE = "saved_login.json"

class ErrorLogger:
    """مسجل الأخطاء المتقدم"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.start_time = datetime.now()
        
        # إنشاء مجلد السجلات
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # ملف السجل
        self.log_file = f"logs/final_run_{self.start_time.strftime('%Y%m%d_%H%M%S')}.log"
    
    def log(self, level, message, details=None):
        """تسجيل رسالة"""
        timestamp = datetime.now()
        log_entry = {
            'time': timestamp,
            'level': level,
            'message': message,
            'details': details
        }
        
        # حفظ في الذاكرة
        if level == 'ERROR':
            self.errors.append(log_entry)
        elif level == 'WARNING':
            self.warnings.append(log_entry)
        
        # عرض فوري
        time_str = timestamp.strftime('%H:%M:%S')
        
        if level == 'ERROR':
            print(f"🚨 [{time_str}] خطأ: {message}")
            if details:
                print(f"   التفاصيل: {details}")
        elif level == 'WARNING':
            print(f"⚠️ [{time_str}] تحذير: {message}")
        elif level == 'INFO':
            print(f"ℹ️ [{time_str}] {message}")
        elif level == 'SUCCESS':
            print(f"✅ [{time_str}] {message}")
        
        # حفظ في الملف
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {level}: {message}\n")
                if details:
                    f.write(f"التفاصيل: {details}\n")
                f.write("-" * 50 + "\n")
        except:
            pass  # تجاهل أخطاء الكتابة في الملف
    
    def error(self, message, details=None):
        """تسجيل خطأ"""
        self.log('ERROR', message, details)
        
        # اقتراح حلول
        self.suggest_solution(message)
    
    def warning(self, message):
        """تسجيل تحذير"""
        self.log('WARNING', message)
    
    def info(self, message):
        """تسجيل معلومات"""
        self.log('INFO', message)
    
    def success(self, message):
        """تسجيل نجاح"""
        self.log('SUCCESS', message)
    
    def suggest_solution(self, error_message):
        """اقتراح حلول للأخطاء"""
        print("💡 اقتراحات للحل:")
        
        if 'import' in error_message.lower():
            print("   📦 تحقق من تثبيت المكتبات المطلوبة")
            print("   📁 تأكد من وجود جميع ملفات المشروع")
        
        elif 'file' in error_message.lower() or 'excel' in error_message.lower():
            print("   📊 تأكد من وجود ملف accounting_system.xlsx")
            print("   🔒 أغلق ملف Excel إذا كان مفتوحاً")
            print("   📁 تحقق من صلاحيات الملف")
        
        elif 'account' in error_message.lower():
            print("   💼 تحقق من وجود الحساب في ملف Excel")
            print("   🔍 تأكد من صحة اسم الحساب")
            print("   📊 تحقق من بنية ملف Excel")
        
        else:
            print("   🔄 أعد تشغيل التطبيق")
            print("   💻 أعد تشغيل الكمبيوتر")
            print("   📞 اتصل بالدعم الفني")
    
    def get_summary(self):
        """الحصول على ملخص الجلسة"""
        duration = datetime.now() - self.start_time
        return {
            'duration': duration,
            'errors': len(self.errors),
            'warnings': len(self.warnings),
            'log_file': self.log_file
        }

class LoginManager:
    """مدير تسجيل الدخول"""
    
    def __init__(self, logger):
        self.logger = logger
        self.login_file = LOGIN_SAVE_FILE
    
    def load_saved_login(self):
        """تحميل معلومات تسجيل الدخول المحفوظة"""
        try:
            if os.path.exists(self.login_file):
                with open(self.login_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if data.get("auto_login", False):
                    self.logger.success(f"تم العثور على معلومات تسجيل دخول محفوظة: {data['username']}")
                    return data
            
            self.logger.info("لا توجد معلومات تسجيل دخول محفوظة")
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل معلومات تسجيل الدخول: {str(e)}")
            return None
    
    def save_login_info(self, username):
        """حفظ معلومات تسجيل الدخول"""
        try:
            data = {
                "username": username,
                "last_login": datetime.now().isoformat(),
                "auto_login": True
            }
            
            with open(self.login_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.success(f"تم حفظ معلومات تسجيل الدخول: {username}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ معلومات تسجيل الدخول: {str(e)}")
            return False

def check_requirements(logger):
    """فحص المتطلبات الأساسية"""
    logger.info("فحص المتطلبات الأساسية...")
    
    # فحص الملفات
    required_files = [
        'app.py', 'excel_manager.py', 'manage_accounts.py',
        'user_manager.py', 'document_window.py', 'search_window.py',
        'accounting_system.xlsx'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            logger.success(f"ملف {file} موجود")
        else:
            logger.error(f"ملف {file} غير موجود")
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    # فحص الاستيرادات
    imports_to_test = [
        ('tkinter', 'import tkinter as tk'),
        ('openpyxl', 'import openpyxl'),
        ('app', 'from app import AccountingApp')
    ]
    
    for name, import_cmd in imports_to_test:
        try:
            exec(import_cmd)
            logger.success(f"استيراد {name} نجح")
        except Exception as e:
            logger.error(f"فشل استيراد {name}: {str(e)}")
            return False
    
    logger.success("تم فحص جميع المتطلبات بنجاح")
    return True

def create_app_with_auto_login(logger, login_manager):
    """إنشاء التطبيق مع تسجيل دخول تلقائي"""
    try:
        logger.info("إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        
        # إعداد معالج الأخطاء العام
        def global_exception_handler(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                logger.info("تم إيقاف التطبيق بواسطة المستخدم")
                return
            
            error_details = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            logger.error(f"{exc_type.__name__}: {str(exc_value)}", error_details)
        
        sys.excepthook = global_exception_handler
        
        logger.info("استيراد وإنشاء التطبيق...")
        from app import AccountingApp
        
        # تحميل معلومات تسجيل الدخول
        saved_login = login_manager.load_saved_login()
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        logger.success("تم إنشاء التطبيق بنجاح")
        
        if saved_login:
            # تسجيل دخول تلقائي
            logger.info(f"تسجيل دخول تلقائي: {saved_login['username']}")
            
            # تعيين المستخدم
            if hasattr(app, 'user_manager'):
                app.user_manager.current_user = {
                    'username': saved_login['username'],
                    'role': 'admin',
                    'permissions': ['add_account', 'delete_account', 'add_document', 'view_reports', 'manage_users']
                }
            
            # إغلاق نافذة تسجيل الدخول
            if hasattr(app, 'login_window') and app.login_window:
                try:
                    if hasattr(app.login_window, 'root'):
                        app.login_window.root.destroy()
                    logger.success("تم إغلاق نافذة تسجيل الدخول")
                except Exception as e:
                    logger.warning(f"خطأ في إغلاق نافذة تسجيل الدخول: {e}")
            
            # إظهار النافذة الرئيسية
            app.root.deiconify()
            
            # إعداد الواجهة
            if hasattr(app, 'setup_modern_ui'):
                logger.info("إعداد الواجهة الحديثة...")
                app.setup_modern_ui()
            
            # بدء التحديث التلقائي
            if hasattr(app, 'start_auto_refresh'):
                try:
                    app.start_auto_refresh()
                    logger.success("تم بدء التحديث التلقائي")
                except:
                    logger.warning("لم يتم بدء التحديث التلقائي")
            
            # تحديث عنوان النافذة
            app.root.title(f"نظام إدارة المستندات المحاسبية - {saved_login['username']} (تسجيل دخول تلقائي)")
            
        else:
            # تسجيل دخول عادي مع حفظ المعلومات
            logger.info("عرض نافذة تسجيل الدخول العادية")
            
            # تحسين دالة تسجيل الدخول
            original_on_login_success = app.on_login_success
            
            def enhanced_login_success():
                try:
                    # حفظ معلومات تسجيل الدخول
                    if hasattr(app, 'user_manager') and app.user_manager.current_user:
                        username = app.user_manager.current_user.get('username', 'admin')
                        login_manager.save_login_info(username)
                    
                    # استدعاء الدالة الأصلية
                    original_on_login_success()
                    
                except Exception as e:
                    logger.error(f"خطأ في تسجيل الدخول: {str(e)}")
            
            app.on_login_success = enhanced_login_success
        
        return app, root
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء التطبيق: {str(e)}", traceback.format_exc())
        return None, None

def main():
    """الدالة الرئيسية"""
    
    print("=" * 80)
    print("🚀 نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية")
    print("=" * 80)
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🛡️ معالجة شاملة للأخطاء مفعلة")
    print("💾 حفظ تلقائي لمعلومات تسجيل الدخول")
    print("📝 تسجيل مفصل للأحداث والأخطاء")
    print("=" * 80)
    
    # إنشاء مسجل الأخطاء
    logger = ErrorLogger()
    logger.success("تم بدء النظام")
    
    # إنشاء مدير تسجيل الدخول
    login_manager = LoginManager(logger)
    
    try:
        # فحص المتطلبات
        if not check_requirements(logger):
            logger.error("فشل في فحص المتطلبات الأساسية")
            return
        
        # إنشاء وتشغيل التطبيق
        app, root = create_app_with_auto_login(logger, login_manager)
        
        if app and root:
            logger.success("بدء تشغيل التطبيق...")
            logger.info("التطبيق يعمل الآن - يمكنك استخدامه")
            logger.info("راقب هذه النافذة لأي أخطاء أو تحديثات")
            
            # تشغيل التطبيق
            root.mainloop()
        else:
            logger.error("فشل في إنشاء التطبيق")
    
    except KeyboardInterrupt:
        logger.info("تم إيقاف التطبيق بواسطة المستخدم")
    
    except Exception as e:
        logger.error(f"خطأ عام في التشغيل: {str(e)}", traceback.format_exc())
    
    finally:
        # عرض ملخص الجلسة
        summary = logger.get_summary()
        
        print(f"\n{'=' * 80}")
        print("📊 ملخص جلسة التشغيل:")
        print(f"⏱️ مدة التشغيل: {summary['duration']}")
        print(f"🚨 عدد الأخطاء: {summary['errors']}")
        print(f"⚠️ عدد التحذيرات: {summary['warnings']}")
        print(f"📁 ملف السجل: {summary['log_file']}")
        
        if summary['errors'] > 0:
            print("\n📋 آخر الأخطاء:")
            for error in logger.errors[-3:]:  # آخر 3 أخطاء
                print(f"   [{error['time'].strftime('%H:%M:%S')}] {error['message']}")
        
        print(f"\n🔚 انتهاء التشغيل - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

if __name__ == "__main__":
    main()
