#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لنافذة تفاصيل الحساب
"""

import sys
import traceback
import tkinter as tk
from tkinter import messagebox

def main():
    """اختبار نافذة تفاصيل الحساب"""
    
    print("🧪 اختبار نافذة تفاصيل الحساب")
    print("=" * 50)
    
    try:
        # استيراد الوحدات
        print("📦 استيراد الوحدات...")
        from excel_manager import ExcelManager
        from manage_accounts import ManageAccountsDialog
        
        print("✅ تم استيراد الوحدات بنجاح")
        
        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        root.title("اختبار إدارة الحسابات")
        root.geometry("800x600")
        
        # إنشاء مدير Excel
        print("📊 إنشاء مدير Excel...")
        excel_manager = ExcelManager()
        
        # تحميل ملف Excel
        print("📁 تحميل ملف Excel...")
        if excel_manager.load_workbook():
            print("✅ تم تحميل ملف Excel بنجاح")
            
            # إنشاء نافذة إدارة الحسابات
            print("🏗️ إنشاء نافذة إدارة الحسابات...")
            manage_dialog = ManageAccountsDialog(root, excel_manager)
            
            print("✅ تم إنشاء نافذة إدارة الحسابات بنجاح")
            print("📋 يمكنك الآن اختيار حساب وعرض تفاصيله")
            print("-" * 50)
            
            # تشغيل التطبيق
            root.mainloop()
            
        else:
            print("❌ فشل في تحميل ملف Excel")
            messagebox.showerror("خطأ", "فشل في تحميل ملف Excel\nتأكد من وجود ملف accounting_system.xlsx")
    
    except ImportError as e:
        error_msg = f"خطأ في الاستيراد: {str(e)}"
        print(f"🚨 {error_msg}")
        print("💡 تأكد من وجود الملفات:")
        print("   - excel_manager.py")
        print("   - manage_accounts.py")
        
    except Exception as e:
        error_msg = f"خطأ عام: {str(e)}"
        print(f"🚨 {error_msg}")
        print(f"📋 التفاصيل:\n{traceback.format_exc()}")
        
        # اقتراح حلول
        print("\n💡 اقتراحات للحل:")
        if "Excel" in str(e):
            print("   - تأكد من وجود ملف accounting_system.xlsx")
            print("   - أغلق ملف Excel إذا كان مفتوحاً")
        elif "AttributeError" in str(e):
            print("   - تحقق من سلامة ملفات الكود")
            print("   - أعد تشغيل التطبيق")
        else:
            print("   - أعد تشغيل الكمبيوتر")
            print("   - تحقق من ملفات النظام")

if __name__ == "__main__":
    main()
