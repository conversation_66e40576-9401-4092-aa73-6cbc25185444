#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تسجيل الدخول
"""

import tkinter as tk
from user_manager import UserManager, LoginWindow

def test_login():
    """اختبار نافذة تسجيل الدخول"""
    print("🚀 اختبار نظام تسجيل الدخول")

    # إنشاء مدير المستخدمين
    user_manager = UserManager()

    def on_success():
        print("✅ تم تسجيل الدخول بنجاح!")
        user_info = user_manager.get_user_info()
        if user_info:
            print(f"👤 المستخدم: {user_info['full_name']}")
            print(f"🔑 الدور: {user_info['role']}")
            print(f"📋 الصلاحيات: {list(user_info['permissions'].keys())}")

    # إنشاء نافذة تسجيل الدخول
    login_window = LoginWindow(user_manager, on_success)

    print("📝 معلومات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin")

    # تشغيل النافذة
    login_window.root.mainloop()

if __name__ == "__main__":
    test_login()
