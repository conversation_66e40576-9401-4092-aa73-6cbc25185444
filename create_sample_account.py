#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حساب تجريبي بالتنسيق الجديد
"""

import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from datetime import datetime

def create_sample_account():
    """إنشاء حساب تجريبي بالتنسيق الجديد"""

    # إنشاء ملف Excel جديد
    wb = openpyxl.Workbook()
    if 'Sheet' in wb.sheetnames:
        wb.remove(wb['Sheet'])

    # بيانات الحساب التجريبي
    account_num = "1001"
    account_name = "حساب الأدوية والمستلزمات الطبية"
    initial_balance = 5000.000
    sheet_name = f"{account_num}-{account_name}"

    # إنشاء الورقة
    ws = wb.create_sheet(sheet_name)
    ws.sheet_properties.rightToLeft = True

    # تطبيق التنسيق الجديد
    setup_new_format(ws, account_num, account_name, initial_balance)

    # حفظ الملف
    sample_file = "sample_new_format.xlsx"
    wb.save(sample_file)
    print(f"✅ تم إنشاء الملف التجريبي: {sample_file}")

    return sample_file

def setup_new_format(ws, account_num, account_name, initial_balance):
    """تطبيق التنسيق المحدث"""

    # تعريف الحدود
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # الصف 1: وزارة / الدائرة
    ws.merge_cells('A1:R1')
    ws['A1'] = "وزارة / الدائرة : وزارة الصحة"
    ws['A1'].font = Font(size=12, bold=False)
    ws['A1'].alignment = Alignment(horizontal='left')

    # الصف 2: الشهر والمركز
    ws.merge_cells('A2:I2')
    ws['A2'] = "الشهر ="
    ws['A2'].font = Font(size=11)
    ws['A2'].alignment = Alignment(horizontal='left')

    ws.merge_cells('J2:R2')
    ws['J2'] = "مركز : التأمين الصحي"
    ws['J2'].font = Font(size=11)
    ws['J2'].alignment = Alignment(horizontal='right')

    # الصف 3: فارغ

    # الصف 4: رقم الحساب (1/214)
    ws.merge_cells('A4:R4')
    ws['A4'] = "1/214"
    ws['A4'].font = Font(bold=True)
    ws['A4'].alignment = Alignment(horizontal='center')

    # الصف 5: عناوين الأعمدة الأولى
    for i in range(6):
        col = chr(65 + (i * 3))  # A, D, G, J, M, P

        ws[f'{col}5'] = "فلس/دينار"
        ws[f'{chr(ord(col) + 1)}5'] = "مستند"
        ws[f'{chr(ord(col) + 2)}5'] = "رقم"

        # تنسيق العناوين
        for j in range(3):
            col_letter = chr(ord(col) + j)
            cell = ws[f'{col_letter}5']
            cell.font = Font(bold=True, size=9)
            cell.alignment = Alignment(horizontal='center')
            cell.border = thin_border

    # الصف 6: عناوين الأعمدة الثانية
    for i in range(6):
        col = chr(65 + (i * 3))  # A, D, G, J, M, P

        ws[f'{chr(ord(col) + 1)}6'] = "الصرف"
        ws[f'{chr(ord(col) + 2)}6'] = "التأدية"

        # تنسيق العناوين
        for j in range(1, 3):  # العمودين الثاني والثالث فقط
            col_letter = chr(ord(col) + j)
            cell = ws[f'{col_letter}6']
            cell.font = Font(bold=True, size=9)
            cell.alignment = Alignment(horizontal='center')
            cell.border = thin_border

    # الصف 7: الرصيد الافتتاحي والترحيل
    for i in range(6):
        col = chr(65 + (i * 3))  # A, D, G, J, M, P

        if i == 0:
            # القسم الأول: الرصيد الافتتاحي
            ws[f'{col}7'] = initial_balance if initial_balance != 0 else 0
            ws[f'{chr(ord(col) + 1)}7'] = "الرصيد"
            ws[f'{chr(ord(col) + 2)}7'] = ""
        else:
            # الأقسام الأخرى: صيغة SUM من القسم السابق
            prev_col = chr(ord(col) - 3)
            ws[f'{col}7'] = f"=SUM({prev_col}28)"
            ws[f'{chr(ord(col) + 1)}7'] = "ما قبله"
            ws[f'{chr(ord(col) + 2)}7'] = ""

        # تنسيق صف الرصيد الافتتاحي
        for j in range(3):
            col_letter = chr(ord(col) + j)
            cell = ws[f'{col_letter}7']
            cell.border = thin_border
            cell.alignment = Alignment(horizontal='center')

    # إعداد صفوف البيانات (الصفوف 8-27 = 20 صف)
    for i in range(6):
        col = chr(65 + (i * 3))  # A, D, G, J, M, P

        for row in range(8, 28):  # 20 صف للبيانات (من 8 إلى 27)
            for j in range(3):
                col_letter = chr(ord(col) + j)
                cell = ws[f'{col_letter}{row}']
                cell.border = thin_border
                cell.alignment = Alignment(horizontal='center')

    # الصف 28: صف المجاميع (حسب التنسيق المحدث)
    for i in range(6):
        col = chr(65 + (i * 3))  # A, D, G, J, M, P

        # صيغة المجموع حسب التنسيق الجديد
        if i == 0:
            # القسم الأول: =SUM(A7:A27)
            ws[f'{col}28'] = f"=SUM({col}7:{col}27)"
        else:
            # الأقسام الأخرى: =SUM(D8:D27) إلخ
            ws[f'{col}28'] = f"=SUM({col}8:{col}27)"

        ws[f'{col}28'].font = Font(bold=True)
        ws[f'{col}28'].number_format = '#,##0.000'
        ws[f'{col}28'].border = Border(
            top=Side(style='double'),
            bottom=Side(style='double'),
            left=Side(style='thin'),
            right=Side(style='thin')
        )
        ws[f'{col}28'].fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")

        # تنسيق الخلايا المجاورة
        for j in range(1, 3):
            col_letter = chr(ord(col) + j)
            cell = ws[f'{col_letter}28']
            cell.border = thin_border
            cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")

    # الصف 30: التاريخ وتوقيع المحاسب (حسب التنسيق المحدث)
    ws.merge_cells('A30:I30')
    ws['A30'] = "التاريخ"
    ws['A30'].font = Font(size=11)
    ws['A30'].alignment = Alignment(horizontal='left')

    ws.merge_cells('J30:R30')
    ws['J30'] = "اسم وتوقيع المحاسب"
    ws['J30'].font = Font(size=11)
    ws['J30'].alignment = Alignment(horizontal='right')

    # إضافة خط للتوقيع
    ws.merge_cells('J31:R31')
    ws['J31'] = "_" * 30
    ws['J31'].alignment = Alignment(horizontal='center')

    print("✅ تم تطبيق التنسيق الجديد")

if __name__ == "__main__":
    create_sample_account()
