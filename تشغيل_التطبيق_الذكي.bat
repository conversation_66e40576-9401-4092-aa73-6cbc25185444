@echo off
chcp 65001 >nul
title Smart Accounting System Launcher

echo ========================================
echo    Smart Accounting System Launcher
echo    Ministry of Health - Jordan
echo    Auto Library Management
echo ========================================
echo.

REM Set environment
set PYTHONIOENCODING=utf-8
set PYTHONPATH=%CD%;%CD%\libraries

echo 🚀 Starting Smart Launcher...
echo.

REM Find Python
set PYTHON_CMD=

py -c "print('test')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python: py
    set "PYTHON_CMD=py"
    goto :smart_check
)

python -c "print('test')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python: python
    set "PYTHON_CMD=python"
    goto :smart_check
)

set PYTHON_PATH=C:\Users\<USER>\Downloads\python-3.13.2-embed-amd64\python.exe
if exist "%PYTHON_PATH%" (
    echo ✅ Python: embedded
    set "PYTHON_CMD=%PYTHON_PATH%"
    goto :smart_check
)

echo ❌ Python not found!
pause
exit /b 1

:smart_check
echo.
echo 🧠 Smart Library Check...

REM Smart library management
if exist "libraries\install_requirements.py" (
    "%PYTHON_CMD%" libraries\install_requirements.py
    if %errorlevel% equ 0 (
        echo ✅ Libraries ready
        goto :run_app
    )
)

REM Fallback: Quick install essentials
echo 📦 Quick install mode...
"%PYTHON_CMD%" -m pip install openpyxl --quiet >nul 2>&1
"%PYTHON_CMD%" -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ GUI not available
    pause
    exit /b 1
)

:run_app
echo.
echo 🚀 Launching Application...
echo.

if exist "launcher.py" (
    echo 🔑 Login: admin/admin
    "%PYTHON_CMD%" launcher.py
) else if exist "app.py" (
    echo 🔑 Login: admin/admin
    "%PYTHON_CMD%" app.py
) else (
    echo ❌ App not found!
    pause
    exit /b 1
)

echo.
echo 👋 Goodbye!
pause
