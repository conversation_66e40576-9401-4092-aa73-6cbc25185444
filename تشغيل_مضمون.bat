@echo off
chcp 65001 >nul
title Accounting System - Guaranteed Working

echo ========================================
echo    Accounting System - Guaranteed
echo    Ministry of Health - Jordan
echo ========================================
echo.

echo 🚀 Starting system...
echo.

REM Set environment
set PYTHONIOENCODING=utf-8
set PYTHONPATH=%CD%

REM Find Python (same logic as working file)
set PYTHON_CMD=

py -c "print('test')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python: py
    set "PYTHON_CMD=py"
    goto :check_libs
)

python -c "print('test')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python: python
    set "PYTHON_CMD=python"
    goto :check_libs
)

set PYTHON_PATH=C:\Users\<USER>\Downloads\python-3.13.2-embed-amd64\python.exe
if exist "%PYTHON_PATH%" (
    echo ✅ Python: embedded
    set "PYTHON_CMD=%PYTHON_PATH%"
    goto :check_libs
)

echo ❌ Python not found!
pause
exit /b 1

:check_libs
echo.
echo 📦 Checking libraries...

REM Check openpyxl (essential)
"%PYTHON_CMD%" -c "import openpyxl" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 Installing openpyxl...
    "%PYTHON_CMD%" -m pip install openpyxl --quiet
    if %errorlevel% equ 0 (
        echo ✅ openpyxl ready
    ) else (
        echo ⚠️ openpyxl install failed - continuing anyway
    )
) else (
    echo ✅ openpyxl OK
)

REM Check tkinter (required)
"%PYTHON_CMD%" -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ tkinter missing!
    pause
    exit /b 1
) else (
    echo ✅ tkinter OK
)

REM Optional libraries (install if possible, ignore if failed)
"%PYTHON_CMD%" -c "import ttkthemes" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 Installing ttkthemes...
    "%PYTHON_CMD%" -m pip install ttkthemes --quiet >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ ttkthemes ready
    )
) else (
    echo ✅ ttkthemes OK
)

"%PYTHON_CMD%" -c "import PIL" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 Installing Pillow...
    "%PYTHON_CMD%" -m pip install Pillow --quiet >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Pillow ready
    )
) else (
    echo ✅ Pillow OK
)

echo.
echo ✅ Library check completed
echo.
echo 🚀 Starting application...
echo.

REM Run application
if exist "launcher.py" (
    echo 🔄 Running launcher.py...
    echo.
    echo 🔑 Login: admin / admin
    echo.
    "%PYTHON_CMD%" launcher.py
) else if exist "app.py" (
    echo 🔄 Running app.py...
    echo.
    echo 🔑 Login: admin / admin
    echo.
    "%PYTHON_CMD%" app.py
) else (
    echo ❌ No app file found!
    dir *.py /b
    pause
    exit /b 1
)

echo.
echo 👋 System closed
pause
