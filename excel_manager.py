import openpyxl
from openpyxl.styles import Font, <PERSON>gn<PERSON>, PatternFill, Border, Side
from datetime import datetime
import os
from tkinter import messagebox

class ExcelManager:
    def __init__(self):
        self.current_file = "accounting_system.xlsx"
        self.file_path = self.current_file  # إضافة خاصية file_path
        self.workbook = None

        # قائمة الملفات المطلوبة
        self.required_files = {
            "accounting_system.xlsx": "نظام المحاسبة الرئيسي",
            "Accounting system deductions.xlsx": "نظام الخصومات المحاسبية"
        }

        # تهيئة جميع الملفات المطلوبة
        self.initialize_all_files()

        # تحميل الملف الرئيسي
        self.load_or_create_workbook()

    def initialize_all_files(self):
        """تهيئة جميع الملفات المطلوبة"""
        print("📁 تهيئة جميع ملفات Excel المطلوبة...")

        for filename, description in self.required_files.items():
            try:
                print(f"🔍 فحص ملف: {filename} ({description})")

                if not os.path.exists(filename):
                    print(f"📝 إنشاء ملف جديد: {filename}")
                    self._create_individual_file(filename, description)
                else:
                    print(f"✅ الملف موجود: {filename}")

                    # فحص سلامة الملف
                    if not self._verify_file_integrity(filename):
                        print(f"⚠️ الملف تالف - إعادة إنشاء: {filename}")
                        self._create_individual_file(filename, description)

            except Exception as e:
                print(f"❌ خطأ في تهيئة {filename}: {str(e)}")
                messagebox.showerror("خطأ في التهيئة",
                                   f"فشل في تهيئة {description}:\n{str(e)}")

        print("✅ تم إنجاز تهيئة جميع الملفات")

    def _create_individual_file(self, filename, description):
        """إنشاء ملف فردي"""
        try:
            print(f"📝 إنشاء {description}...")

            # إنشاء ملف جديد
            new_workbook = openpyxl.Workbook()

            # إزالة الورقة الافتراضية
            if 'Sheet' in new_workbook.sheetnames:
                new_workbook.remove(new_workbook['Sheet'])

            # إضافة ورقة ترحيب مخصصة
            welcome_sheet = new_workbook.create_sheet("مرحباً")
            self._setup_welcome_sheet(welcome_sheet, filename, description)

            # حفظ الملف
            new_workbook.save(filename)
            print(f"✅ تم إنشاء {description} بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء {filename}: {str(e)}")
            raise

    def _setup_welcome_sheet(self, sheet, filename, description):
        """إعداد ورقة الترحيب حسب نوع الملف"""
        try:
            if "deductions" in filename.lower():
                # ورقة ترحيب للخصومات
                sheet['A1'] = "مرحباً بك في نظام إدارة الخصومات المحاسبية"
                sheet['A2'] = "وزارة الصحة الأردنية - قسم الخصومات والاستقطاعات"
                sheet['A5'] = "لبدء الاستخدام:"
                sheet['A6'] = "1. اذهب إلى قائمة 'الخصومات' واختر 'إضافة خصم جديد'"
                sheet['A7'] = "2. أدخل تفاصيل الخصم والمبلغ"
                sheet['A8'] = "3. ابدأ بإدارة الخصومات والاستقطاعات"
            else:
                # ورقة ترحيب للنظام الرئيسي
                sheet['A1'] = "مرحباً بك في نظام إدارة المستندات المحاسبية"
                sheet['A2'] = "وزارة الصحة الأردنية - النظام الرئيسي"
                sheet['A5'] = "لبدء الاستخدام:"
                sheet['A6'] = "1. اذهب إلى قائمة 'الحسابات' واختر 'إضافة حساب جديد'"
                sheet['A7'] = "2. أدخل رقم الحساب واسمه والرصيد الافتتاحي"
                sheet['A8'] = "3. ابدأ بإضافة المستندات للحساب"

            sheet['A3'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
            sheet['A4'] = f"نوع الملف: {description}"

            # تنسيق الورقة
            title_font = Font(size=14, bold=True)
            sheet['A1'].font = title_font
            sheet['A2'].font = Font(size=12, bold=True)

        except Exception as e:
            print(f"❌ خطأ في إعداد ورقة الترحيب: {str(e)}")

    def _verify_file_integrity(self, filename):
        """فحص سلامة الملف"""
        try:
            # فحص حجم الملف
            if os.path.getsize(filename) < 1024:  # أقل من 1KB
                return False

            # محاولة تحميل الملف
            test_workbook = openpyxl.load_workbook(filename)

            # فحص وجود أوراق
            if not test_workbook.sheetnames:
                return False

            return True

        except Exception:
            return False

    def load_or_create_workbook(self):
        """تحميل الملف الموجود أو إنشاء ملف جديد"""
        print("🔍 فحص وجود ملف البيانات...")

        # فحص وجود الملف المخصص لحفظ البيانات
        if os.path.exists(self.current_file):
            print(f"✅ تم العثور على ملف البيانات: {self.current_file}")

            # فحص حجم الملف للتأكد من أنه ليس فارغاً
            file_size = os.path.getsize(self.current_file)
            print(f"📊 حجم الملف: {file_size} بايت")

            if file_size < 1024:  # إذا كان الملف أقل من 1KB فهو على الأرجح تالف
                print("⚠️ الملف صغير جداً أو فارغ - سيتم إنشاء ملف جديد")
                self._backup_and_create_new()
                return

            # محاولة تحميل الملف الموجود
            try:
                print(f"🔄 محاولة تحميل البيانات من الملف...")
                self.workbook = openpyxl.load_workbook(self.current_file)

                # التحقق من سلامة الملف
                if self.workbook and self.workbook.sheetnames:
                    print(f"✅ تم تحميل البيانات بنجاح!")
                    print(f"📊 عدد الصفحات المحملة: {len(self.workbook.sheetnames)}")

                    # عد الحسابات (استثناء الصفحات الخاصة)
                    accounts_count = 0
                    special_sheets = ['مرحباً', 'التقارير', 'تقرير المستندات', 'التقرير الإجمالي']

                    for sheet_name in self.workbook.sheetnames:
                        if sheet_name not in special_sheets:
                            accounts_count += 1
                            print(f"💼 حساب محمل: {sheet_name}")

                    if accounts_count > 0:
                        print(f"📊 إجمالي الحسابات المحملة: {accounts_count}")
                    else:
                        print("📝 لا توجد حسابات في الملف - ملف جديد")

                    return  # تم تحميل الملف بنجاح
                else:
                    print("⚠️ الملف فارغ أو لا يحتوي على صفحات")
                    self._backup_and_create_new()

            except openpyxl.utils.exceptions.InvalidFileException as e:
                print(f"❌ الملف تالف أو غير صالح: {str(e)}")
                self._backup_and_create_new()

            except PermissionError as e:
                print(f"❌ لا يمكن الوصول إلى الملف - قد يكون مفتوحاً في برنامج آخر: {str(e)}")
                messagebox.showerror("خطأ في الوصول",
                                   f"لا يمكن فتح الملف {self.current_file}\n\nقد يكون مفتوحاً في Excel أو برنامج آخر.\nالرجاء إغلاق البرنامج وإعادة المحاولة.")
                # إنشاء ملف جديد باسم مختلف
                self.current_file = f"accounting_system_new_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                self.file_path = self.current_file
                print(f"📝 إنشاء ملف جديد باسم: {self.current_file}")
                self.create_new_workbook()

            except Exception as e:
                print(f"❌ خطأ غير متوقع في تحميل الملف: {str(e)}")
                self._backup_and_create_new()
        else:
            # الملف غير موجود - إنشاء ملف جديد
            print(f"📝 لم يتم العثور على ملف البيانات: {self.current_file}")
            print("🆕 سيتم إنشاء ملف جديد لحفظ البيانات")
            self.create_new_workbook()

    def _backup_and_create_new(self):
        """إنشاء نسخة احتياطية وإنشاء ملف جديد"""
        try:
            # إنشاء نسخة احتياطية من الملف التالف
            import shutil
            backup_name = f"{self.current_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(self.current_file, backup_name)
            print(f"💾 تم إنشاء نسخة احتياطية: {backup_name}")
        except Exception as backup_error:
            print(f"⚠️ فشل في إنشاء النسخة الاحتياطية: {str(backup_error)}")

        print("🔧 إنشاء ملف جديد بدلاً من الملف التالف")
        self.create_new_workbook()

    def create_new_workbook(self):
        """إنشاء ملف Excel جديد"""
        print("📝 إنشاء ملف Excel جديد...")
        self.workbook = openpyxl.Workbook()

        # إزالة الورقة الافتراضية إذا كانت موجودة
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])

        # إنشاء ورقة ترحيب أولية لضمان وجود ورقة واحدة على الأقل
        welcome_sheet = self.workbook.create_sheet("مرحباً")
        welcome_sheet['A1'] = "مرحباً بك في نظام إدارة المستندات المحاسبية"
        welcome_sheet['A2'] = "وزارة الصحة الأردنية"
        welcome_sheet['A3'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
        welcome_sheet['A5'] = "لبدء الاستخدام:"
        welcome_sheet['A6'] = "1. اذهب إلى قائمة 'الحسابات' واختر 'إضافة حساب جديد'"
        welcome_sheet['A7'] = "2. أدخل رقم الحساب واسمه والرصيد الافتتاحي"
        welcome_sheet['A8'] = "3. ابدأ بإضافة المستندات للحساب"

        # تنسيق الورقة
        from openpyxl.styles import Font, Alignment
        title_font = Font(size=14, bold=True)
        welcome_sheet['A1'].font = title_font
        welcome_sheet['A2'].font = Font(size=12, bold=True)

        for row in range(1, 9):
            welcome_sheet[f'A{row}'].alignment = Alignment(horizontal='right')

        print("✅ تم إنشاء ملف جديد مع ورقة ترحيب")
        self.save_workbook()

    def save_workbook(self, file_path=None):
        """حفظ الملف"""
        try:
            save_path = file_path or self.current_file
            print(f"💾 محاولة حفظ الملف: {save_path}")

            # التأكد من وجود الملف العامل
            if self.workbook is None:
                print("❌ لا يوجد ملف عامل لحفظه")
                return False

            # حفظ الملف
            self.workbook.save(save_path)

            # تحديث مسار الملف إذا لزم الأمر
            if file_path:
                self.current_file = file_path
                self.file_path = file_path

            print(f"✅ تم حفظ الملف بنجاح: {save_path}")
            print(f"📊 عدد الصفحات المحفوظة: {len(self.workbook.sheetnames)}")
            return True

        except PermissionError as e:
            error_msg = f"لا يمكن حفظ الملف - قد يكون مفتوحاً في برنامج آخر: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في الحفظ", error_msg)
            return False

        except Exception as e:
            error_msg = f"خطأ في حفظ الملف: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في الحفظ", error_msg)
            return False

    def save(self, file_path=None):
        """دالة حفظ بديلة للتوافق"""
        return self.save_workbook(file_path)
    def create_account_sheet(self, account_num, account_name, initial_balance=0, format_type="classic"):
        """إنشاء صفحة حساب جديدة مع دعم أنواع تنسيق متعددة"""
        try:
            # التحقق من صحة البيانات
            if not account_num or not account_name:
                messagebox.showerror("خطأ", "رقم الحساب واسم الحساب مطلوبان")
                return False

            # تنظيف البيانات
            account_num = str(account_num).strip()
            account_name = str(account_name).strip()

            # التحقق من الرصيد الافتتاحي
            try:
                initial_balance = float(initial_balance)
            except (ValueError, TypeError):
                initial_balance = 0

            sheet_name = f"{account_num}-{account_name}"

            # التحقق من عدم وجود الحساب
            if sheet_name in self.workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{sheet_name}' موجود مسبقاً")
                return False

            # التحقق من صحة اسم الورقة
            if len(sheet_name) > 31:  # حد Excel لأسماء الأوراق
                messagebox.showerror("خطأ", "اسم الحساب طويل جداً (الحد الأقصى 31 حرف)")
                return False

            print(f"إنشاء حساب جديد: {sheet_name} بتنسيق: {format_type}")  # للتشخيص

            # إنشاء الورقة
            ws = self.workbook.create_sheet(sheet_name)
            ws.sheet_properties.rightToLeft = True  # اتجاه من اليمين لليسار (عربي)

            # اختيار نوع التنسيق
            if format_type == "official":
                self._setup_official_format(ws, account_num, account_name, initial_balance)
            else:
                # التنسيق الكلاسيكي (الافتراضي)
                self._setup_header(ws, account_num, account_name)
                self._setup_sections(ws, initial_balance)

            print(f"تم إنشاء الحساب: {sheet_name}")  # للتشخيص

            # إنشاء/تحديث التقرير الإجمالي تلقائياً
            print(f"📊 إنشاء/تحديث التقرير الإجمالي...")
            self.create_summary_report()

            # حفظ التغييرات
            save_result = self.save_workbook()
            if save_result:
                print(f"تم حفظ الحساب: {sheet_name}")  # للتشخيص
            else:
                print(f"فشل في حفظ الحساب: {sheet_name}")  # للتشخيص

            return save_result

        except Exception as e:
            error_msg = f"خطأ في إنشاء الحساب: {str(e)}"
            print(error_msg)  # للتشخيص
            messagebox.showerror("خطأ", error_msg)
            return False

    def _setup_header(self, ws, account_num, account_name):
        """إعداد ترويسة الصفحة"""
        try:
            # الترويسة الرسمية
            ws.merge_cells('A1:R1')
            ws['A1'] = "المملكة الأردنية الهاشمية"
            ws['A1'].font = Font(size=14, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A2:R2')
            ws['A2'] = "وزارة الصحة"
            ws['A2'].font = Font(size=12, bold=True)
            ws['A2'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A3:R3')
            ws['A3'] = f"حساب: {account_name} - {account_num}"
            ws['A3'].font = Font(size=12, bold=True)
            ws['A3'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A4:R4')
            current_date = datetime.now().strftime("%Y/%m/%d")
            ws['A4'] = f"تاريخ الإنشاء: {current_date}"
            ws['A4'].alignment = Alignment(horizontal='center')

        except Exception as e:
            print(f"خطأ في إعداد الترويسة: {str(e)}")
            raise
    def _setup_sections(self, ws, initial_balance):
        """إعداد أقسام الصفحة"""
        try:
            print(f"إعداد الأقسام مع رصيد افتتاحي: {initial_balance}")  # للتشخيص

            # تعريف الحدود
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إعداد عنوان الأقسام
            ws.merge_cells('A6:R6')
            ws['A6'] = "سجل المستندات والحوالات المالية"
            ws['A6'].font = Font(bold=True)
            ws['A6'].alignment = Alignment(horizontal='center')

            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P
                print(f"إعداد القسم {i+1} في العمود {col}")  # للتشخيص

                # العناوين مع أيقونات جميلة
                ws[f'{col}7'] = "💰 المبلغ"
                ws[f'{chr(ord(col) + 1)}7'] = "📝 مستند الصرف"
                ws[f'{chr(ord(col) + 2)}7'] = "💳 رقم التأدية"

                # تنسيق العناوين بشكل جميل وملون
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}7']
                    cell.font = Font(bold=True, color="FFFFFF", size=11)

                    # ألوان مختلفة لكل عمود
                    if j == 0:  # عمود المبلغ
                        cell.fill = PatternFill(start_color="2E8B57", end_color="2E8B57", fill_type="solid")  # أخضر
                    elif j == 1:  # عمود رقم المستند
                        cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")  # أزرق
                    else:  # عمود رقم التأدية
                        cell.fill = PatternFill(start_color="C5504B", end_color="C5504B", fill_type="solid")  # أحمر

                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.border = Border(
                        left=Side(style='medium', color='000000'),
                        right=Side(style='medium', color='000000'),
                        top=Side(style='medium', color='000000'),
                        bottom=Side(style='medium', color='000000')
                    )

                    # إضافة حدود وتنسيق جميل لجميع الخلايا
                    for row in range(8, 32):  # صفوف البيانات
                        cell = ws[f'{col_letter}{row}']
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center', vertical='center')

                        # تلوين متناوب للصفوف لسهولة القراءة
                        if row % 2 == 0:  # صفوف زوجية
                            cell.fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")  # رمادي فاتح
                        else:  # صفوف فردية
                            cell.fill = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")  # أبيض

                        # تعيين تنسيق "عام" لأعمدة رقم المستند ورقم التأدية
                        if j == 1 or j == 2:  # عمود رقم المستند أو رقم التأدية
                            cell.number_format = 'General'  # تنسيق عام
                        elif j == 0:  # عمود المبلغ
                            cell.number_format = '#,##0.00'  # تنسيق رقمي مع فواصل

                # القيم الأولية - إعداد الرصيد الافتتاحي وقيمة ما قبله (تم تحديث أرقام الصفوف)
                if i == 0:
                    ws[f'{col}8'] = initial_balance  # تم تغيير من 9 إلى 8
                    ws[f'{chr(ord(col) + 1)}8'] = "رصيد افتتاحي"
                    print(f"تم تعيين الرصيد الافتتاحي: {initial_balance}")  # للتشخيص
                else:
                    # إصلاح صيغة ترحيل الرصيد من القسم السابق
                    prev_col = chr(ord(col) - 3)  # العمود السابق
                    ws[f'{col}8'] = f"={prev_col}32"  # تم تغيير من 9=33 إلى 8=32
                    ws[f'{chr(ord(col) + 1)}8'] = "ما قبله"
                    print(f"تم تعيين صيغة ترحيل الرصيد: {col}8 = {prev_col}32")  # للتشخيص

                # إعداد صف المجموع بتنسيق جميل
                ws.merge_cells(f'{col}31:{chr(ord(col)+2)}31')
                ws[f'{col}31'] = "📊 المجموع العام"
                ws[f'{col}31'].font = Font(bold=True, color="FFFFFF", size=12)
                ws[f'{col}31'].fill = PatternFill(start_color="FF6B35", end_color="FF6B35", fill_type="solid")  # برتقالي جميل
                ws[f'{col}31'].alignment = Alignment(horizontal='center', vertical='center')
                ws[f'{col}31'].border = Border(
                    left=Side(style='thick', color='000000'),
                    right=Side(style='thick', color='000000'),
                    top=Side(style='thick', color='000000'),
                    bottom=Side(style='thick', color='000000')
                )

                # صيغ الجمع لجميع الأعمدة الثلاثة في القسم
                for j in range(3):  # الأعمدة الثلاثة في كل قسم
                    col_letter = chr(ord(col) + j)

                    if j == 0:  # عمود المبلغ
                        ws[f'{col_letter}32'] = f"=SUM({col_letter}8:{col_letter}30)"
                        ws[f'{col_letter}32'].font = Font(bold=True)
                    elif j == 1:  # عمود رقم المستند
                        ws[f'{col_letter}32'] = "الإجمالي"
                        ws[f'{col_letter}32'].font = Font(bold=True)
                    else:  # عمود رقم التأدية
                        ws[f'{col_letter}32'] = f"=COUNTA({col_letter}8:{col_letter}30)"  # عدد المستندات
                        ws[f'{col_letter}32'].font = Font(bold=True)

                # تنسيق خلايا المجموع بشكل جميل
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}32']

                    # حدود قوية ومميزة
                    cell.border = Border(
                        top=Side(style='double', color='000000'),
                        bottom=Side(style='double', color='000000'),
                        left=Side(style='medium', color='000000'),
                        right=Side(style='medium', color='000000')
                    )

                    # ألوان متدرجة حسب نوع العمود
                    if j == 0:  # عمود المبلغ
                        cell.fill = PatternFill(start_color="D5E8D4", end_color="D5E8D4", fill_type="solid")  # أخضر فاتح
                        cell.font = Font(bold=True, size=11, color="2E8B57")
                    elif j == 1:  # عمود رقم المستند
                        cell.fill = PatternFill(start_color="DAE8FC", end_color="DAE8FC", fill_type="solid")  # أزرق فاتح
                        cell.font = Font(bold=True, size=11, color="4472C4")
                    else:  # عمود رقم التأدية
                        cell.fill = PatternFill(start_color="F8CECC", end_color="F8CECC", fill_type="solid")  # أحمر فاتح
                        cell.font = Font(bold=True, size=11, color="C5504B")

                    cell.alignment = Alignment(horizontal='center', vertical='center')

            print("تم إنجاز إعداد جميع الأقسام")  # للتشخيص

        except Exception as e:
            print(f"خطأ في إعداد الأقسام: {str(e)}")
            raise

    def _setup_official_format(self, ws, account_num, account_name, initial_balance):
        """إعداد التنسيق الرسمي الحكومي الأردني"""
        try:
            print(f"إعداد التنسيق الرسمي للحساب: {account_name}")

            # إعداد الترويسة الرسمية
            self._setup_official_header(ws, account_num, account_name)

            # إعداد الأقسام الرسمية
            self._setup_official_sections(ws, account_name, initial_balance)

            # إعداد التذييل الرسمي
            self._setup_official_footer(ws)

            print("✅ تم إنجاز إعداد التنسيق الرسمي")

        except Exception as e:
            print(f"❌ خطأ في إعداد التنسيق الرسمي: {str(e)}")
            raise

    def _setup_official_header(self, ws, account_num, account_name):
        """إعداد الترويسة الرسمية حسب النسخة المرجعية"""
        try:
            from datetime import datetime

            # الصف 1: وزارة الصحة الأردنية
            ws.merge_cells('A1:R1')
            ws['A1'] = "🏥 وزارة الصحة الأردنية - نظام إدارة المستندات المحاسبية"
            ws['A1'].font = Font(size=14, bold=True, color="1F4E79")
            ws['A1'].alignment = Alignment(horizontal='center')
            ws['A1'].fill = PatternFill(start_color="E7F3FF", end_color="E7F3FF", fill_type="solid")

            # الصف 2: معلومات الحساب
            ws.merge_cells('A2:I2')
            ws['A2'] = f"رقم الحساب: {account_num}"
            ws['A2'].font = Font(size=12, bold=True)
            ws['A2'].alignment = Alignment(horizontal='left')

            ws.merge_cells('J2:R2')
            ws['J2'] = f"اسم الحساب: {account_name}"
            ws['J2'].font = Font(size=12, bold=True)
            ws['J2'].alignment = Alignment(horizontal='right')

            # الصف 3: تاريخ الإنشاء
            ws.merge_cells('A3:R3')
            ws['A3'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            ws['A3'].font = Font(size=10, italic=True)
            ws['A3'].alignment = Alignment(horizontal='center')

            print("✅ تم إعداد الترويسة الرسمية المحسنة")

        except Exception as e:
            print(f"❌ خطأ في إعداد الترويسة الرسمية: {str(e)}")
            raise

    def _setup_official_sections(self, ws, account_name, initial_balance):
        """إعداد الأقسام الرسمية"""
        try:
            # تعريف الحدود
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # الصف 4: رقم الحساب (1/214)
            ws.merge_cells('A4:R4')
            ws['A4'] = "1/214"
            ws['A4'].font = Font(bold=True)
            ws['A4'].alignment = Alignment(horizontal='center')

            # الصف 5: عناوين الأعمدة الأولى
            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                ws[f'{col}5'] = "فلس/دينار"
                ws[f'{chr(ord(col) + 1)}5'] = "مستند"
                ws[f'{chr(ord(col) + 2)}5'] = "رقم"

                # تنسيق العناوين
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}5']
                    cell.font = Font(bold=True, size=9)
                    cell.alignment = Alignment(horizontal='center')
                    cell.border = thin_border

            # الصف 6: عناوين الأعمدة الثانية
            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                ws[f'{chr(ord(col) + 1)}6'] = "الصرف"
                ws[f'{chr(ord(col) + 2)}6'] = "التأدية"

                # تنسيق العناوين
                for j in range(1, 3):  # العمودين الثاني والثالث فقط
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}6']
                    cell.font = Font(bold=True, size=9)
                    cell.alignment = Alignment(horizontal='center')
                    cell.border = thin_border

            # الصف 7: الرصيد الافتتاحي والترحيل
            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                if i == 0:
                    # القسم الأول: الرصيد الافتتاحي
                    ws[f'{col}7'] = initial_balance if initial_balance != 0 else 0
                    ws[f'{chr(ord(col) + 1)}7'] = "الرصيد"
                    ws[f'{chr(ord(col) + 2)}7'] = ""
                else:
                    # الأقسام الأخرى: صيغة SUM من القسم السابق
                    prev_col = chr(ord(col) - 3)
                    ws[f'{col}7'] = f"=SUM({prev_col}28)"
                    ws[f'{chr(ord(col) + 1)}7'] = "ما قبله"
                    ws[f'{chr(ord(col) + 2)}7'] = ""

                # تنسيق صف الرصيد الافتتاحي
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}7']
                    cell.border = thin_border
                    cell.alignment = Alignment(horizontal='center')

            # إعداد صفوف البيانات (الصفوف 8-27 = 20 صف)
            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                for row in range(8, 28):  # 20 صف للبيانات (من 8 إلى 27)
                    for j in range(3):
                        col_letter = chr(ord(col) + j)
                        cell = ws[f'{col_letter}{row}']
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center')

                        # تعيين تنسيق "عام" لأعمدة رقم المستند ورقم التأدية
                        if j == 1 or j == 2:  # عمود رقم المستند أو رقم التأدية
                            cell.number_format = 'General'  # تنسيق عام

            # الصف 30: فارغ (فاصل)

            # الصف 28: صف المجاميع (حسب التنسيق المحدث)
            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                # صيغ الجمع لجميع الأعمدة الثلاثة في القسم
                for j in range(3):  # الأعمدة الثلاثة في كل قسم
                    col_letter = chr(ord(col) + j)

                    if j == 0:  # عمود المبلغ
                        # جميع الأقسام تشمل الرصيد (افتتاحي أو مرحل)
                        ws[f'{col_letter}28'] = f"=SUM({col_letter}7:{col_letter}27)"

                        ws[f'{col_letter}28'].font = Font(bold=True)
                        ws[f'{col_letter}28'].number_format = '#,##0.000'

                    elif j == 1:  # عمود رقم المستند
                        ws[f'{col_letter}28'] = "الإجمالي"
                        ws[f'{col_letter}28'].font = Font(bold=True)

                    else:  # عمود رقم التأدية
                        ws[f'{col_letter}28'] = f"=COUNTA({col_letter}8:{col_letter}27)"  # عدد المستندات
                        ws[f'{col_letter}28'].font = Font(bold=True)

                    # تنسيق موحد لجميع الخلايا
                    cell = ws[f'{col_letter}28']
                    cell.border = Border(
                        top=Side(style='double'),
                        bottom=Side(style='double'),
                        left=Side(style='thin'),
                        right=Side(style='thin')
                    )
                    cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')

            print("✅ تم إعداد عناوين الأقسام الرسمية")

        except Exception as e:
            print(f"❌ خطأ في إعداد الأقسام الرسمية: {str(e)}")
            raise

    def _setup_official_footer(self, ws):
        """إعداد التذييل الرسمي"""
        try:
            from datetime import datetime

            # الصف 30: التاريخ وتوقيع المحاسب (حسب التنسيق المحدث)
            ws.merge_cells('A30:I30')
            ws['A30'] = "التاريخ"
            ws['A30'].font = Font(size=11)
            ws['A30'].alignment = Alignment(horizontal='left')

            ws.merge_cells('J30:R30')
            ws['J30'] = "اسم وتوقيع المحاسب"
            ws['J30'].font = Font(size=11)
            ws['J30'].alignment = Alignment(horizontal='right')

            # إضافة خط للتوقيع
            ws.merge_cells('J31:R31')
            ws['J31'] = "_" * 30
            ws['J31'].alignment = Alignment(horizontal='center')

            print("✅ تم إعداد التذييل الرسمي")

        except Exception as e:
            print(f"❌ خطأ في إعداد التذييل الرسمي: {str(e)}")
            raise

    def add_document(self, sheet_name, amount, doc_num, pay_num):
        """إضافة مستند إلى حساب بالآلية المرجعية"""
        try:
            print(f"📝 [آلية مرجعية] إضافة مستند إلى الحساب: {sheet_name}")
            print(f"   المبلغ: {amount}, رقم المستند: {doc_num}, رقم التأدية: {pay_num}")

            # تطبيق آلية الترحيل المرجعية
            return self._add_document_with_reference_logic(sheet_name, amount, doc_num, pay_num)

        except Exception as e:
            print(f"❌ خطأ في إضافة المستند: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المستند: {str(e)}")
            return False

    def _add_document_with_reference_logic(self, sheet_name, amount, doc_num, pay_num):
        """آلية الترحيل المحسنة للمستندات - ترحيل بالأقسام بالترتيب مع فحص التكرار"""
        try:
            # التحقق من وجود الحساب
            if sheet_name not in self.workbook.sheetnames:
                error_msg = f"الحساب '{sheet_name}' غير موجود"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)
                return False

            # فحص تكرار رقم المستند ورقم التأدية في نفس صفحة الحساب
            duplicate_in_account = self._check_document_duplicates_in_account(sheet_name, doc_num, pay_num)
            if duplicate_in_account:
                error_msg = f"خطأ: رقم المستند '{doc_num}' ورقم التأدية '{pay_num}' موجودان مسبقاً في نفس صفحة الحساب '{sheet_name}'"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ - مستند مكرر في نفس الحساب", error_msg)
                return False

            # فحص تكرار رقم المستند ورقم التأدية في جميع الحسابات الأخرى
            duplicate_in_other_account = self._check_document_duplicates_in_other_accounts(sheet_name, doc_num, pay_num)
            if duplicate_in_other_account:
                error_msg = f"خطأ: رقم المستند '{doc_num}' ورقم التأدية '{pay_num}' موجودان مسبقاً في الحساب '{duplicate_in_other_account}'"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ - مستند مكرر في حساب آخر", error_msg)
                return False

            ws = self.workbook[sheet_name]
            print(f"✅ تم العثور على ورقة الحساب")

            # البحث عن أول خلية فارغة بالآلية المحسنة (ترحيل بالأقسام)
            empty_cell = self._find_empty_cell_sequential_sections(ws)

            if not empty_cell:
                # محاولة إنشاء جدول جديد تلقائياً
                print("📝 جميع الأقسام في الجدول الحالي ممتلئة - إنشاء جدول جديد تلقائياً...")

                # استخراج معلومات الحساب
                account_info = self._extract_account_info(sheet_name)
                if account_info:
                    account_num, account_name = account_info

                    # إنشاء جدول جديد تلقائياً مع ترحيل الرصيد
                    new_table_created = self._create_new_table_with_balance_transfer(ws, account_num, account_name)

                    if new_table_created:
                        # إعادة البحث عن خلية فارغة في الجدول الجديد
                        empty_cell = self._find_empty_cell_sequential_sections(ws)
                        if not empty_cell:
                            error_msg = "فشل في إنشاء جدول جديد"
                            print(f"❌ {error_msg}")
                            return False
                    else:
                        error_msg = "لا يوجد مكان لإضافة جدول جديد"
                        print(f"❌ {error_msg}")
                        return False
                else:
                    error_msg = "فشل في استخراج معلومات الحساب"
                    print(f"❌ {error_msg}")
                    return False

            row, col = empty_cell
            print(f"✅ تم العثور على خلية فارغة في الصف {row}, العمود {col}")

            # التأكد من أن القيمة رقمية
            try:
                amount_float = float(amount)
                print(f"✅ المبلغ صحيح: {amount_float}")
            except ValueError:
                error_msg = "يرجى إدخال قيمة رقمية صحيحة للمبلغ"
                print(f"❌ {error_msg}")
                return False

            # إضافة المستند
            return self._insert_document_enhanced(ws, row, col, amount_float, doc_num, pay_num)

        except Exception as e:
            print(f"❌ خطأ في آلية الترحيل المحسنة: {str(e)}")
            return False



    def _check_document_duplicates_in_account(self, sheet_name, doc_num, pay_num):
        """فحص تكرار رقم المستند ورقم التأدية في حساب محدد"""
        try:
            print(f"🔍 فحص تكرار في الحساب: {sheet_name}")
            print(f"   رقم المستند: {doc_num}, رقم التأدية: {pay_num}")

            # تحويل إلى نص للمقارنة
            doc_num_str = str(doc_num).strip()
            pay_num_str = str(pay_num).strip()

            # الحصول على ورقة الحساب
            if sheet_name not in self.workbook.sheetnames:
                return False

            ws = self.workbook[sheet_name]

            # فحص جميع الجداول في هذا الحساب
            duplicate_found = self._check_duplicates_in_sheet(ws, doc_num_str, pay_num_str)
            if duplicate_found:
                print(f"  ❌ تم العثور على مستند مكرر في نفس الحساب")
                return True

            print(f"  ✅ لا يوجد تكرار في هذا الحساب")
            return False

        except Exception as e:
            print(f"❌ خطأ في فحص تكرار الحساب: {str(e)}")
            return False

    def _check_document_duplicates_in_other_accounts(self, current_sheet_name, doc_num, pay_num):
        """فحص تكرار رقم المستند ورقم التأدية في جميع الحسابات الأخرى"""
        try:
            print(f"🔍 فحص تكرار في جميع الحسابات الأخرى")
            print(f"   رقم المستند: {doc_num}, رقم التأدية: {pay_num}")

            # تحويل إلى نص للمقارنة
            doc_num_str = str(doc_num).strip()
            pay_num_str = str(pay_num).strip()

            # فحص جميع الحسابات (الأوراق) في الملف
            for sheet_name in self.workbook.sheetnames:
                # تجاهل ورقة أرصدة الحسابات والحساب الحالي
                if sheet_name == "أرصدة الحسابات" or sheet_name == current_sheet_name:
                    continue

                try:
                    ws = self.workbook[sheet_name]
                    print(f"  🔍 فحص الحساب: {sheet_name}")

                    # فحص جميع الجداول في هذا الحساب
                    duplicate_found = self._check_duplicates_in_sheet(ws, doc_num_str, pay_num_str)
                    if duplicate_found:
                        print(f"  ❌ تم العثور على مستند مكرر في: {sheet_name}")
                        return sheet_name

                except Exception as e:
                    print(f"  ⚠️ خطأ في فحص الحساب {sheet_name}: {str(e)}")
                    continue

            print(f"  ✅ لا يوجد تكرار في أي حساب آخر")
            return None

        except Exception as e:
            print(f"❌ خطأ في فحص تكرار الحسابات الأخرى: {str(e)}")
            return None

    def _check_duplicates_in_sheet(self, ws, doc_num_str, pay_num_str):
        """فحص تكرار المستند في ورقة واحدة"""
        try:
            # فحص جميع الجداول في الورقة
            table_start_row = 8  # بداية الجدول الأول

            while table_start_row < 1000:  # حد أقصى للبحث
                # فحص وجود جدول في هذا الموقع
                if self._is_table_header_at_row(ws, table_start_row - 3):
                    # فحص جميع الأقسام في هذا الجدول
                    for section in range(6):
                        col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                        # فحص جميع الصفوف في هذا القسم
                        for row in range(table_start_row, table_start_row + 20):
                            doc_cell = ws.cell(row=row, column=col+1)
                            pay_cell = ws.cell(row=row, column=col+2)

                            # مقارنة رقم المستند ورقم التأدية
                            existing_doc = str(doc_cell.value).strip() if doc_cell.value else ""
                            existing_pay = str(pay_cell.value).strip() if pay_cell.value else ""

                            # فحص التطابق (رقم المستند ورقم التأدية معاً)
                            if (existing_doc == doc_num_str and existing_pay == pay_num_str and
                                existing_doc != "" and existing_pay != "" and
                                existing_doc not in ["ما قبله", "رقم المستند", "رقم التأدية"]):
                                print(f"    ❌ تكرار في الصف {row}, العمود {col+1}: {existing_doc}, {existing_pay}")
                                return True

                    # الانتقال إلى الجدول التالي
                    table_start_row += 35  # ارتفاع الجدول الواحد
                else:
                    break

            return False

        except Exception as e:
            print(f"❌ خطأ في فحص التكرار داخل الورقة: {str(e)}")
            return False

    def _find_empty_cell_sequential_sections(self, ws):
        """البحث عن خلية فارغة بالترتيب المطلوب: الجدول الأول-القسم الأول حتى القسم السادس"""
        try:
            print("🔍 [آلية محسنة] البحث عن خلية فارغة بالترتيب المطلوب...")

            # البحث في جميع الجداول الموجودة
            table_start_row = 8  # بداية الجدول الأول

            while table_start_row < 1000:  # حد أقصى للبحث
                # فحص وجود جدول في هذا الموقع
                if self._is_table_header_at_row(ws, table_start_row - 3):  # العناوين في الصف 5
                    print(f"📋 تم العثور على جدول في الصف {table_start_row}")

                    # البحث عن خلية فارغة في هذا الجدول بالترتيب المطلوب
                    empty_cell = self._find_empty_cell_in_table_sequential(ws, table_start_row)
                    if empty_cell:
                        return empty_cell

                    # الانتقال إلى الجدول التالي
                    table_start_row += 35  # ارتفاع الجدول الواحد
                else:
                    break

            print("❌ لم يتم العثور على خلية فارغة في جميع الجداول")
            return None

        except Exception as e:
            print(f"❌ خطأ في البحث عن خلية فارغة: {str(e)}")
            return None

    def _find_empty_cell_in_table_sequential(self, ws, table_start_row):
        """البحث عن خلية فارغة في جدول محدد بالترتيب: قسم 1 حتى قسم 6"""
        try:
            # فحص الأقسام بالترتيب (1-6)
            for section in range(6):
                col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16
                section_name = chr(64 + col)  # A, D, G, J, M, P

                print(f"  🔍 فحص القسم {section + 1} (عمود {section_name})...")

                # فحص الصفوف من 8 إلى 27 (20 صف)
                for row in range(table_start_row, table_start_row + 20):
                    # فحص إذا كانت الخلية فارغة
                    amount_cell = ws.cell(row=row, column=col)
                    doc_cell = ws.cell(row=row, column=col+1)
                    pay_cell = ws.cell(row=row, column=col+2)

                    if (self._is_cell_truly_empty(amount_cell.value) and
                        self._is_cell_truly_empty(doc_cell.value) and
                        self._is_cell_truly_empty(pay_cell.value)):

                        print(f"  ✅ خلية فارغة في القسم {section + 1}, الصف {row}, العمود {col}")
                        return (row, col)

                print(f"  ❌ القسم {section + 1} ممتلئ - الانتقال إلى القسم التالي")

            print(f"  ❌ جميع الأقسام في الجدول ممتلئة")
            return None

        except Exception as e:
            print(f"❌ خطأ في البحث داخل الجدول: {str(e)}")
            return None

    def _create_new_table_with_balance_transfer(self, ws, account_num, account_name):
        """إنشاء جدول جديد مع ترحيل الرصيد تلقائياً وبدون رسائل مزعجة"""
        try:
            print("📝 [آلية محسنة] إنشاء جدول جديد مع ترحيل الرصيد تلقائياً...")

            # البحث عن آخر جدول موجود
            last_table_end = self._find_last_table_end(ws)

            if last_table_end is None:
                print("❌ لم يتم العثور على جداول موجودة")
                return False

            # حساب موقع الجدول الجديد
            new_table_start = last_table_end + 5  # فاصل 5 صفوف

            # حساب الرصيد المرحل من الجدول السابق (مجموع القسم السادس)
            carried_balance = self._calculate_last_table_section6_total(ws, last_table_end)
            print(f"💰 الرصيد المرحل من الجدول السابق: {carried_balance}")

            # إنشاء الجدول الجديد
            success = self._create_table_at_position_enhanced(ws, new_table_start, account_num, account_name, carried_balance)

            if success:
                print(f"✅ تم إنشاء الجدول الجديد بنجاح في الصف {new_table_start}")
                return True
            else:
                print("❌ فشل في إنشاء الجدول الجديد")
                return False

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول جديد: {str(e)}")
            return False

    def _calculate_last_table_section6_total(self, ws, last_table_end):
        """حساب مجموع القسم السادس من آخر جدول"""
        try:
            # موقع صف المجاميع في آخر جدول
            totals_row = last_table_end - 2  # صف المجاميع
            section6_col = 16  # عمود P (القسم السادس)

            # قراءة قيمة مجموع القسم السادس
            total_cell = ws.cell(row=totals_row, column=section6_col)

            if total_cell.value and isinstance(total_cell.value, (int, float)):
                return float(total_cell.value)
            elif total_cell.value and str(total_cell.value).startswith('='):
                # إذا كانت صيغة، حاول حساب القيمة يدوياً
                return self._calculate_section_total_manually(ws, last_table_end - 22, 16)  # بداية الجدول
            else:
                return 0.0

        except Exception as e:
            print(f"❌ خطأ في حساب مجموع القسم السادس: {str(e)}")
            return 0.0

    def _calculate_section_total_manually(self, ws, table_start_row, section_col):
        """حساب مجموع قسم يدوياً"""
        try:
            total = 0.0
            # حساب مجموع قيم المستندات في هذا القسم
            for row in range(table_start_row, table_start_row + 20):
                cell = ws.cell(row=row, column=section_col)
                if cell.value and isinstance(cell.value, (int, float)):
                    total += float(cell.value)
            return total
        except:
            return 0.0

    def _create_table_at_position_enhanced(self, ws, start_row, account_num, account_name, carried_balance):
        """إنشاء جدول في موقع محدد مع ترحيل الرصيد المحسن"""
        try:
            print(f"📝 إنشاء جدول جديد في الصف {start_row} مع رصيد مرحل: {carried_balance}")

            # إعداد عناوين الجدول
            self._setup_table_headers_at_position(ws, start_row)

            # إعداد صفوف البيانات
            self._setup_table_data_rows_at_position(ws, start_row + 3)

            # إعداد صف المجاميع مع ترحيل الرصيد المحسن
            self._setup_table_totals_enhanced(ws, start_row + 23, carried_balance)

            print(f"✅ تم إنشاء الجدول بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء الجدول: {str(e)}")
            return False

    def _setup_table_totals_enhanced(self, ws, totals_row, carried_balance):
        """إعداد صف المجاميع مع ترحيل الرصيد المحسن"""
        try:
            from openpyxl.styles import Border, Side, Alignment, Font

            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إعداد صف المجاميع للأقسام الستة
            for section in range(6):
                col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16
                col_letter = chr(64 + col)

                if section == 0:
                    # القسم الأول - ترحيل الرصيد من الجدول السابق
                    ws.cell(row=totals_row, column=col, value=carried_balance)
                    ws.cell(row=totals_row, column=col+1, value="ما قبله")
                    ws.cell(row=totals_row, column=col+2, value="")
                    print(f"✅ تم ترحيل الرصيد {carried_balance} إلى القسم الأول")
                else:
                    # الأقسام الأخرى - صيغة من العمود السابق
                    prev_col_letter = chr(64 + col - 3)
                    ws.cell(row=totals_row, column=col, value=f"={prev_col_letter}{totals_row}")
                    ws.cell(row=totals_row, column=col+1, value="ما قبله")
                    ws.cell(row=totals_row, column=col+2, value="")

                # تنسيق الخلايا
                for j in range(3):
                    cell = ws.cell(row=totals_row, column=col+j)
                    cell.border = thin_border
                    cell.alignment = Alignment(horizontal='center')
                    if j == 0:  # عمود المبلغ
                        cell.font = Font(bold=True)

            print(f"✅ تم إعداد صف المجاميع بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إعداد صف المجاميع: {str(e)}")
            return False

    def _insert_document_enhanced(self, ws, row, col, amount, doc_num, pay_num):
        """إدراج المستند بالآلية المحسنة بدون رسائل مزعجة"""
        try:
            print(f"📝 [آلية محسنة] إدراج المستند...")

            from openpyxl.styles import Border, Side, Alignment

            # إعداد الحدود
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إدراج بيانات المستند
            ws.cell(row=row, column=col, value=amount).border = thin_border
            ws.cell(row=row, column=col+1, value=doc_num).border = thin_border
            ws.cell(row=row, column=col+2, value=pay_num).border = thin_border

            # تنسيق الخلايا
            for i in range(3):
                cell = ws.cell(row=row, column=col+i)
                cell.alignment = Alignment(horizontal='center')

            # حفظ التغييرات بدون رسائل مزعجة
            save_result = self.save_workbook()
            if save_result:
                print("✅ تم إضافة المستند وحفظ الملف بنجاح")
                # لا توجد رسالة نجاح مزعجة - حسب المطلوب
                return True
            else:
                print("❌ تم إضافة المستند ولكن فشل في حفظ الملف")
                return False

        except Exception as e:
            print(f"❌ خطأ في إدراج المستند: {str(e)}")
            return False

    def _find_empty_cell_reference_method(self, ws):
        """البحث عن أول خلية فارغة بالآلية المرجعية"""
        try:
            print("🔍 [آلية مرجعية] البحث عن خلية فارغة...")

            # البحث في جميع الجداول الموجودة
            table_start_row = 8  # بداية الجدول الأول

            while table_start_row < 1000:  # حد أقصى للبحث
                # فحص وجود جدول في هذا الموقع
                if self._is_table_header_at_row(ws, table_start_row - 3):  # العناوين في الصف 5
                    # البحث عن خلية فارغة في هذا الجدول
                    empty_cell = self._find_empty_cell_in_table(ws, table_start_row)
                    if empty_cell:
                        return empty_cell

                    # الانتقال إلى الجدول التالي
                    table_start_row += 35  # ارتفاع الجدول الواحد
                else:
                    break

            print("❌ لم يتم العثور على خلية فارغة")
            return None

        except Exception as e:
            print(f"❌ خطأ في البحث عن خلية فارغة: {str(e)}")
            return None

    def _is_table_header_at_row(self, ws, row):
        """فحص وجود عناوين جدول في صف معين"""
        try:
            # فحص وجود عناوين مميزة للجدول
            header_indicators = ["فلس/دينار", "مستند", "رقم"]

            for col in range(1, 19, 3):  # فحص الأعمدة A, D, G, J, M, P
                cell_value = ws.cell(row=row, column=col).value
                if cell_value and any(indicator in str(cell_value) for indicator in header_indicators):
                    return True
            return False

        except Exception as e:
            print(f"❌ خطأ في فحص عناوين الجدول: {str(e)}")
            return False

    def _find_empty_cell_in_table(self, ws, table_start_row):
        """البحث عن خلية فارغة في جدول محدد"""
        try:
            # فحص الصفوف من 8 إلى 27 (20 صف)
            for row in range(table_start_row, table_start_row + 20):
                # فحص الأقسام الستة (A, D, G, J, M, P)
                for section in range(6):
                    col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                    # فحص إذا كانت الخلية فارغة
                    amount_cell = ws.cell(row=row, column=col)
                    doc_cell = ws.cell(row=row, column=col+1)
                    pay_cell = ws.cell(row=row, column=col+2)

                    if (not amount_cell.value and not doc_cell.value and not pay_cell.value):
                        print(f"✅ خلية فارغة في الصف {row}, العمود {col}")
                        return (row, col)

            return None

        except Exception as e:
            print(f"❌ خطأ في البحث داخل الجدول: {str(e)}")
            return None

    def _create_new_table_reference_method(self, ws, account_num, account_name):
        """إنشاء جدول جديد بالآلية المرجعية"""
        try:
            print("📝 [آلية مرجعية] إنشاء جدول جديد...")

            # البحث عن آخر جدول موجود
            last_table_end = self._find_last_table_end(ws)

            if last_table_end is None:
                print("❌ لم يتم العثور على جداول موجودة")
                return False

            # حساب موقع الجدول الجديد
            new_table_start = last_table_end + 5  # فاصل 5 صفوف

            # إنشاء الجدول الجديد
            return self._create_table_at_position(ws, new_table_start, account_num, account_name)

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول جديد: {str(e)}")
            return False

    def _find_last_table_end(self, ws):
        """البحث عن نهاية آخر جدول"""
        try:
            last_end = 30  # البداية الافتراضية

            # البحث عن آخر صف يحتوي على بيانات
            for row in range(1000, 30, -1):  # البحث من الأسفل إلى الأعلى
                for col in range(1, 19):  # فحص جميع الأعمدة
                    if ws.cell(row=row, column=col).value:
                        return row

            return last_end

        except Exception as e:
            print(f"❌ خطأ في البحث عن آخر جدول: {str(e)}")
            return 30

    def _create_table_at_position(self, ws, start_row, account_num, account_name):
        """إنشاء جدول في موقع محدد"""
        try:
            print(f"📝 إنشاء جدول جديد في الصف {start_row}")

            # إعداد عناوين الجدول
            self._setup_table_headers_at_position(ws, start_row)

            # إعداد صفوف البيانات
            self._setup_table_data_rows_at_position(ws, start_row + 3)

            # إعداد صف المجاميع
            self._setup_table_totals_at_position(ws, start_row + 23)

            print(f"✅ تم إنشاء الجدول بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء الجدول: {str(e)}")
            return False

    def _insert_document_reference_method(self, ws, row, col, amount, doc_num, pay_num):
        """إدراج المستند بالآلية المرجعية"""
        try:
            print(f"📝 [آلية مرجعية] إدراج المستند...")

            # إعداد الحدود
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إدراج المبلغ
            amount_cell = ws.cell(row=row, column=col)
            amount_cell.value = amount
            amount_cell.number_format = '#,##0.000'  # تنسيق المبلغ
            amount_cell.border = thin_border
            amount_cell.alignment = Alignment(horizontal='center')
            print(f"   المبلغ في {chr(64+col)}{row}: {amount}")

            # إدراج رقم المستند
            doc_cell = ws.cell(row=row, column=col+1)
            doc_cell.value = str(doc_num)
            doc_cell.border = thin_border
            doc_cell.alignment = Alignment(horizontal='center')
            print(f"   رقم المستند في {chr(64+col+1)}{row}: {doc_num}")

            # إدراج رقم التأدية
            pay_cell = ws.cell(row=row, column=col+2)
            pay_cell.value = str(pay_num)
            pay_cell.border = thin_border
            pay_cell.alignment = Alignment(horizontal='center')
            print(f"   رقم التأدية في {chr(64+col+2)}{row}: {pay_num}")

            # حفظ التغييرات بدون رسائل مزعجة
            save_result = self.save_workbook()
            if save_result:
                print("✅ تم إضافة المستند وحفظ الملف بنجاح")
                # تم إزالة رسالة النجاح المزعجة حسب المطلوب
                return True
            else:
                print("❌ تم إضافة المستند ولكن فشل في حفظ الملف")
                messagebox.showerror("خطأ", "تم إضافة المستند ولكن فشل في حفظ الملف")
                return False

        except Exception as e:
            print(f"❌ خطأ في إدراج المستند: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ في إدراج المستند: {str(e)}")
            return False

    def _find_empty_cell(self, ws):
        """البحث عن خلية فارغة مع دعم التنسيقات المختلفة"""
        print("🔍 البحث عن خلية فارغة...")  # للتشخيص

        # تحديد نوع التنسيق بناءً على محتوى الورقة
        format_type = self._detect_format_type(ws)
        print(f"🔍 نوع التنسيق المكتشف: {format_type}")

        if format_type == "official":
            return self._find_empty_cell_official(ws)
        else:
            return self._find_empty_cell_classic(ws)

    def _detect_format_type(self, ws):
        """اكتشاف نوع التنسيق المستخدم في الورقة"""
        try:
            # فحص الخلية A1 لتحديد نوع التنسيق
            cell_a1 = ws['A1'].value
            if cell_a1 and "وزارة / الدائرة" in str(cell_a1):
                return "official"
            else:
                return "classic"
        except:
            return "classic"

    def _find_empty_cell_classic(self, ws):
        """البحث عن خلية فارغة في التنسيق الكلاسيكي"""
        print("🔍 البحث في التنسيق الكلاسيكي...")

        # فحص كل قسم من الأقسام الستة
        for i in range(6):  # الأقسام الستة
            col_start = chr(65 + (i * 3))  # A, D, G, J, M, P
            col_num = ord(col_start) - 64  # تحويل الحرف إلى رقم العمود

            print(f"فحص القسم {i+1} - العمود {col_start} (رقم {col_num})")  # للتشخيص

            # البحث عن خلية فارغة في هذا القسم (من الصف 8 إلى 30)
            for row in range(8, 31):  # من الصف 8 إلى 30 (المستندات فقط)
                try:
                    # فحص الخلية
                    cell = ws.cell(row=row, column=col_num)
                    cell_value = cell.value

                    print(f"  فحص الخلية {col_start}{row}: '{cell_value}' (نوع: {type(cell_value)})")  # للتشخيص

                    # فحص أكثر دقة للخلايا الفارغة حقاً
                    is_empty = (
                        cell_value is None or
                        cell_value == "" or
                        (isinstance(cell_value, str) and cell_value.strip() == "") or
                        (isinstance(cell_value, str) and cell_value.strip() in ['0', '0.0']) or
                        (isinstance(cell_value, (int, float)) and cell_value == 0)
                    )

                    if is_empty:
                        # فحص الخلايا المجاورة
                        doc_cell = ws.cell(row=row, column=col_num+1).value
                        pay_cell = ws.cell(row=row, column=col_num+2).value

                        # فحص أكثر دقة للخلايا المجاورة - حماية البيانات اليدوية
                        doc_empty = self._is_cell_truly_empty(doc_cell)
                        pay_empty = self._is_cell_truly_empty(pay_cell)

                        if doc_empty and pay_empty:
                            print(f"✅ وجدت خلية فارغة آمنة في {col_start}{row}")
                            return (row, col_num)
                        else:
                            print(f"⚠️ الخلايا المجاورة ممتلئة (حماية البيانات): doc='{doc_cell}', pay='{pay_cell}'")

                except Exception as e:
                    print(f"❌ خطأ في فحص الخلية {col_start}{row}: {str(e)}")
                    continue

        print("❌ لم يتم العثور على خلية فارغة في جميع الأقسام")  # للتشخيص
        return None

    def _is_cell_truly_empty(self, cell_value):
        """فحص إذا كانت الخلية فارغة حقاً (حماية البيانات اليدوية)"""
        if cell_value is None:
            return True

        if isinstance(cell_value, str):
            stripped = cell_value.strip()
            # فارغة إذا كانت فارغة أو تحتوي على عناوين افتراضية فقط
            if (stripped == "" or
                stripped in ['رقم المستند', 'رقم المستند:', 'رقم القبض', 'رقم القبض:', '0', '0.0']):
                return True
            # إذا كانت تحتوي على بيانات حقيقية، فهي ليست فارغة
            return False

        if isinstance(cell_value, (int, float)):
            # فارغة إذا كانت صفر
            return cell_value == 0

        # أي نوع آخر من البيانات يعتبر غير فارغ
        return False

    def _calculate_carried_balance(self, ws, before_row):
        """حساب الرصيد المرحل بطريقة محسنة ودقيقة"""
        try:
            print(f"📊 حساب الرصيد المرحل قبل الصف {before_row}")

            # الطريقة المحسنة: البحث عن آخر صف مجموع في الجدول السابق
            last_total_balance = self._find_last_total_in_previous_tables(ws, before_row)

            if last_total_balance is not None:
                print(f"✅ تم العثور على آخر مجموع: {last_total_balance}")
                return last_total_balance

            # إذا لم يتم العثور على مجموع، حساب الرصيد يدوياً
            calculated_balance = self._calculate_total_balance_manually(ws, before_row)
            print(f"📊 الرصيد المحسوب يدوياً: {calculated_balance}")
            return calculated_balance

        except Exception as e:
            print(f"❌ خطأ في حساب الرصيد المرحل: {str(e)}")
            return 0

    def _find_last_total_in_previous_tables(self, ws, before_row):
        """البحث عن آخر صف مجموع في الجداول السابقة"""
        try:
            # البحث عن صفوف المجموع (عادة تكون في نهاية كل جدول)
            for row in range(before_row - 1, 0, -1):
                # فحص العمود الأخير الممتلئ (P = 16)
                for col in [16, 13, 10, 7, 4, 1]:  # P, M, J, G, D, A - من اليمين لليسار
                    cell = ws.cell(row=row, column=col)

                    # فحص إذا كانت هذه خلية مجموع
                    if self._is_total_cell(cell, row, col, ws):
                        try:
                            balance = float(cell.value)
                            print(f"✅ وجد مجموع في {chr(64+col)}{row}: {balance}")
                            return balance
                        except (ValueError, TypeError):
                            continue

            return None

        except Exception as e:
            print(f"❌ خطأ في البحث عن آخر مجموع: {str(e)}")
            return None

    def _is_total_cell(self, cell, row, col, ws):
        """فحص إذا كانت الخلية تحتوي على مجموع"""
        try:
            if cell.value is None:
                return False

            # فحص إذا كانت القيمة رقمية وموجبة
            if isinstance(cell.value, (int, float)) and cell.value > 0:
                # فحص إذا كانت هناك علامة على أنها مجموع
                # 1. فحص الخلية المجاورة للنص
                adjacent_cell = ws.cell(row=row, column=col+1)
                if adjacent_cell.value and 'مجموع' in str(adjacent_cell.value):
                    return True

                # 2. فحص إذا كانت في نهاية قسم (عادة صف 32 أو ما شابه)
                if row % 32 == 0 or row % 33 == 0:  # صفوف المجموع المتوقعة
                    return True

                # 3. فحص إذا كانت هناك مستندات في الصفوف السابقة
                has_documents_above = False
                for check_row in range(max(1, row-25), row):
                    check_cell = ws.cell(row=check_row, column=col)
                    if (check_cell.value and isinstance(check_cell.value, (int, float)) and
                        check_cell.value > 0 and check_cell.value != cell.value):
                        has_documents_above = True
                        break

                if has_documents_above:
                    return True

            return False

        except Exception as e:
            print(f"❌ خطأ في فحص خلية المجموع: {str(e)}")
            return False

    def _calculate_total_balance_manually(self, ws, before_row):
        """حساب الرصيد الإجمالي يدوياً"""
        try:
            # الحصول على الرصيد الافتتاحي
            opening_balance = 0
            for location in ['A8', 'A9', 'A7']:
                try:
                    cell = ws[location]
                    if cell.value and isinstance(cell.value, (int, float)):
                        opening_balance = float(cell.value)
                        break
                except:
                    continue

            # حساب مجموع جميع المستندات قبل الصف المحدد
            documents_total = 0

            # فحص جميع الأقسام والجداول
            for section in range(6):  # 6 أقسام
                col_start = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                # فحص جميع الصفوف في هذا القسم
                for row in range(8, before_row):  # من بداية المستندات
                    try:
                        amount_cell = ws.cell(row=row, column=col_start)
                        if (amount_cell.value and isinstance(amount_cell.value, (int, float)) and
                            amount_cell.value > 0):

                            # فحص إذا كان هذا مستند حقيقي (ليس رصيد افتتاحي)
                            doc_cell = ws.cell(row=row, column=col_start+1)
                            pay_cell = ws.cell(row=row, column=col_start+2)

                            # إذا كان هناك رقم مستند أو رقم تأدية، فهو مستند
                            if ((doc_cell.value and str(doc_cell.value).strip() not in ['', 'رقم المستند', 'رصيد افتتاحي', 'ما قبله']) or
                                (pay_cell.value and str(pay_cell.value).strip() not in ['', 'رقم القبض', 'رقم التأدية'])):
                                documents_total += float(amount_cell.value)
                                print(f"📝 مستند في {chr(64+col_start)}{row}: {amount_cell.value}")
                    except:
                        continue

            total_balance = opening_balance + documents_total
            print(f"📊 الحساب اليدوي: افتتاحي({opening_balance}) + مستندات({documents_total}) = {total_balance}")
            return total_balance

        except Exception as e:
            print(f"❌ خطأ في الحساب اليدوي: {str(e)}")
            return 0

    def _find_empty_cell_official(self, ws):
        """البحث عن خلية فارغة في التنسيق الرسمي"""
        print("🔍 البحث في التنسيق الرسمي...")

        # فحص كل قسم من الأقسام الستة
        for i in range(6):  # الأقسام الستة
            col_start = chr(65 + (i * 3))  # A, D, G, J, M, P
            col_num = ord(col_start) - 64  # تحويل الحرف إلى رقم العمود

            print(f"فحص القسم {i+1} - العمود {col_start} (رقم {col_num})")  # للتشخيص

            # البحث عن خلية فارغة في هذا القسم (من الصف 8 إلى 29 في التنسيق الرسمي)
            for row in range(8, 30):  # من الصف 8 إلى 29 (22 صف للمستندات)
                try:
                    # فحص الخلية
                    cell = ws.cell(row=row, column=col_num)
                    cell_value = cell.value

                    print(f"  فحص الخلية {col_start}{row}: '{cell_value}' (نوع: {type(cell_value)})")  # للتشخيص

                    # فحص أكثر دقة للخلايا الفارغة حقاً
                    is_empty = (
                        cell_value is None or
                        cell_value == "" or
                        (isinstance(cell_value, str) and cell_value.strip() == "") or
                        (isinstance(cell_value, str) and cell_value.strip() in ['0', '0.0']) or
                        (isinstance(cell_value, (int, float)) and cell_value == 0)
                    )

                    if is_empty:
                        # فحص الخلايا المجاورة
                        doc_cell = ws.cell(row=row, column=col_num+1).value
                        pay_cell = ws.cell(row=row, column=col_num+2).value

                        # فحص أكثر دقة للخلايا المجاورة - حماية البيانات اليدوية
                        doc_empty = self._is_cell_truly_empty(doc_cell)
                        pay_empty = self._is_cell_truly_empty(pay_cell)

                        if doc_empty and pay_empty:
                            print(f"✅ وجدت خلية فارغة آمنة في {col_start}{row}")
                            return (row, col_num)
                        else:
                            print(f"⚠️ الخلايا المجاورة ممتلئة (حماية البيانات): doc='{doc_cell}', pay='{pay_cell}'")

                except Exception as e:
                    print(f"❌ خطأ في فحص الخلية {col_start}{row}: {str(e)}")
                    continue

        print("❌ لم يتم العثور على خلية فارغة في جميع الأقسام")  # للتشخيص
        return None

    def _extract_account_info(self, sheet_name):
        """استخراج معلومات الحساب من اسم الورقة"""
        try:
            if '-' in sheet_name:
                parts = sheet_name.split('-', 1)
                account_num = parts[0].strip()
                account_name = parts[1].strip()
                return account_num, account_name
            return None
        except Exception as e:
            print(f"❌ خطأ في استخراج معلومات الحساب: {str(e)}")
            return None

    def _create_new_table(self, ws, account_num, account_name):
        """إنشاء جدول جديد مع فحص إمكانية الإضافة"""
        try:
            print("📝 بدء إنشاء جدول جديد...")

            # فحص إمكانية إضافة جدول جديد
            can_add_table, reason = self._can_add_new_table(ws)

            if not can_add_table:
                print(f"❌ لا يمكن إضافة جدول جديد: {reason}")
                messagebox.showwarning("تحذير", f"لا يمكن إضافة جدول جديد:\n{reason}")
                return False

            # العثور على آخر صف مستخدم
            last_row = self._find_last_used_row(ws)
            new_table_start = last_row + 3  # ترك مسافة 3 صفوف

            print(f"📍 آخر صف مستخدم: {last_row}, بداية الجدول الجديد: {new_table_start}")

            # تحديد نوع التنسيق
            format_type = self._detect_format_type(ws)

            if format_type == "official":
                return self._create_new_official_table(ws, account_num, account_name, new_table_start)
            else:
                return self._create_new_classic_table(ws, account_num, account_name, new_table_start)

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول جديد: {str(e)}")
            return False

    def _can_add_new_table(self, ws):
        """فحص إمكانية إضافة جدول جديد"""
        try:
            # 1. فحص عدد الجداول الموجودة
            existing_tables_count = self._count_existing_tables(ws)
            max_tables_per_sheet = 10  # حد أقصى مقترح

            if existing_tables_count >= max_tables_per_sheet:
                return False, f"تم الوصول للحد الأقصى من الجداول ({max_tables_per_sheet})"

            # 2. فحص المساحة المتاحة
            last_row = self._find_last_used_row(ws)
            max_excel_rows = 1048576  # حد Excel الأقصى
            required_rows = 35  # عدد الصفوف المطلوبة لجدول جديد

            if last_row + required_rows > max_excel_rows:
                return False, f"لا توجد مساحة كافية في الورقة (مطلوب {required_rows} صف)"

            # 3. فحص إذا كان الجدول الحالي ممتلئ فعلاً
            if not self._is_current_table_full(ws):
                return False, "الجدول الحالي ليس ممتلئاً بعد. يمكن إضافة مزيد من المستندات."

            print(f"✅ يمكن إضافة جدول جديد. الجداول الموجودة: {existing_tables_count}")
            return True, "يمكن إضافة جدول جديد"

        except Exception as e:
            print(f"❌ خطأ في فحص إمكانية الإضافة: {str(e)}")
            return False, f"خطأ في الفحص: {str(e)}"

    def _count_existing_tables(self, ws):
        """عد الجداول الموجودة في الورقة"""
        try:
            table_count = 0

            # البحث عن عناوين الجداول (عادة تحتوي على "حساب" أو "تابع")
            for row in range(1, ws.max_row + 1):
                for col in range(1, 19):  # فحص الأعمدة الرئيسية
                    cell = ws.cell(row=row, column=col)
                    if cell.value and isinstance(cell.value, str):
                        cell_text = str(cell.value).strip()
                        if ('حساب' in cell_text or 'تابع' in cell_text) and 'رقم' not in cell_text:
                            table_count += 1
                            print(f"📊 وجد جدول في الصف {row}: {cell_text}")
                            break  # انتقال للصف التالي

            print(f"📊 عدد الجداول الموجودة: {table_count}")
            return table_count

        except Exception as e:
            print(f"❌ خطأ في عد الجداول: {str(e)}")
            return 0

    def _is_current_table_full(self, ws):
        """فحص إذا كان الجدول الحالي ممتلئ فعلاً"""
        try:
            # فحص جميع الأقسام الستة
            for section in range(6):
                col_start = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                # فحص إذا كان هناك مكان فارغ في هذا القسم
                for row in range(10, 32):  # صفوف المستندات
                    amount_cell = ws.cell(row=row, column=col_start)
                    doc_cell = ws.cell(row=row, column=col_start+1)
                    pay_cell = ws.cell(row=row, column=col_start+2)

                    # إذا وجدت خلية فارغة حقيقية، فالجدول ليس ممتلئ
                    if (self._is_cell_truly_empty(amount_cell.value) and
                        self._is_cell_truly_empty(doc_cell.value) and
                        self._is_cell_truly_empty(pay_cell.value)):
                        print(f"🔲 وجدت مكان فارغ في القسم {section+1}, الصف {row}")
                        return False

            print("✅ جميع الأقسام ممتلئة - يمكن إضافة جدول جديد")
            return True

        except Exception as e:
            print(f"❌ خطأ في فحص امتلاء الجدول: {str(e)}")
            return True  # في حالة الخطأ، افترض أنه ممتلئ

    def _find_last_used_row(self, ws):
        """العثور على آخر صف مستخدم في الورقة"""
        max_row = 1
        for row in ws.iter_rows():
            for cell in row:
                if cell.value is not None and str(cell.value).strip() != "":
                    max_row = max(max_row, cell.row)
        return max_row

    def _create_new_official_table(self, ws, account_num, account_name, start_row):
        """إنشاء جدول جديد بالتنسيق الرسمي مع الترحيل الصحيح"""
        try:
            print(f"📝 إنشاء جدول رسمي جديد في الصف {start_row}")

            # حساب الرصيد المرحل من الجدول السابق
            carried_balance = self._calculate_carried_balance(ws, start_row)
            print(f"💰 الرصيد المرحل: {carried_balance}")

            # تعريف الحدود
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # عنوان الجدول الجديد
            ws.merge_cells(f'A{start_row}:R{start_row}')
            ws[f'A{start_row}'] = f"تابع حساب: {account_name} - {account_num}"
            ws[f'A{start_row}'].font = Font(bold=True, size=12)
            ws[f'A{start_row}'].alignment = Alignment(horizontal='center')

            # رقم الحساب
            table_num_row = start_row + 1
            ws.merge_cells(f'A{table_num_row}:R{table_num_row}')
            ws[f'A{table_num_row}'] = "1/214"
            ws[f'A{table_num_row}'].font = Font(bold=True)
            ws[f'A{table_num_row}'].alignment = Alignment(horizontal='center')

            # عناوين الأعمدة
            headers_row1 = start_row + 2
            headers_row2 = start_row + 3

            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                # الصف الأول من العناوين
                ws[f'{col}{headers_row1}'] = "فلس/دينار"
                ws[f'{chr(ord(col) + 1)}{headers_row1}'] = "مستند"
                ws[f'{chr(ord(col) + 2)}{headers_row1}'] = "رقم"

                # الصف الثاني من العناوين
                ws[f'{chr(ord(col) + 1)}{headers_row2}'] = "الصرف"
                ws[f'{chr(ord(col) + 2)}{headers_row2}'] = "التأدية"

                # تنسيق العناوين
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    if j == 0 or headers_row1 == start_row + 2:
                        cell = ws[f'{col_letter}{headers_row1}']
                        cell.font = Font(bold=True, size=9)
                        cell.alignment = Alignment(horizontal='center')
                        cell.border = thin_border

                    if j > 0:
                        cell = ws[f'{col_letter}{headers_row2}']
                        cell.font = Font(bold=True, size=9)
                        cell.alignment = Alignment(horizontal='center')
                        cell.border = thin_border

            # صف الرصيد (ترحيل من الجدول السابق) - محسن
            balance_row = start_row + 4
            for i in range(6):
                col = chr(65 + (i * 3))

                if i == 0:  # العمود الأول - الرصيد المرحل
                    ws[f'{col}{balance_row}'] = carried_balance
                    ws[f'{chr(ord(col) + 1)}{balance_row}'] = "ما قبله"
                    ws[f'{chr(ord(col) + 2)}{balance_row}'] = ""
                    print(f"✅ تم إضافة الرصيد المرحل {carried_balance} في {col}{balance_row}")
                else:  # الأعمدة الأخرى - صيغة من العمود السابق
                    prev_col = chr(ord(col) - 3)
                    ws[f'{col}{balance_row}'] = f"={prev_col}{balance_row + 22}"  # من صف المجموع في العمود السابق
                    ws[f'{chr(ord(col) + 1)}{balance_row}'] = "ما قبله"
                    ws[f'{chr(ord(col) + 2)}{balance_row}'] = ""

                # تنسيق
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}{balance_row}']
                    cell.border = thin_border
                    cell.alignment = Alignment(horizontal='center')

            # صفوف البيانات (20 صف)
            data_start = balance_row + 1
            data_end = data_start + 19  # 20 صف

            for i in range(6):
                col = chr(65 + (i * 3))
                for row in range(data_start, data_end + 1):
                    for j in range(3):
                        col_letter = chr(ord(col) + j)
                        cell = ws[f'{col_letter}{row}']
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center')

                        # تعيين تنسيق "عام" لأعمدة رقم المستند ورقم التأدية
                        if j == 1 or j == 2:  # عمود رقم المستند أو رقم التأدية
                            cell.number_format = 'General'  # تنسيق عام

            # صف المجاميع مع صيغ شاملة لجميع الأعمدة
            sum_row = data_end + 1
            for i in range(6):
                col = chr(65 + (i * 3))

                # صيغ الجمع لجميع الأعمدة الثلاثة في القسم
                for j in range(3):  # الأعمدة الثلاثة في كل قسم
                    col_letter = chr(ord(col) + j)

                    if j == 0:  # عمود المبلغ
                        # جميع الأقسام تشمل الرصيد المرحل
                        ws[f'{col_letter}{sum_row}'] = f"=SUM({col_letter}{balance_row}:{col_letter}{data_end})"
                        ws[f'{col_letter}{sum_row}'].font = Font(bold=True)
                        ws[f'{col_letter}{sum_row}'].number_format = '#,##0.000'

                    elif j == 1:  # عمود رقم المستند
                        ws[f'{col_letter}{sum_row}'] = "الإجمالي"
                        ws[f'{col_letter}{sum_row}'].font = Font(bold=True)

                    else:  # عمود رقم التأدية
                        ws[f'{col_letter}{sum_row}'] = f"=COUNTA({col_letter}{data_start}:{col_letter}{data_end})"  # عدد المستندات
                        ws[f'{col_letter}{sum_row}'].font = Font(bold=True)

                    # تنسيق موحد لجميع الخلايا
                    cell = ws[f'{col_letter}{sum_row}']
                    cell.border = Border(
                        top=Side(style='double'),
                        bottom=Side(style='double'),
                        left=Side(style='thin'),
                        right=Side(style='thin')
                    )
                    cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')

            print(f"✅ تم إنشاء جدول رسمي جديد بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول رسمي جديد: {str(e)}")
            return False

    def _create_new_classic_table(self, ws, account_num, account_name, start_row):
        """إنشاء جدول جديد بالتنسيق الكلاسيكي"""
        try:
            print(f"📝 إنشاء جدول كلاسيكي جديد في الصف {start_row}")

            # تعريف الحدود
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # عنوان الجدول الجديد
            ws.merge_cells(f'A{start_row}:R{start_row}')
            ws[f'A{start_row}'] = f"تابع حساب: {account_name} - {account_num}"
            ws[f'A{start_row}'].font = Font(bold=True, size=12)
            ws[f'A{start_row}'].alignment = Alignment(horizontal='center')

            # عنوان الأقسام
            sections_row = start_row + 1
            ws.merge_cells(f'A{sections_row}:R{sections_row}')
            ws[f'A{sections_row}'] = "سجل المستندات والحوالات المالية"
            ws[f'A{sections_row}'].font = Font(bold=True)
            ws[f'A{sections_row}'].alignment = Alignment(horizontal='center')

            # عناوين الأعمدة
            headers_row = start_row + 2
            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                ws[f'{col}{headers_row}'] = "المبلغ"
                ws[f'{chr(ord(col) + 1)}{headers_row}'] = "مستند الصرف"
                ws[f'{chr(ord(col) + 2)}{headers_row}'] = "رقم التأدية"

                # تنسيق العناوين
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}{headers_row}']
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')
                    cell.border = thin_border

            # صف الرصيد (ترحيل من الجدول السابق)
            balance_row = start_row + 3
            for i in range(6):
                col = chr(65 + (i * 3))
                prev_table_sum_row = start_row - 3  # افتراض أن صف المجموع في الجدول السابق

                ws[f'{col}{balance_row}'] = f"={col}{prev_table_sum_row}"
                ws[f'{chr(ord(col) + 1)}{balance_row}'] = "ما قبله"
                ws[f'{chr(ord(col) + 2)}{balance_row}'] = ""

                # تنسيق
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}{balance_row}']
                    cell.border = thin_border
                    cell.alignment = Alignment(horizontal='center')

            # صفوف البيانات (23 صف)
            data_start = balance_row + 1
            data_end = data_start + 22  # 23 صف

            for i in range(6):
                col = chr(65 + (i * 3))
                for row in range(data_start, data_end + 1):
                    for j in range(3):
                        col_letter = chr(ord(col) + j)
                        cell = ws[f'{col_letter}{row}']
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center')

                        # تعيين تنسيق "عام" لأعمدة رقم المستند ورقم التأدية
                        if j == 1 or j == 2:  # عمود رقم المستند أو رقم التأدية
                            cell.number_format = 'General'  # تنسيق عام

            # صف المجاميع مع صيغ شاملة
            sum_label_row = data_end + 1
            sum_row = data_end + 2

            for i in range(6):
                col = chr(65 + (i * 3))

                # عنوان المجموع
                ws.merge_cells(f'{col}{sum_label_row}:{chr(ord(col)+2)}{sum_label_row}')
                ws[f'{col}{sum_label_row}'] = "المجموع"
                ws[f'{col}{sum_label_row}'].font = Font(bold=True)
                ws[f'{col}{sum_label_row}'].fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
                ws[f'{col}{sum_label_row}'].alignment = Alignment(horizontal='center')

                # صيغ الجمع لجميع الأعمدة الثلاثة
                for j in range(3):
                    col_letter = chr(ord(col) + j)

                    if j == 0:  # عمود المبلغ
                        ws[f'{col_letter}{sum_row}'] = f"=SUM({col_letter}{balance_row}:{col_letter}{data_end})"
                        ws[f'{col_letter}{sum_row}'].font = Font(bold=True)

                    elif j == 1:  # عمود رقم المستند
                        ws[f'{col_letter}{sum_row}'] = "الإجمالي"
                        ws[f'{col_letter}{sum_row}'].font = Font(bold=True)

                    else:  # عمود رقم التأدية
                        ws[f'{col_letter}{sum_row}'] = f"=COUNTA({col_letter}{data_start}:{col_letter}{data_end})"
                        ws[f'{col_letter}{sum_row}'].font = Font(bold=True)

                    # تنسيق موحد
                    cell = ws[f'{col_letter}{sum_row}']
                    cell.border = Border(
                        top=Side(style='double'),
                        bottom=Side(style='double'),
                        left=Side(style='thin'),
                        right=Side(style='thin')
                    )
                    cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')

            print(f"✅ تم إنشاء جدول كلاسيكي جديد بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول كلاسيكي جديد: {str(e)}")
            return False

    def diagnose_account(self, sheet_name):
        """تشخيص حالة الحساب"""
        try:
            print(f"🔍 تشخيص الحساب: {sheet_name}")

            if sheet_name not in self.workbook.sheetnames:
                print(f"❌ الحساب غير موجود")
                return False

            ws = self.workbook[sheet_name]

            print(f"📊 تحليل الحساب:")
            print(f"  اسم الورقة: {sheet_name}")
            print(f"  أبعاد الورقة: {ws.max_row} صف × {ws.max_column} عمود")

            # فحص كل قسم
            for i in range(6):
                col_start = chr(65 + (i * 3))
                col_num = ord(col_start) - 64

                print(f"\n  القسم {i+1} (العمود {col_start}):")

                # فحص الرصيد الافتتاحي
                opening_value = ws.cell(row=9, column=col_num).value
                print(f"    الرصيد الافتتاحي (صف 9): {opening_value}")

                # فحص المستندات
                document_count = 0
                empty_count = 0

                for row in range(10, 32):
                    amount = ws.cell(row=row, column=col_num).value
                    doc_num = ws.cell(row=row, column=col_num+1).value
                    pay_num = ws.cell(row=row, column=col_num+2).value

                    if amount is not None and amount != "":
                        document_count += 1
                        print(f"      صف {row}: {amount} | {doc_num} | {pay_num}")
                    else:
                        empty_count += 1

                print(f"    المستندات الموجودة: {document_count}")
                print(f"    الخلايا الفارغة: {empty_count}")

                # فحص المجموع
                total_value = ws.cell(row=33, column=col_num).value
                print(f"    المجموع (صف 33): {total_value}")

            return True

        except Exception as e:
            print(f"❌ خطأ في تشخيص الحساب: {str(e)}")
            return False

    def diagnose_balance_transfer_mechanism(self, sheet_name):
        """تشخيص شامل لآلية الترحيل في الحساب"""
        try:
            print(f"🔍 تشخيص آلية الترحيل للحساب: {sheet_name}")
            print("=" * 80)

            if sheet_name not in self.workbook.sheetnames:
                print(f"❌ الحساب غير موجود")
                return False

            ws = self.workbook[sheet_name]

            # 1. فحص الرصيد الافتتاحي
            print("\n1️⃣ فحص الرصيد الافتتاحي:")
            opening_balance = 0
            for location in ['A8', 'A9', 'A7']:
                try:
                    cell = ws[location]
                    if cell.value and isinstance(cell.value, (int, float)):
                        opening_balance = float(cell.value)
                        print(f"  ✅ رصيد افتتاحي في {location}: {opening_balance:,.2f}")
                        break
                except:
                    continue

            if opening_balance == 0:
                print("  ⚠️ لم يتم العثور على رصيد افتتاحي")

            # 2. عد الجداول وتحليلها
            print("\n2️⃣ تحليل الجداول:")
            tables_count = self._count_existing_tables(ws)
            print(f"  📊 عدد الجداول: {tables_count}")

            # 3. تحليل كل جدول
            print("\n3️⃣ تحليل تفصيلي للجداول:")
            table_ranges = self._identify_table_ranges(ws)

            total_documents = 0
            total_amount = 0

            for i, (start_row, end_row) in enumerate(table_ranges, 1):
                print(f"\n  📊 الجدول {i} (الصفوف {start_row}-{end_row}):")

                table_docs, table_amount = self._analyze_table_range(ws, start_row, end_row)
                total_documents += table_docs
                total_amount += table_amount

                print(f"    📝 عدد المستندات: {table_docs}")
                print(f"    💰 مجموع المبالغ: {table_amount:,.2f}")

                # فحص آلية الترحيل لهذا الجدول
                if i > 1:  # ليس الجدول الأول
                    carried_balance = self._calculate_carried_balance(ws, start_row)
                    print(f"    🔄 الرصيد المرحل: {carried_balance:,.2f}")

            # 4. الملخص النهائي
            print("\n4️⃣ الملخص النهائي:")
            final_balance = opening_balance + total_amount
            print(f"  💰 الرصيد الافتتاحي: {opening_balance:,.2f}")
            print(f"  📝 إجمالي المستندات: {total_documents}")
            print(f"  💵 إجمالي المبالغ: {total_amount:,.2f}")
            print(f"  🏆 الرصيد النهائي: {final_balance:,.2f}")

            # 5. فحص إمكانية إضافة جدول جديد
            print("\n5️⃣ فحص إمكانية إضافة جدول جديد:")
            can_add, reason = self._can_add_new_table(ws)
            print(f"  {'✅' if can_add else '❌'} {reason}")

            print("\n" + "=" * 80)
            return True

        except Exception as e:
            print(f"❌ خطأ في تشخيص آلية الترحيل: {str(e)}")
            return False

    def _identify_table_ranges(self, ws):
        """تحديد نطاقات الجداول في الورقة"""
        try:
            table_ranges = []
            current_table_start = None

            for row in range(1, ws.max_row + 1):
                # البحث عن بداية جدول (عنوان يحتوي على "حساب" أو "تابع")
                for col in range(1, 19):
                    cell = ws.cell(row=row, column=col)
                    if cell.value and isinstance(cell.value, str):
                        cell_text = str(cell.value).strip()
                        if ('حساب' in cell_text or 'تابع' in cell_text) and 'رقم' not in cell_text:
                            if current_table_start is not None:
                                # إنهاء الجدول السابق
                                table_ranges.append((current_table_start, row - 1))
                            current_table_start = row
                            break

            # إضافة الجدول الأخير
            if current_table_start is not None:
                table_ranges.append((current_table_start, ws.max_row))

            return table_ranges

        except Exception as e:
            print(f"❌ خطأ في تحديد نطاقات الجداول: {str(e)}")
            return []

    def _analyze_table_range(self, ws, start_row, end_row):
        """تحليل نطاق جدول محدد"""
        try:
            documents_count = 0
            total_amount = 0

            # فحص جميع الأقسام في هذا النطاق
            for section in range(6):
                col_start = 1 + (section * 3)

                for row in range(start_row, min(end_row + 1, ws.max_row + 1)):
                    try:
                        amount_cell = ws.cell(row=row, column=col_start)
                        doc_cell = ws.cell(row=row, column=col_start+1)
                        pay_cell = ws.cell(row=row, column=col_start+2)

                        # فحص إذا كان هذا مستند حقيقي
                        if (amount_cell.value and isinstance(amount_cell.value, (int, float)) and amount_cell.value > 0):
                            # فحص إذا كان هناك رقم مستند أو رقم تأدية
                            if ((doc_cell.value and str(doc_cell.value).strip() not in ['', 'رقم المستند', 'رصيد افتتاحي', 'ما قبله']) or
                                (pay_cell.value and str(pay_cell.value).strip() not in ['', 'رقم القبض', 'رقم التأدية'])):
                                documents_count += 1
                                total_amount += float(amount_cell.value)
                    except:
                        continue

            return documents_count, total_amount

        except Exception as e:
            print(f"❌ خطأ في تحليل نطاق الجدول: {str(e)}")
            return 0, 0

    def create_report(self):
        """إنشاء تقرير المجاميع"""
        if 'التقارير' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['التقارير'])

        ws = self.workbook.create_sheet('التقارير')
        ws.sheet_properties.rightToLeft = True

        # إعداد العناوين
        headers = ['الحساب', 'الرصيد الافتتاحي', 'مجموع المستندات', 'الرصيد النهائي']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # إضافة الحسابات
        row = 2
        total_opening = 0
        total_documents = 0
        total_final = 0

        for sheet_name in self.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                ws_account = self.workbook[sheet_name]

                # الرصيد الافتتاحي (أول قيمة في القسم الأول)
                opening_balance = ws_account['A9'].value or 0
                total_opening += opening_balance

                # مجموع المستندات (مجموع جميع الأقسام)
                documents_total = 0
                for i in range(6):
                    col = chr(65 + (i * 3))  # A, D, G, J, M, P
                    section_total = ws_account[f'{col}33'].value
                    if isinstance(section_total, (int, float)):
                        documents_total += section_total
                total_documents += documents_total

                # الرصيد النهائي
                final_balance = opening_balance + documents_total
                total_final += final_balance

                # إضافة الصف
                ws.cell(row=row, column=1).value = sheet_name
                ws.cell(row=row, column=2).value = opening_balance
                ws.cell(row=row, column=3).value = documents_total
                ws.cell(row=row, column=4).value = final_balance

                # تنسيق الخلايا
                for col in range(1, 5):
                    ws.cell(row=row, column=col).border = Border(all=Side(style='thin'))

                row += 1

        # إضافة المجاميع
        ws.cell(row=row, column=1).value = "المجموع"
        ws.cell(row=row, column=2).value = total_opening
        ws.cell(row=row, column=3).value = total_documents
        ws.cell(row=row, column=4).value = total_final

        # تنسيق صف المجموع
        for col in range(1, 5):
            cell = ws.cell(row=row, column=col)
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")

        # تنسيق عرض الأعمدة
        for col in range(1, 5):
            ws.column_dimensions[chr(64 + col)].width = 20

        self.save_workbook()
        return True

    def create_documents_report(self, account_name=None):
        """إنشاء تقرير المستندات"""
        sheet_name = 'تقرير المستندات'
        if sheet_name in self.workbook.sheetnames:
            self.workbook.remove(self.workbook[sheet_name])

        ws = self.workbook.create_sheet(sheet_name)
        ws.sheet_properties.rightToLeft = True

        # إعداد العناوين
        headers = ['الحساب', 'المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        row = 2
        # تجميع المستندات من كل حساب
        for sheet_name in self.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                if account_name and sheet_name != account_name:
                    continue

                ws_account = self.workbook[sheet_name]
                for section in range(6):  # الأقسام الستة
                    col_start = 1 + (section * 3)
                    for doc_row in range(10, 33):
                        amount = ws_account.cell(row=doc_row, column=col_start).value
                        doc_num = ws_account.cell(row=doc_row, column=col_start+1).value
                        pay_num = ws_account.cell(row=doc_row, column=col_start+2).value

                        if amount and doc_num and pay_num:  # إذا كان هناك مستند
                            ws.cell(row=row, column=1).value = sheet_name
                            ws.cell(row=row, column=2).value = amount
                            ws.cell(row=row, column=3).value = doc_num
                            ws.cell(row=row, column=4).value = pay_num
                            ws.cell(row=row, column=5).value = f"القسم {section + 1}"

                            # تنسيق الخلايا
                            for col in range(1, 6):
                                ws.cell(row=row, column=col).border = Border(all=Side(style='thin'))

                            row += 1

        # تنسيق العرض
        for col in range(1, 6):
            ws.column_dimensions[chr(64 + col)].width = 15

        self.save_workbook()
        return True

    def create_summary_report(self):
        """إنشاء تقرير أرصدة الحسابات مع الترحيل التلقائي"""
        try:
            print("📊 إنشاء تقرير أرصدة الحسابات...")

            # حذف التقرير القديم إن وجد
            report_sheet_name = 'أرصدة الحسابات'
            if report_sheet_name in self.workbook.sheetnames:
                self.workbook.remove(self.workbook[report_sheet_name])
                print("🗑️ تم حذف التقرير القديم")

            # إنشاء ورقة التقرير الجديدة
            ws = self.workbook.create_sheet(report_sheet_name, 0)  # في المقدمة
            ws.sheet_properties.rightToLeft = False  # من اليسار لليمين
            print("✅ تم إنشاء ورقة التقرير الجديدة")

            # إعداد التقرير الجديد
            self._setup_new_summary_report(ws)

            print("✅ تم إنشاء تقرير أرصدة الحسابات بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير الإجمالي: {str(e)}")
            return False

    def _setup_new_summary_report(self, ws):
        """إعداد تقرير أرصدة الحسابات الجديد"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            # عنوان التقرير
            ws.merge_cells('A1:E1')
            ws['A1'] = "أرصدة الحسابات للمواد"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].alignment = Alignment(horizontal='center')

            # عناوين الأعمدة
            ws['A3'] = "المادة"
            ws['B3'] = ""
            ws['C3'] = ""
            ws['D3'] = ""
            ws['E3'] = "المبلغ"

            # تنسيق عناوين الأعمدة
            for col in ['A', 'E']:
                ws[f'{col}3'].font = Font(bold=True)
                ws[f'{col}3'].alignment = Alignment(horizontal='center')
                ws[f'{col}3'].fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # قائمة الحسابات المحددة مسبقاً
            accounts_list = [
                ("عمولات بنكية 83", "83"),
                ("أنظمة تشغيل 512/15", "512-15"),
                ("خدمات الاتصالات 202", "202"),
                ("الأيجارات 201", "201"),
                ("حوافز كوادرالصحه 116/3", "116-3"),
                ("حوافز كوادر الصحة146-214", "146-214"),
                ("حوافز لغير مشمولين 116/4", "116-4"),
                ("مكافات لجان العاملة بالتأمين 116/6", "116-6"),
                ("مكافات مؤظفين التأمين الصحي 116/8", "116-8"),
                ("أجهزة طبية 402/2", "402-2"),
                ("نفقات زراعة قوقعة 214/25", "214-25"),
                ("المياه 203", "203"),
                ("التدفئة 1/205", "205-1"),
                ("محروقات سيارات صالون 2/205", "205-2"),
                ("بعثات ودورات 303", "303"),
                ("كهرباء 204", "204"),
                ("صيانة سيارات 207", "207"),
                ("اجهزة حواسيب 1/505", "505-1"),
                ("صيانه واصلاحات ابنيه 208", "208"),
                ("قرطاسية 209", "209"),
                ("صيدليات 24/210", "210-24"),
                ("السفر والمهمات 213", "213"),
                ("حالات الطارئة 41/214", "214-41"),
                ("مستشفيات القطاع الخاص 41/214", "214-41 مستشفيات"),
                ("معالجة م. الخدمات الطبية 40/214", "214-40"),
                ("مركز السكري 43/214", "214-43"),
                ("معالجه بالخارج 44/214", "214-44"),
                ("التزامات سابقه 45/214", "214-45"),
                ("معالجه م. الحسين للسرطان 42/214", "214-42"),
                ("اجور تدقيق سكوب52/214", "214-52"),
                ("التامين212", "212"),
                ("ن. معلجه م.المؤسس38/214", "214-38"),
                ("ن. معالجه م.الجامعه الاردنيه39/214", "214-39"),
                ("نفقات اصدار بطاقات46/214", "214-46"),
                ("نظارات طبيه26/214", "214-26"),
                ("حملات توعية واعلانات 47/214", "214-47"),
                ("مستشفيات كلى 90/214", "214-90"),
                ("رديات سنوات سابقه 306", "306"),
                ("حفلات 214/001", "214-001"),
                ("مستهلكات طبية 21/210", "210-21"),
                ("شبكة حياة 151/214", "214-151"),
                ("مكافاة لغير الموظفين 305", "305"),
                ("مكافأت تدقيق المطالبات المالية 116/7", "116-7"),
                ("صيانة الالات 206", "206")
            ]

            # إضافة بيانات الحسابات
            row = 4
            for account_display, account_key in accounts_list:
                ws[f'A{row}'] = account_display

                # البحث عن الحساب في الملف
                account_found = False
                for sheet_name in self.workbook.sheetnames:
                    if sheet_name.startswith(account_key + "-") or sheet_name == account_key:
                        # استخدام صيغة للربط مع الحساب
                        ws[f'E{row}'] = f"=[{self.file_path}]{sheet_name}!P34"
                        account_found = True
                        break

                if not account_found:
                    # إذا لم يتم العثور على الحساب
                    ws[f'E{row}'] = 0

                row += 1

            # إضافة المجموع الإجمالي
            ws[f'A{row}'] = "المجموع الاجمالي"
            ws[f'E{row}'] = f"=SUM(E4:E{row-1})"
            ws[f'F{row}'] = "مجموع التنسيق"

            # إضافة مجموع اليومية
            row += 1
            ws[f'E{row}'] = 0
            ws[f'F{row}'] = "مجموع اليومية"

            # إضافة الفرق
            row += 1
            ws[f'E{row}'] = f"=E{row-2}-E{row-1}"
            ws[f'F{row}'] = "الفرق"

            # إضافة قسم رديات أمانات
            row += 3
            ws[f'A{row}'] = "رديات أمانات"
            ws[f'A{row}'].font = Font(bold=True)

            # تنسيق عام
            for row_num in range(1, row + 5):
                for col in ['A', 'B', 'C', 'D', 'E', 'F']:
                    cell = ws[f'{col}{row_num}']
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )

            # تعيين عرض الأعمدة
            ws.column_dimensions['A'].width = 40
            ws.column_dimensions['B'].width = 10
            ws.column_dimensions['C'].width = 10
            ws.column_dimensions['D'].width = 10
            ws.column_dimensions['E'].width = 15
            ws.column_dimensions['F'].width = 20

            print("✅ تم إعداد تقرير أرصدة الحسابات")

        except Exception as e:
            print(f"❌ خطأ في إعداد تقرير أرصدة الحسابات: {str(e)}")
            raise

    def _setup_report_header(self, ws):
        """إعداد ترويسة التقرير"""
        try:
            # العنوان الرئيسي
            ws.merge_cells('A1:F1')
            ws['A1'] = "المملكة الأردنية الهاشمية"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].alignment = Alignment(horizontal='center')

            # وزارة الصحة
            ws.merge_cells('A2:F2')
            ws['A2'] = "وزارة الصحة"
            ws['A2'].font = Font(bold=True, size=12)
            ws['A2'].alignment = Alignment(horizontal='center')

            # عنوان التقرير
            ws.merge_cells('A3:F3')
            ws['A3'] = "التقرير الإجمالي لأرصدة الحسابات"
            ws['A3'].font = Font(bold=True, size=12)
            ws['A3'].alignment = Alignment(horizontal='center')

            # تاريخ التقرير
            from datetime import datetime
            ws.merge_cells('A4:F4')
            ws['A4'] = f"تاريخ التقرير: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
            ws['A4'].font = Font(size=10)
            ws['A4'].alignment = Alignment(horizontal='center')

        except Exception as e:
            print(f"❌ خطأ في إعداد ترويسة التقرير: {str(e)}")
            raise

    def _setup_report_columns(self, ws):
        """إعداد عناوين أعمدة التقرير"""
        try:
            headers = [
                'رقم الحساب',
                'اسم الحساب',
                'الرصيد الافتتاحي',
                'مجموع المستندات',
                'الرصيد النهائي',
                'عدد المستندات'
            ]

            # إضافة العناوين في الصف 6
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=6, column=col)
                cell.value = header
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(all=Side(style='thin'))

            print("✅ تم إعداد عناوين الأعمدة")

        except Exception as e:
            print(f"❌ خطأ في إعداد عناوين الأعمدة: {str(e)}")
            raise

    def _add_accounts_data(self, ws):
        """إضافة بيانات الحسابات إلى التقرير"""
        try:
            row = 7  # بداية البيانات

            for sheet_name in self.workbook.sheetnames:
                if sheet_name in ['التقرير الإجمالي', 'التقارير', 'تقرير المستندات']:
                    continue

                print(f"📊 معالجة الحساب: {sheet_name}")

                # تحليل اسم الحساب
                if '-' in sheet_name:
                    account_num, account_name = sheet_name.split('-', 1)
                else:
                    account_num = sheet_name
                    account_name = sheet_name

                ws_account = self.workbook[sheet_name]

                # الرصيد الافتتاحي (من الخلية A9)
                opening_balance = ws_account['A9'].value or 0
                if isinstance(opening_balance, str):
                    opening_balance = 0

                # حساب مجموع المستندات وعددها
                documents_total = 0
                documents_count = 0

                # فحص جميع الأقسام الستة
                for i in range(6):
                    col_letter = chr(65 + (i * 3))  # A, D, G, J, M, P

                    # مجموع القسم من الخلية 33
                    section_total = ws_account[f'{col_letter}33'].value
                    if isinstance(section_total, (int, float)):
                        documents_total += section_total

                    # عد المستندات في هذا القسم
                    for doc_row in range(10, 32):  # صفوف المستندات
                        amount = ws_account.cell(row=doc_row, column=ord(col_letter)-64).value
                        if amount and isinstance(amount, (int, float)) and amount != 0:
                            documents_count += 1

                # الرصيد النهائي
                final_balance = opening_balance + documents_total

                # إضافة البيانات إلى التقرير
                ws.cell(row=row, column=1).value = account_num
                ws.cell(row=row, column=2).value = account_name
                ws.cell(row=row, column=3).value = opening_balance
                ws.cell(row=row, column=4).value = documents_total
                ws.cell(row=row, column=5).value = final_balance
                ws.cell(row=row, column=6).value = documents_count

                # تنسيق الصف
                for col in range(1, 7):
                    cell = ws.cell(row=row, column=col)
                    cell.border = Border(all=Side(style='thin'))
                    cell.alignment = Alignment(horizontal='center')

                    # تنسيق الأرقام
                    if col in [3, 4, 5]:  # أعمدة المبالغ
                        cell.number_format = '#,##0.00'

                row += 1

            # حفظ رقم آخر صف للمجاميع
            self.last_data_row = row - 1
            print(f"✅ تم إضافة بيانات {self.last_data_row - 6} حساب")

        except Exception as e:
            print(f"❌ خطأ في إضافة بيانات الحسابات: {str(e)}")
            raise

    def _add_report_totals(self, ws):
        """إضافة المجاميع النهائية للتقرير"""
        try:
            if not hasattr(self, 'last_data_row'):
                return

            totals_row = self.last_data_row + 2

            # عنوان المجاميع
            ws.merge_cells(f'A{totals_row}:B{totals_row}')
            ws[f'A{totals_row}'] = "المجموع الكلي"
            ws[f'A{totals_row}'].font = Font(bold=True)
            ws[f'A{totals_row}'].alignment = Alignment(horizontal='center')
            ws[f'A{totals_row}'].fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")

            # حساب المجاميع
            start_row = 7
            end_row = self.last_data_row

            # مجموع الأرصدة الافتتاحية
            ws[f'C{totals_row}'] = f"=SUM(C{start_row}:C{end_row})"

            # مجموع المستندات
            ws[f'D{totals_row}'] = f"=SUM(D{start_row}:D{end_row})"

            # مجموع الأرصدة النهائية
            ws[f'E{totals_row}'] = f"=SUM(E{start_row}:E{end_row})"

            # مجموع عدد المستندات
            ws[f'F{totals_row}'] = f"=SUM(F{start_row}:F{end_row})"

            # تنسيق صف المجاميع
            for col in range(1, 7):
                cell = ws.cell(row=totals_row, column=col)
                cell.font = Font(bold=True)
                cell.border = Border(all=Side(style='thick'))
                cell.fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')

                # تنسيق الأرقام
                if col in [3, 4, 5]:
                    cell.number_format = '#,##0.00'

            print("✅ تم إضافة المجاميع النهائية")

        except Exception as e:
            print(f"❌ خطأ في إضافة المجاميع: {str(e)}")
            raise

    def _format_report(self, ws):
        """تنسيق التقرير النهائي"""
        try:
            # تعيين عرض الأعمدة
            column_widths = {
                'A': 15,  # رقم الحساب
                'B': 25,  # اسم الحساب
                'C': 18,  # الرصيد الافتتاحي
                'D': 18,  # مجموع المستندات
                'E': 18,  # الرصيد النهائي
                'F': 15   # عدد المستندات
            }

            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width

            # تجميد الصفوف العلوية
            ws.freeze_panes = 'A7'

            print("✅ تم تنسيق التقرير")

        except Exception as e:
            print(f"❌ خطأ في تنسيق التقرير: {str(e)}")
            raise

    def export_all_accounts_to_excel_reference_format(self):
        """تصدير جميع الحسابات إلى ملف Excel بالتنسيق المرجعي"""
        try:
            from datetime import datetime
            import os

            print("📊 [آلية مرجعية] بدء تصدير جميع الحسابات...")

            # فلترة الحسابات
            special_sheets = ['مرحباً', 'التقارير', 'تقرير المستندات', 'التقرير الإجمالي']
            accounts = [sheet for sheet in self.workbook.sheetnames if sheet not in special_sheets]

            if not accounts:
                print("❌ لا توجد حسابات للتصدير")
                return False, None

            # إنشاء ملف جديد للتصدير
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_filename = f"نظام_المحاسبة_الكامل_{timestamp}.xlsx"

            # إنشاء ملف جديد
            export_wb = openpyxl.Workbook()

            # حذف الورقة الافتراضية
            if 'Sheet' in export_wb.sheetnames:
                export_wb.remove(export_wb['Sheet'])

            # إضافة ورقة ملخص عام
            summary_ws = export_wb.create_sheet("ملخص عام")
            self._create_export_summary_sheet(summary_ws, accounts)

            # تصدير كل حساب في ورقة منفصلة
            exported_count = 0
            for account_name in accounts:
                try:
                    print(f"📝 تصدير الحساب: {account_name}")

                    # إنشاء ورقة جديدة للحساب
                    account_ws = export_wb.create_sheet(account_name)

                    # نسخ بيانات الحساب بالتنسيق المرجعي
                    success = self._copy_account_with_reference_format(self.workbook[account_name], account_ws)

                    if success:
                        exported_count += 1
                        print(f"✅ تم تصدير {account_name} بنجاح")
                    else:
                        print(f"❌ فشل في تصدير {account_name}")

                except Exception as e:
                    print(f"❌ خطأ في تصدير {account_name}: {str(e)}")
                    continue

            # حفظ الملف
            try:
                export_wb.save(export_filename)
                print(f"✅ تم حفظ ملف التصدير: {export_filename}")
                print(f"📊 عدد الحسابات المصدرة: {exported_count}/{len(accounts)}")

                return True, export_filename

            except Exception as save_error:
                print(f"❌ خطأ في حفظ ملف التصدير: {str(save_error)}")
                return False, None

        except Exception as e:
            print(f"❌ خطأ عام في عملية التصدير: {str(e)}")
            return False, None

    def _create_export_summary_sheet(self, ws, accounts):
        """إنشاء ورقة ملخص عام للتصدير"""
        try:
            from datetime import datetime

            # إعداد الترويسة
            ws.merge_cells('A1:F1')
            ws['A1'] = "🏥 وزارة الصحة الأردنية - نظام إدارة المستندات المحاسبية"
            ws['A1'].font = Font(size=14, bold=True, color="1F4E79")
            ws['A1'].alignment = Alignment(horizontal='center')
            ws['A1'].fill = PatternFill(start_color="E7F3FF", end_color="E7F3FF", fill_type="solid")

            # معلومات التصدير
            ws['A3'] = f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            ws['A4'] = f"عدد الحسابات: {len(accounts)}"
            ws['A5'] = "التنسيق: مطابق للنظام المرجعي"

            # عناوين قائمة الحسابات
            ws['A7'] = "رقم"
            ws['B7'] = "اسم الحساب"
            ws['C7'] = "عدد المستندات"
            ws['D7'] = "إجمالي المبلغ"
            ws['E7'] = "حالة التصدير"

            # تنسيق العناوين
            for col in ['A', 'B', 'C', 'D', 'E']:
                ws[f'{col}7'].font = Font(bold=True)
                ws[f'{col}7'].fill = PatternFill(start_color="D9E2F3", end_color="D9E2F3", fill_type="solid")

            # قائمة الحسابات
            row = 8
            for i, account_name in enumerate(accounts, 1):
                ws[f'A{row}'] = i
                ws[f'B{row}'] = account_name
                ws[f'C{row}'] = "يتم حسابها تلقائياً"
                ws[f'D{row}'] = "يتم حسابها تلقائياً"
                ws[f'E{row}'] = "✅ تم التصدير"
                row += 1

            # تعيين عرض الأعمدة
            ws.column_dimensions['A'].width = 8
            ws.column_dimensions['B'].width = 40
            ws.column_dimensions['C'].width = 15
            ws.column_dimensions['D'].width = 15
            ws.column_dimensions['E'].width = 15

            print("✅ تم إنشاء ورقة الملخص العام")

        except Exception as e:
            print(f"❌ خطأ في إنشاء ورقة الملخص: {str(e)}")

    def _copy_account_with_reference_format(self, source_ws, target_ws):
        """نسخ حساب بالتنسيق المرجعي"""
        try:
            print(f"📝 نسخ بيانات الحساب بالتنسيق المرجعي...")

            # إعداد اتجاه النص من اليمين إلى اليسار
            target_ws.sheet_properties.rightToLeft = True

            # نسخ جميع البيانات والتنسيق
            for row in source_ws.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        target_cell = target_ws.cell(row=cell.row, column=cell.column)

                        # نسخ القيمة
                        target_cell.value = cell.value

                        # نسخ التنسيق
                        if cell.font:
                            target_cell.font = Font(
                                name=cell.font.name,
                                size=cell.font.size,
                                bold=cell.font.bold,
                                italic=cell.font.italic,
                                color=cell.font.color
                            )

                        if cell.alignment:
                            target_cell.alignment = Alignment(
                                horizontal=cell.alignment.horizontal,
                                vertical=cell.alignment.vertical
                            )

                        if cell.border:
                            target_cell.border = cell.border

                        if cell.fill:
                            target_cell.fill = cell.fill

                        if cell.number_format:
                            target_cell.number_format = cell.number_format

            # نسخ الخلايا المدمجة
            for merged_range in source_ws.merged_cells.ranges:
                target_ws.merge_cells(str(merged_range))

            # نسخ عرض الأعمدة
            for col_letter, col_dim in source_ws.column_dimensions.items():
                target_ws.column_dimensions[col_letter].width = col_dim.width

            # نسخ ارتفاع الصفوف
            for row_num, row_dim in source_ws.row_dimensions.items():
                target_ws.row_dimensions[row_num].height = row_dim.height

            print("✅ تم نسخ الحساب بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في نسخ الحساب: {str(e)}")
            return False

    def _setup_table_headers_at_position(self, ws, start_row):
        """إعداد عناوين الجدول في موقع محدد"""
        try:
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إعداد عناوين الأقسام الستة
            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                # الصف الأول من العناوين
                ws[f'{col}{start_row}'] = "فلس/دينار"
                ws[f'{chr(ord(col) + 1)}{start_row}'] = "مستند"
                ws[f'{chr(ord(col) + 2)}{start_row}'] = "رقم"

                # الصف الثاني من العناوين
                ws[f'{chr(ord(col) + 1)}{start_row + 1}'] = "الصرف"
                ws[f'{chr(ord(col) + 2)}{start_row + 1}'] = "التأدية"

                # تنسيق العناوين
                for j in range(3):
                    for row_offset in range(2):
                        col_letter = chr(ord(col) + j)
                        cell = ws[f'{col_letter}{start_row + row_offset}']
                        cell.font = Font(bold=True, size=9)
                        cell.alignment = Alignment(horizontal='center')
                        cell.border = thin_border

            print(f"✅ تم إعداد عناوين الجدول في الصف {start_row}")

        except Exception as e:
            print(f"❌ خطأ في إعداد عناوين الجدول: {str(e)}")

    def _setup_table_data_rows_at_position(self, ws, start_row):
        """إعداد صفوف بيانات الجدول في موقع محدد"""
        try:
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إعداد 20 صف للبيانات
            for i in range(6):  # 6 أقسام
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                for row in range(start_row, start_row + 20):  # 20 صف
                    for j in range(3):  # 3 أعمدة لكل قسم
                        col_letter = chr(ord(col) + j)
                        cell = ws[f'{col_letter}{row}']
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center')

                        # تعيين تنسيق عام لأعمدة رقم المستند ورقم التأدية
                        if j == 1 or j == 2:
                            cell.number_format = 'General'

            print(f"✅ تم إعداد صفوف بيانات الجدول في الصف {start_row}")

        except Exception as e:
            print(f"❌ خطأ في إعداد صفوف البيانات: {str(e)}")

    def _setup_table_totals_at_position(self, ws, start_row):
        """إعداد صف مجاميع الجدول في موقع محدد"""
        try:
            # إعداد صف المجاميع
            for i in range(6):  # 6 أقسام
                col = chr(65 + (i * 3))  # A, D, G, J, M, P

                # صيغ الجمع لجميع الأعمدة الثلاثة في القسم
                for j in range(3):
                    col_letter = chr(ord(col) + j)

                    if j == 0:  # عمود المبلغ
                        # جميع الأقسام تشمل الرصيد (افتتاحي أو مرحل)
                        data_start = start_row - 20  # بداية بيانات الجدول
                        ws[f'{col_letter}{start_row}'] = f"=SUM({col_letter}{data_start - 1}:{col_letter}{start_row - 1})"
                        ws[f'{col_letter}{start_row}'].font = Font(bold=True)
                        ws[f'{col_letter}{start_row}'].number_format = '#,##0.000'

                    elif j == 1:  # عمود رقم المستند
                        ws[f'{col_letter}{start_row}'] = "الإجمالي"
                        ws[f'{col_letter}{start_row}'].font = Font(bold=True)

                    else:  # عمود رقم التأدية
                        data_start = start_row - 20
                        ws[f'{col_letter}{start_row}'] = f"=COUNTA({col_letter}{data_start}:{col_letter}{start_row - 1})"
                        ws[f'{col_letter}{start_row}'].font = Font(bold=True)

                    # تنسيق موحد لجميع الخلايا
                    cell = ws[f'{col_letter}{start_row}']
                    cell.border = Border(
                        top=Side(style='double'),
                        bottom=Side(style='double'),
                        left=Side(style='thin'),
                        right=Side(style='thin')
                    )
                    cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')

            print(f"✅ تم إعداد صف المجاميع في الصف {start_row}")

        except Exception as e:
            print(f"❌ خطأ في إعداد صف المجاميع: {str(e)}")

    # =============================================================================
    # دوال إدارة الحسابات والمستندات الجديدة
    # =============================================================================

    def get_all_accounts(self):
        """جلب جميع الحسابات مع بياناتها الأساسية"""
        try:
            accounts = []
            special_sheets = ['مرحباً', 'التقارير', 'تقرير المستندات', 'التقرير الإجمالي', 'أرصدة الحسابات']

            for sheet_name in self.workbook.sheetnames:
                if sheet_name not in special_sheets and '-' in sheet_name:
                    try:
                        # استخراج رقم واسم الحساب
                        account_num, account_name = sheet_name.split('-', 1)

                        # حساب الرصيد
                        ws = self.workbook[sheet_name]
                        balance = self._calculate_account_balance_simple(ws)

                        accounts.append({
                            'sheet_name': sheet_name,
                            'account_num': account_num.strip(),
                            'account_name': account_name.strip(),
                            'balance': balance
                        })
                    except Exception as e:
                        print(f"⚠️ خطأ في معالجة الحساب {sheet_name}: {str(e)}")
                        continue

            return accounts

        except Exception as e:
            print(f"❌ خطأ في جلب الحسابات: {str(e)}")
            return []

    def _calculate_account_balance_simple(self, ws):
        """حساب رصيد الحساب بطريقة بسيطة"""
        try:
            total_balance = 0

            # فحص جميع الأقسام في جميع الجداول
            table_start_row = 8
            while table_start_row < 1000:
                if self._is_table_header_at_row(ws, table_start_row - 3):
                    # فحص الأقسام الستة
                    for section in range(6):
                        col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                        # فحص جميع الصفوف في هذا القسم
                        for row in range(table_start_row, table_start_row + 25):
                            amount_cell = ws.cell(row=row, column=col)
                            if amount_cell.value and isinstance(amount_cell.value, (int, float)):
                                total_balance += float(amount_cell.value)

                    table_start_row += 35
                else:
                    break

            return round(total_balance, 3)

        except Exception as e:
            print(f"❌ خطأ في حساب الرصيد: {str(e)}")
            return 0

    def get_account_documents(self, sheet_name):
        """جلب جميع مستندات حساب محدد"""
        try:
            if sheet_name not in self.workbook.sheetnames:
                return []

            ws = self.workbook[sheet_name]
            documents = []

            # فحص جميع الجداول في الحساب
            table_start_row = 8
            while table_start_row < 1000:
                if self._is_table_header_at_row(ws, table_start_row - 3):
                    # فحص جميع الأقسام في هذا الجدول
                    for section in range(6):
                        col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                        # فحص جميع الصفوف في هذا القسم
                        for row in range(table_start_row, table_start_row + 20):
                            amount_cell = ws.cell(row=row, column=col)
                            doc_cell = ws.cell(row=row, column=col+1)
                            pay_cell = ws.cell(row=row, column=col+2)

                            # فحص إذا كان هذا مستند حقيقي
                            if (amount_cell.value and isinstance(amount_cell.value, (int, float)) and
                                amount_cell.value != 0 and
                                doc_cell.value and str(doc_cell.value).strip() not in ['', 'رقم المستند', 'ما قبله']):

                                documents.append({
                                    'row': row,
                                    'col': col,
                                    'amount': float(amount_cell.value),
                                    'doc_num': str(doc_cell.value).strip() if doc_cell.value else '',
                                    'pay_num': str(pay_cell.value).strip() if pay_cell.value else '',
                                    'section': section + 1,
                                    'table': (table_start_row - 8) // 35 + 1
                                })

                    table_start_row += 35
                else:
                    break

            return documents

        except Exception as e:
            print(f"❌ خطأ في جلب مستندات الحساب: {str(e)}")
            return []

    def update_document(self, sheet_name, row, col, new_amount, new_doc_num, new_pay_num):
        """تعديل مستند موجود"""
        try:
            if sheet_name not in self.workbook.sheetnames:
                return False

            ws = self.workbook[sheet_name]

            # تحديث القيم
            ws.cell(row=row, column=col).value = float(new_amount)
            ws.cell(row=row, column=col+1).value = str(new_doc_num).strip()
            ws.cell(row=row, column=col+2).value = str(new_pay_num).strip()

            # حفظ التغييرات
            self.save_workbook()

            print(f"✅ تم تعديل المستند في الصف {row}")
            return True

        except Exception as e:
            print(f"❌ خطأ في تعديل المستند: {str(e)}")
            return False

    def delete_document(self, sheet_name, row, col):
        """حذف مستند موجود"""
        try:
            if sheet_name not in self.workbook.sheetnames:
                return False

            ws = self.workbook[sheet_name]

            # حذف القيم (جعلها فارغة)
            ws.cell(row=row, column=col).value = None
            ws.cell(row=row, column=col+1).value = None
            ws.cell(row=row, column=col+2).value = None

            # حفظ التغييرات
            self.save_workbook()

            print(f"✅ تم حذف المستند من الصف {row}")
            return True

        except Exception as e:
            print(f"❌ خطأ في حذف المستند: {str(e)}")
            return False

    def delete_account(self, sheet_name):
        """حذف حساب بالكامل مع جميع مستنداته"""
        try:
            if sheet_name not in self.workbook.sheetnames:
                return False

            # حذف الورقة
            del self.workbook[sheet_name]

            # حفظ التغييرات
            self.save_workbook()

            print(f"✅ تم حذف الحساب {sheet_name} بالكامل")
            return True

        except Exception as e:
            print(f"❌ خطأ في حذف الحساب: {str(e)}")
            return False

    def rename_account(self, old_sheet_name, new_account_num, new_account_name):
        """تعديل اسم ورقم الحساب"""
        try:
            if old_sheet_name not in self.workbook.sheetnames:
                return False

            # إنشاء اسم جديد للورقة
            new_sheet_name = f"{new_account_num.strip()}-{new_account_name.strip()}"

            # التحقق من عدم وجود الاسم الجديد
            if new_sheet_name in self.workbook.sheetnames and new_sheet_name != old_sheet_name:
                print(f"❌ اسم الحساب {new_sheet_name} موجود مسبقاً")
                return False

            # تغيير اسم الورقة
            ws = self.workbook[old_sheet_name]
            ws.title = new_sheet_name

            # تحديث عنوان الحساب في الورقة
            # البحث عن خلية عنوان الحساب وتحديثها
            for row in range(1, 10):
                for col in range(1, 10):
                    cell = ws.cell(row=row, column=col)
                    if cell.value and 'حساب' in str(cell.value):
                        # تحديث عنوان الحساب
                        ws.cell(row=row, column=col).value = f"حساب {new_account_num} - {new_account_name}"
                        break

            # حفظ التغييرات
            self.save_workbook()

            print(f"✅ تم تعديل اسم الحساب من {old_sheet_name} إلى {new_sheet_name}")
            return new_sheet_name

        except Exception as e:
            print(f"❌ خطأ في تعديل اسم الحساب: {str(e)}")
            return False

    # =============================================================================
    # دوال فحص تكرار المستندات المحسنة
    # =============================================================================

    def check_document_number_globally(self, doc_num):
        """فحص تكرار رقم المستند في جميع الحسابات (عالمياً)"""
        try:
            print(f"🔍 فحص تكرار رقم المستند عالمياً: {doc_num}")

            # تحويل إلى نص للمقارنة
            doc_num_str = str(doc_num).strip()

            # فحص جميع الحسابات
            for sheet_name in self.workbook.sheetnames:
                # تجاهل ورقة أرصدة الحسابات والتقارير
                if sheet_name in ["أرصدة الحسابات", "التقارير", "تقرير المستندات", "التقرير الإجمالي", "مرحباً"]:
                    continue

                try:
                    ws = self.workbook[sheet_name]
                    print(f"  🔍 فحص الحساب: {sheet_name}")

                    # فحص جميع الجداول في هذا الحساب
                    if self._check_document_number_in_sheet(ws, doc_num_str):
                        print(f"  ❌ تم العثور على رقم مستند مكرر في: {sheet_name}")
                        return sheet_name

                except Exception as e:
                    print(f"  ⚠️ خطأ في فحص الحساب {sheet_name}: {str(e)}")
                    continue

            print(f"  ✅ لا يوجد تكرار لرقم المستند في أي حساب")
            return None

        except Exception as e:
            print(f"❌ خطأ في فحص تكرار رقم المستند عالمياً: {str(e)}")
            return None

    def check_payment_number_in_account(self, sheet_name, pay_num):
        """فحص تكرار رقم التأدية في حساب محدد (محلياً)"""
        try:
            print(f"🔍 فحص تكرار رقم التأدية في الحساب: {sheet_name}")
            print(f"   رقم التأدية: {pay_num}")

            # تحويل إلى نص للمقارنة
            pay_num_str = str(pay_num).strip()

            # الحصول على ورقة الحساب
            if sheet_name not in self.workbook.sheetnames:
                return False

            ws = self.workbook[sheet_name]

            # فحص جميع الجداول في هذا الحساب
            duplicate_found = self._check_payment_number_in_sheet(ws, pay_num_str)
            if duplicate_found:
                print(f"  ❌ تم العثور على رقم تأدية مكرر في نفس الحساب")
                return True

            print(f"  ✅ لا يوجد تكرار لرقم التأدية في هذا الحساب")
            return False

        except Exception as e:
            print(f"❌ خطأ في فحص تكرار رقم التأدية: {str(e)}")
            return False

    def _check_document_number_in_sheet(self, ws, doc_num_str):
        """فحص تكرار رقم المستند في ورقة واحدة"""
        try:
            # فحص جميع الجداول في الورقة
            table_start_row = 8  # بداية الجدول الأول

            while table_start_row < 1000:  # حد أقصى للبحث
                # فحص وجود جدول في هذا الموقع
                if self._is_table_header_at_row(ws, table_start_row - 3):
                    # فحص جميع الأقسام في هذا الجدول
                    for section in range(6):
                        col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                        # فحص جميع الصفوف في هذا القسم
                        for row in range(table_start_row, table_start_row + 20):
                            doc_cell = ws.cell(row=row, column=col+1)  # عمود رقم المستند

                            # مقارنة رقم المستند
                            existing_doc = str(doc_cell.value).strip() if doc_cell.value else ""

                            # فحص التطابق
                            if (existing_doc == doc_num_str and existing_doc != "" and
                                existing_doc not in ["ما قبله", "رقم المستند"]):
                                return True

                    # الانتقال إلى الجدول التالي
                    table_start_row += 35  # ارتفاع الجدول الواحد
                else:
                    break

            return False

        except Exception as e:
            print(f"❌ خطأ في فحص رقم المستند في الورقة: {str(e)}")
            return False

    def _check_payment_number_in_sheet(self, ws, pay_num_str):
        """فحص تكرار رقم التأدية في ورقة واحدة"""
        try:
            # فحص جميع الجداول في الورقة
            table_start_row = 8  # بداية الجدول الأول

            while table_start_row < 1000:  # حد أقصى للبحث
                # فحص وجود جدول في هذا الموقع
                if self._is_table_header_at_row(ws, table_start_row - 3):
                    # فحص جميع الأقسام في هذا الجدول
                    for section in range(6):
                        col = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                        # فحص جميع الصفوف في هذا القسم
                        for row in range(table_start_row, table_start_row + 20):
                            pay_cell = ws.cell(row=row, column=col+2)  # عمود رقم التأدية

                            # مقارنة رقم التأدية
                            existing_pay = str(pay_cell.value).strip() if pay_cell.value else ""

                            # فحص التطابق
                            if (existing_pay == pay_num_str and existing_pay != "" and
                                existing_pay not in ["ما قبله", "رقم التأدية"]):
                                return True

                    # الانتقال إلى الجدول التالي
                    table_start_row += 35  # ارتفاع الجدول الواحد
                else:
                    break

            return False

        except Exception as e:
            print(f"❌ خطأ في فحص رقم التأدية في الورقة: {str(e)}")
            return False

    # =============================================================================
    # دوال التصدير والطباعة الجديدة
    # =============================================================================

    def export_account_to_excel(self, sheet_name, output_path=None):
        """تصدير حساب محدد إلى ملف Excel منفصل"""
        try:
            print(f"📤 تصدير الحساب: {sheet_name}")

            if sheet_name not in self.workbook.sheetnames:
                print(f"❌ الحساب {sheet_name} غير موجود")
                return False

            # إنشاء ملف جديد
            from openpyxl import Workbook
            new_workbook = Workbook()

            # حذف الورقة الافتراضية
            new_workbook.remove(new_workbook.active)

            # نسخ الورقة المطلوبة
            source_sheet = self.workbook[sheet_name]
            target_sheet = new_workbook.create_sheet(title=sheet_name)

            # نسخ جميع البيانات والتنسيق
            for row in source_sheet.iter_rows():
                for cell in row:
                    target_cell = target_sheet.cell(row=cell.row, column=cell.column)
                    target_cell.value = cell.value

                    # نسخ التنسيق
                    if cell.has_style:
                        target_cell.font = cell.font
                        target_cell.border = cell.border
                        target_cell.fill = cell.fill
                        target_cell.number_format = cell.number_format
                        target_cell.protection = cell.protection
                        target_cell.alignment = cell.alignment

            # نسخ عرض الأعمدة
            for i, column in enumerate(source_sheet.columns, 1):
                target_sheet.column_dimensions[target_sheet.cell(row=1, column=i).column_letter].width = \
                    source_sheet.column_dimensions[column[0].column_letter].width

            # نسخ ارتفاع الصفوف
            for i, row in enumerate(source_sheet.rows, 1):
                target_sheet.row_dimensions[i].height = source_sheet.row_dimensions[i].height

            # تحديد مسار الحفظ
            if not output_path:
                from tkinter import filedialog
                output_path = filedialog.asksaveasfilename(
                    title=f"حفظ تصدير الحساب: {sheet_name}",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    initialname=f"حساب_{sheet_name.replace('-', '_')}.xlsx"
                )

                if not output_path:
                    print("❌ تم إلغاء عملية التصدير")
                    return False

            # حفظ الملف
            new_workbook.save(output_path)

            print(f"✅ تم تصدير الحساب بنجاح إلى: {output_path}")
            return output_path

        except Exception as e:
            print(f"❌ خطأ في تصدير الحساب: {str(e)}")
            return False

    def export_all_accounts_to_excel(self, output_path=None):
        """تصدير جميع الحسابات إلى ملف Excel واحد"""
        try:
            print(f"📤 تصدير جميع الحسابات...")

            # إنشاء ملف جديد
            from openpyxl import Workbook
            new_workbook = Workbook()

            # حذف الورقة الافتراضية
            new_workbook.remove(new_workbook.active)

            # جلب جميع الحسابات
            accounts = self.get_all_accounts()

            if not accounts:
                print("❌ لا توجد حسابات للتصدير")
                return False

            # إضافة ورقة ملخص الحسابات
            summary_sheet = new_workbook.create_sheet(title="ملخص الحسابات")

            # عناوين الملخص
            summary_sheet['A1'] = "رقم الحساب"
            summary_sheet['B1'] = "اسم الحساب"
            summary_sheet['C1'] = "الرصيد"
            summary_sheet['D1'] = "عدد المستندات"

            # تنسيق عناوين الملخص
            from openpyxl.styles import Font, PatternFill, Alignment
            header_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            for col in ['A1', 'B1', 'C1', 'D1']:
                summary_sheet[col].font = header_font
                summary_sheet[col].fill = header_fill
                summary_sheet[col].alignment = Alignment(horizontal='center')

            # إضافة بيانات الحسابات إلى الملخص
            for i, account in enumerate(accounts, 2):
                summary_sheet[f'A{i}'] = account['account_num']
                summary_sheet[f'B{i}'] = account['account_name']
                summary_sheet[f'C{i}'] = account['balance']

                # حساب عدد المستندات
                documents = self.get_account_documents(account['sheet_name'])
                summary_sheet[f'D{i}'] = len(documents)

            # نسخ جميع الحسابات
            for account in accounts:
                sheet_name = account['sheet_name']
                print(f"  📄 نسخ الحساب: {sheet_name}")

                try:
                    source_sheet = self.workbook[sheet_name]
                    target_sheet = new_workbook.create_sheet(title=sheet_name[:31])  # حد أقصى 31 حرف

                    # نسخ البيانات
                    for row in source_sheet.iter_rows():
                        for cell in row:
                            target_cell = target_sheet.cell(row=cell.row, column=cell.column)
                            target_cell.value = cell.value

                            # نسخ التنسيق الأساسي
                            if cell.has_style:
                                target_cell.font = cell.font
                                target_cell.border = cell.border
                                target_cell.fill = cell.fill
                                target_cell.alignment = cell.alignment

                except Exception as e:
                    print(f"  ⚠️ خطأ في نسخ الحساب {sheet_name}: {str(e)}")
                    continue

            # تحديد مسار الحفظ
            if not output_path:
                from tkinter import filedialog
                from datetime import datetime
                default_name = f"جميع_الحسابات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

                output_path = filedialog.asksaveasfilename(
                    title="حفظ تصدير جميع الحسابات",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    initialname=default_name
                )

                if not output_path:
                    print("❌ تم إلغاء عملية التصدير")
                    return False

            # حفظ الملف
            new_workbook.save(output_path)

            print(f"✅ تم تصدير {len(accounts)} حساب بنجاح إلى: {output_path}")
            return output_path

        except Exception as e:
            print(f"❌ خطأ في تصدير جميع الحسابات: {str(e)}")
            return False

    def print_account(self, sheet_name):
        """طباعة حساب محدد"""
        try:
            print(f"🖨️ طباعة الحساب: {sheet_name}")

            # تصدير إلى ملف مؤقت
            import tempfile
            temp_file = tempfile.mktemp(suffix=".xlsx")

            if self.export_account_to_excel(sheet_name, temp_file):
                # فتح الملف للطباعة
                import os
                os.startfile(temp_file)

                print(f"✅ تم فتح الملف للطباعة")
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ خطأ في طباعة الحساب: {str(e)}")
            return False

    def get_account_summary_data(self, sheet_name):
        """جلب بيانات ملخص الحساب"""
        try:
            if sheet_name not in self.workbook.sheetnames:
                return None

            # جلب بيانات الحساب الأساسية
            accounts = self.get_all_accounts()
            account_info = None
            for account in accounts:
                if account['sheet_name'] == sheet_name:
                    account_info = account
                    break

            if not account_info:
                return None

            # جلب المستندات
            documents = self.get_account_documents(sheet_name)

            # حساب الإحصائيات
            total_amount = sum(doc['amount'] for doc in documents)
            positive_amount = sum(doc['amount'] for doc in documents if doc['amount'] > 0)
            negative_amount = sum(doc['amount'] for doc in documents if doc['amount'] < 0)

            return {
                'account_num': account_info['account_num'],
                'account_name': account_info['account_name'],
                'balance': account_info['balance'],
                'total_documents': len(documents),
                'total_amount': total_amount,
                'positive_amount': positive_amount,
                'negative_amount': negative_amount,
                'documents': documents
            }

        except Exception as e:
            print(f"❌ خطأ في جلب بيانات ملخص الحساب: {str(e)}")
            return None