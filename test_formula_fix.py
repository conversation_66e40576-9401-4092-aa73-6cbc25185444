#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الصيغ
"""

import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_formula_fix():
    """اختبار إصلاح مشكلة الصيغ"""
    print("🧪 اختبار إصلاح مشكلة الصيغ")
    print("=" * 40)
    
    try:
        # حذف الملف إذا كان موجوداً
        if os.path.exists("accounting_system.xlsx"):
            os.remove("accounting_system.xlsx")
            print("🗑️ تم حذف الملف السابق")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        
        # إنشاء ExcelManager جديد
        print("📝 إنشاء ExcelManager جديد...")
        excel = ExcelManager()
        
        if excel.workbook:
            print("✅ تم إنشاء الملف بنجاح")
            print(f"📋 الصفحات: {excel.workbook.sheetnames}")
            
            # إنشاء حساب اختبار
            print("💼 إنشاء حساب اختبار...")
            result = excel.create_account_sheet("001", "حساب تجريبي", 1000.0)
            print(f"✅ إنشاء الحساب: {'نجح' if result else 'فشل'}")
            
            if result:
                # إضافة مستندات
                print("📄 إضافة مستندات...")
                for i in range(3):
                    doc_result = excel.add_document("001-حساب تجريبي", 100.0 * (i+1), f"DOC{i+1:03d}", f"PAY{i+1:03d}")
                    print(f"   مستند {i+1}: {'نجح' if doc_result else 'فشل'}")
                
                # حفظ الملف
                print("💾 حفظ الملف...")
                save_result = excel.save_workbook()
                print(f"✅ حفظ الملف: {'نجح' if save_result else 'فشل'}")
                
                # اختبار قراءة الصيغ
                print("🔍 اختبار قراءة الصيغ...")
                ws = excel.workbook["001-حساب تجريبي"]
                
                # فحص خلية المجموع
                sum_cell = ws['A33']
                print(f"   قيمة خلية المجموع A33: {sum_cell.value}")
                print(f"   نوع القيمة: {type(sum_cell.value)}")
                
                # اختبار الدالة الآمنة
                from manage_accounts import ManageAccountsWindow
                
                # إنشاء كائن وهمي للاختبار
                class TestObject:
                    def __init__(self):
                        pass
                    
                    def _safe_get_numeric_value(self, cell):
                        """استخراج قيمة رقمية من خلية مع معالجة الصيغ"""
                        try:
                            value = cell.value
                            if value is None:
                                return 0
                            
                            # إذا كانت القيمة رقمية بالفعل
                            if isinstance(value, (int, float)):
                                return float(value)
                            
                            # إذا كانت نص (قد تكون صيغة)
                            if isinstance(value, str):
                                # إذا كانت صيغة Excel
                                if value.startswith('='):
                                    # محاولة تقييم الصيغة بطريقة بسيطة
                                    if 'SUM(' in value.upper():
                                        # استخراج نطاق الخلايا من الصيغة
                                        import re
                                        match = re.search(r'SUM\(([A-Z]+\d+):([A-Z]+\d+)\)', value.upper())
                                        if match:
                                            start_cell = match.group(1)
                                            end_cell = match.group(2)
                                            return self._calculate_sum_range(cell.parent, start_cell, end_cell)
                                    return 0  # صيغة غير مدعومة
                                else:
                                    # محاولة تحويل النص إلى رقم
                                    try:
                                        return float(value)
                                    except ValueError:
                                        return 0
                            
                            return 0
                            
                        except Exception as e:
                            print(f"⚠️ خطأ في قراءة قيمة الخلية: {str(e)}")
                            return 0
                    
                    def _calculate_sum_range(self, worksheet, start_cell, end_cell):
                        """حساب مجموع نطاق من الخلايا"""
                        try:
                            # تحليل مرجع الخلايا
                            from openpyxl.utils import range_boundaries
                            min_col, min_row, max_col, max_row = range_boundaries(f"{start_cell}:{end_cell}")
                            
                            total = 0
                            for row in range(min_row, max_row + 1):
                                for col in range(min_col, max_col + 1):
                                    cell_value = worksheet.cell(row=row, column=col).value
                                    if isinstance(cell_value, (int, float)):
                                        total += cell_value
                            
                            return total
                            
                        except Exception as e:
                            print(f"⚠️ خطأ في حساب المجموع: {str(e)}")
                            return 0
                
                test_obj = TestObject()
                safe_value = test_obj._safe_get_numeric_value(sum_cell)
                print(f"   القيمة الآمنة: {safe_value}")
                
                print("✅ تم اختبار إصلاح الصيغ بنجاح!")
                return True
            
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_formula_fix()
    input("\nاضغط Enter للإغلاق...")
