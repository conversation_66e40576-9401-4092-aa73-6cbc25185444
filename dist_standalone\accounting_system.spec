# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['launcher.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        ('app.py', '.'),
        ('excel_manager.py', '.'),
        ('document_window.py', '.'),
        ('search_window.py', '.'),
        ('manage_accounts.py', '.'),
        ('requirements.txt', '.'),
        ('*.md', '.'),
    ],
    hiddenimports=[
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.worksheet.worksheet',
        'openpyxl.styles',
        'openpyxl.styles.fonts',
        'openpyxl.styles.borders',
        'openpyxl.styles.fills',
        'openpyxl.styles.alignment',
        'openpyxl.utils',
        'openpyxl.utils.cell',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'datetime',
        'os',
        'sys',
        'threading',
        'locale',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_المستندات_المحاسبية',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_info={
        'version': '1.0.0',
        'description': 'نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية',
        'company': 'وزارة الصحة - المملكة الأردنية الهاشمية',
        'product': 'نظام إدارة المستندات المحاسبية',
        'copyright': '2024 وزارة الصحة الأردنية',
    }
)
