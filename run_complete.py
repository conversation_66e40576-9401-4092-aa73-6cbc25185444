#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت شامل لتشغيل التطبيق مع معالجة مباشرة للأخطاء وتسجيل مفصل
"""

import sys
import traceback
import threading
import time
import json
import os
import logging
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

# إعداد التسجيل
def setup_logging():
    """إعداد نظام تسجيل شامل"""
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    log_filename = f"logs/complete_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    # إعداد التسجيل مع مستويات متعددة
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s() - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return log_filename, logging.getLogger(__name__)

class ComprehensiveErrorHandler:
    """معالج شامل للأخطاء مع تسجيل وحلول"""
    
    def __init__(self, logger):
        self.logger = logger
        self.error_count = 0
        self.warning_count = 0
        self.error_history = []
        self.start_time = datetime.now()
        
    def log_error(self, error_type, message, details=None, suggest_fix=True):
        """تسجيل خطأ مع معالجة شاملة"""
        self.error_count += 1
        timestamp = datetime.now()
        
        error_info = {
            'id': self.error_count,
            'timestamp': timestamp,
            'type': error_type,
            'message': message,
            'details': details
        }
        
        self.error_history.append(error_info)
        
        # تسجيل في الملف
        self.logger.error(f"خطأ #{self.error_count}: {error_type} - {message}")
        if details:
            self.logger.error(f"التفاصيل: {details}")
        
        # عرض فوري
        print(f"\n{'🚨' * 20}")
        print(f"🚨 خطأ #{self.error_count} - {timestamp.strftime('%H:%M:%S')}")
        print(f"📝 النوع: {error_type}")
        print(f"💬 الرسالة: {message}")
        
        if details:
            print(f"📋 التفاصيل:")
            print(details)
        
        if suggest_fix:
            self.suggest_solution(error_type, message)
        
        print(f"{'🚨' * 20}\n")
        
    def log_warning(self, message, details=None):
        """تسجيل تحذير"""
        self.warning_count += 1
        self.logger.warning(f"تحذير #{self.warning_count}: {message}")
        
        print(f"⚠️ تحذير #{self.warning_count}: {message}")
        if details:
            print(f"   التفاصيل: {details}")
    
    def log_info(self, message):
        """تسجيل معلومات"""
        self.logger.info(message)
        print(f"ℹ️ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        self.logger.info(f"نجاح: {message}")
        print(f"✅ {message}")
    
    def suggest_solution(self, error_type, message):
        """اقتراح حلول للأخطاء"""
        solutions = {
            'ImportError': [
                "🔧 تحقق من تثبيت المكتبات المطلوبة: pip install openpyxl tkinter",
                "📁 تأكد من وجود جميع ملفات المشروع في نفس المجلد",
                "🔄 جرب إعادة تشغيل الكمبيوتر"
            ],
            'FileNotFoundError': [
                "📁 تأكد من وجود ملف accounting_system.xlsx",
                "📂 تحقق من المسار الصحيح للملفات",
                "🔍 ابحث عن الملف في مجلدات أخرى"
            ],
            'PermissionError': [
                "🔐 أغلق ملف Excel إذا كان مفتوحاً",
                "👤 شغل التطبيق كمدير (Run as Administrator)",
                "📁 تحقق من صلاحيات المجلد"
            ],
            'AttributeError': [
                "🔄 أعد تشغيل التطبيق",
                "📋 تحقق من تحديث ملفات المشروع",
                "🔍 تأكد من سلامة ملفات الكود"
            ],
            'KeyError': [
                "📊 تحقق من بنية ملف Excel",
                "🔧 أعد إنشاء ملف البيانات",
                "📋 تأكد من وجود الأوراق المطلوبة"
            ],
            'ValueError': [
                "🔢 تحقق من صحة البيانات المدخلة",
                "📊 تأكد من تنسيق الأرقام",
                "🔍 ابحث عن قيم فارغة أو غير صحيحة"
            ]
        }
        
        print("🔧 اقتراحات للحل:")
        
        # حلول خاصة بنوع الخطأ
        if error_type in solutions:
            for solution in solutions[error_type]:
                print(f"   {solution}")
        
        # حلول عامة
        print("   🔄 حلول عامة:")
        print("   📋 تحقق من ملفات المشروع")
        print("   💻 أعد تشغيل الكمبيوتر")
        print("   📞 اتصل بالدعم الفني إذا استمر الخطأ")
        
        # حلول خاصة بالرسالة
        if 'account' in message.lower():
            print("   💼 حلول خاصة بالحسابات:")
            print("   📊 تحقق من وجود الحساب في ملف Excel")
            print("   🔍 تأكد من صحة اسم الحساب")
        
        if 'excel' in message.lower():
            print("   📊 حلول خاصة بـ Excel:")
            print("   📁 تأكد من وجود ملف accounting_system.xlsx")
            print("   🔒 أغلق ملف Excel إذا كان مفتوحاً")
    
    def get_summary(self):
        """الحصول على ملخص الأخطاء"""
        duration = datetime.now() - self.start_time
        return {
            'duration': duration,
            'errors': self.error_count,
            'warnings': self.warning_count,
            'last_errors': self.error_history[-5:] if self.error_history else []
        }

class ApplicationManager:
    """مدير التطبيق مع معالجة شاملة"""
    
    def __init__(self, error_handler):
        self.error_handler = error_handler
        self.app = None
        self.root = None
        self.monitoring = True
        self.login_file = "saved_login.json"
    
    def load_saved_login(self):
        """تحميل معلومات تسجيل الدخول المحفوظة"""
        try:
            if os.path.exists(self.login_file):
                with open(self.login_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if data.get("auto_login", False):
                    self.error_handler.log_success(f"تم العثور على معلومات تسجيل دخول محفوظة: {data['username']}")
                    return data
            
            self.error_handler.log_info("لا توجد معلومات تسجيل دخول محفوظة")
            return None
            
        except Exception as e:
            self.error_handler.log_error("LoginLoadError", str(e), traceback.format_exc())
            return None
    
    def save_login_info(self, username):
        """حفظ معلومات تسجيل الدخول"""
        try:
            data = {
                "username": username,
                "last_login": datetime.now().isoformat(),
                "auto_login": True
            }
            
            with open(self.login_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.error_handler.log_success(f"تم حفظ معلومات تسجيل الدخول: {username}")
            return True
            
        except Exception as e:
            self.error_handler.log_error("LoginSaveError", str(e))
            return False
    
    def test_imports(self):
        """اختبار استيراد جميع الوحدات المطلوبة"""
        self.error_handler.log_info("بدء اختبار استيراد الوحدات...")
        
        modules = [
            ('tkinter', 'tk'),
            ('app', 'AccountingApp'),
            ('excel_manager', 'ExcelManager'),
            ('manage_accounts', 'ManageAccountsDialog'),
            ('user_manager', 'UserManager'),
            ('document_window', 'AddDocumentWindow'),
            ('search_window', 'SearchWindow')
        ]
        
        failed_imports = []
        
        for module_name, class_name in modules:
            try:
                if module_name == 'tkinter':
                    import tkinter as tk
                    self.error_handler.log_success(f"{module_name} - OK")
                else:
                    module = __import__(module_name)
                    if hasattr(module, class_name):
                        self.error_handler.log_success(f"{module_name}.{class_name} - OK")
                    else:
                        self.error_handler.log_warning(f"{class_name} غير موجود في {module_name}")
                        
            except Exception as e:
                failed_imports.append((module_name, str(e)))
                self.error_handler.log_error("ImportError", f"فشل استيراد {module_name}: {str(e)}")
        
        if failed_imports:
            self.error_handler.log_error("CriticalImportError", 
                                       f"فشل في استيراد {len(failed_imports)} وحدة")
            return False
        
        self.error_handler.log_success("تم اختبار جميع الوحدات بنجاح")
        return True
    
    def create_app_with_error_handling(self):
        """إنشاء التطبيق مع معالجة شاملة للأخطاء"""
        try:
            self.error_handler.log_info("إنشاء النافذة الرئيسية...")
            self.root = tk.Tk()
            
            # إعداد معالج الأخطاء العام
            def global_error_handler(exc_type, exc_value, exc_traceback):
                if issubclass(exc_type, KeyboardInterrupt):
                    self.error_handler.log_info("تم إيقاف التطبيق بواسطة المستخدم (Ctrl+C)")
                    return
                
                self.error_handler.log_error(
                    exc_type.__name__,
                    str(exc_value),
                    "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
                )
            
            sys.excepthook = global_error_handler
            
            # استيراد التطبيق
            from app import AccountingApp
            
            # تحميل معلومات تسجيل الدخول
            saved_login = self.load_saved_login()
            
            self.error_handler.log_info("إنشاء كائن التطبيق...")
            self.app = AccountingApp(self.root)
            
            if saved_login:
                # تسجيل دخول تلقائي
                self.error_handler.log_info(f"تسجيل دخول تلقائي: {saved_login['username']}")
                
                # تعيين المستخدم
                if hasattr(self.app, 'user_manager'):
                    self.app.user_manager.current_user = {
                        'username': saved_login['username'],
                        'role': 'admin',
                        'permissions': ['add_account', 'delete_account', 'add_document', 'view_reports', 'manage_users']
                    }
                
                # إغلاق نافذة تسجيل الدخول
                if hasattr(self.app, 'login_window') and self.app.login_window:
                    try:
                        if hasattr(self.app.login_window, 'root'):
                            self.app.login_window.root.destroy()
                        self.error_handler.log_success("تم إغلاق نافذة تسجيل الدخول")
                    except Exception as e:
                        self.error_handler.log_warning(f"خطأ في إغلاق نافذة تسجيل الدخول: {e}")
                
                # إظهار النافذة الرئيسية
                self.app.root.deiconify()
                
                # إعداد الواجهة
                if hasattr(self.app, 'setup_modern_ui'):
                    self.error_handler.log_info("إعداد الواجهة الحديثة...")
                    self.app.setup_modern_ui()
                
                # بدء التحديث التلقائي
                if hasattr(self.app, 'start_auto_refresh'):
                    try:
                        self.app.start_auto_refresh()
                        self.error_handler.log_success("تم بدء التحديث التلقائي")
                    except Exception as e:
                        self.error_handler.log_warning(f"خطأ في بدء التحديث التلقائي: {e}")
                
                # تحديث عنوان النافذة
                self.app.root.title(f"نظام إدارة المستندات المحاسبية - {saved_login['username']} (تسجيل دخول تلقائي)")
                
            else:
                # تسجيل دخول عادي مع حفظ المعلومات
                self.error_handler.log_info("عرض نافذة تسجيل الدخول العادية")
                
                # تحسين دالة تسجيل الدخول
                original_on_login_success = self.app.on_login_success
                
                def enhanced_login_success():
                    try:
                        # حفظ معلومات تسجيل الدخول
                        if hasattr(self.app, 'user_manager') and self.app.user_manager.current_user:
                            username = self.app.user_manager.current_user.get('username', 'admin')
                            self.save_login_info(username)
                        
                        # استدعاء الدالة الأصلية
                        original_on_login_success()
                        
                    except Exception as e:
                        self.error_handler.log_error("LoginSuccessError", str(e), traceback.format_exc())
                
                self.app.on_login_success = enhanced_login_success
            
            self.error_handler.log_success("تم إنشاء التطبيق بنجاح")
            return True
            
        except Exception as e:
            self.error_handler.log_error("AppCreationError", str(e), traceback.format_exc())
            return False
    
    def start_monitoring(self):
        """بدء مراقبة التطبيق"""
        def monitor():
            while self.monitoring and self.app and self.root:
                try:
                    if not self.root.winfo_exists():
                        self.error_handler.log_info("تم إغلاق التطبيق بواسطة المستخدم")
                        self.monitoring = False
                        break
                    
                    time.sleep(2)  # فحص كل ثانيتين
                    
                except Exception as e:
                    self.error_handler.log_warning(f"خطأ في المراقبة: {e}")
                    time.sleep(5)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        self.error_handler.log_success("تم بدء مراقبة التطبيق")
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            if self.create_app_with_error_handling():
                self.start_monitoring()
                
                self.error_handler.log_success("بدء تشغيل حلقة الأحداث الرئيسية")
                self.error_handler.log_info("التطبيق يعمل الآن - يمكنك استخدامه")
                
                self.root.mainloop()
            else:
                self.error_handler.log_error("StartupError", "فشل في إنشاء التطبيق")
                
        except KeyboardInterrupt:
            self.error_handler.log_info("تم إيقاف التطبيق بواسطة المستخدم")
            
        except Exception as e:
            self.error_handler.log_error("RuntimeError", str(e), traceback.format_exc())
        
        finally:
            self.monitoring = False

def main():
    """الدالة الرئيسية الشاملة"""
    
    print("=" * 100)
    print("🚀 نظام إدارة المستندات المحاسبية - تشغيل شامل مع معالجة متقدمة للأخطاء")
    print("=" * 100)
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🛡️ معالجة شاملة ومباشرة للأخطاء مفعلة")
    print("📝 تسجيل مفصل في ملفات السجل")
    print("🔍 مراقبة مستمرة لحالة التطبيق")
    print("💾 حفظ تلقائي لمعلومات تسجيل الدخول")
    print("=" * 100)
    
    # إعداد التسجيل
    log_file, logger = setup_logging()
    print(f"📁 ملف السجل: {log_file}")
    
    # إنشاء معالج الأخطاء
    error_handler = ComprehensiveErrorHandler(logger)
    error_handler.log_success("تم بدء النظام")
    
    # إنشاء مدير التطبيق
    app_manager = ApplicationManager(error_handler)
    
    try:
        # اختبار الاستيرادات
        if not app_manager.test_imports():
            error_handler.log_error("CriticalError", "فشل في اختبار الوحدات الأساسية")
            return
        
        # تشغيل التطبيق
        app_manager.run()
        
    except Exception as e:
        error_handler.log_error("FatalError", str(e), traceback.format_exc())
    
    finally:
        # عرض ملخص الجلسة
        summary = error_handler.get_summary()
        
        print(f"\n{'=' * 100}")
        print("📊 ملخص جلسة التشغيل:")
        print(f"⏱️ مدة التشغيل: {summary['duration']}")
        print(f"🚨 عدد الأخطاء: {summary['errors']}")
        print(f"⚠️ عدد التحذيرات: {summary['warnings']}")
        
        if summary['last_errors']:
            print("\n📋 آخر الأخطاء:")
            for error in summary['last_errors']:
                print(f"   [{error['timestamp'].strftime('%H:%M:%S')}] {error['type']}: {error['message']}")
        
        print(f"\n📁 سجل مفصل محفوظ في: {log_file}")
        print(f"🔚 انتهاء التشغيل - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 100)

if __name__ == "__main__":
    main()
