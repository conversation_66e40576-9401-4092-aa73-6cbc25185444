#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تقارير الأرصدة المحدثة مع الزر الثاني
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_updated_window():
    """اختبار النافذة المحدثة مع الزر الثاني"""
    try:
        print("🧪 اختبار نافذة تقارير الأرصدة المحدثة...")
        
        # إنشاء نافذة رئيسية مؤقتة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # محاكاة كائن parent
        class MockParent:
            def __init__(self):
                self.root = root
        
        parent = MockParent()
        
        # استيراد وإنشاء النافذة
        from account_balances_window import AccountBalancesWindow
        window = AccountBalancesWindow(parent)
        
        print("✅ تم إنشاء النافذة المحدثة بنجاح")
        print("📋 الأزرار المتاحة:")
        print("   1. 💰 تقرير حسابات المقبوضات (محسن)")
        print("   2. 📦 تقرير حسابات المواد (جديد)")
        print("   3. ❓ مساعدة (محدثة)")
        print("   4. ❌ إغلاق")
        
        # عرض النافذة لفترة قصيرة
        root.after(5000, root.quit)  # إغلاق بعد 5 ثوانٍ
        root.deiconify()  # إظهار النافذة للاختبار
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_functions_exist():
    """اختبار وجود الدوال الجديدة"""
    try:
        print("\n🔍 فحص وجود الدوال الجديدة...")
        
        # قراءة ملف النافذة
        with open('account_balances_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # قائمة الدوال المطلوبة
        required_functions = [
            "def create_materials_accounts_report(self):",
            "def setup_enhanced_materials_report(self, ws, workbook):",
            "def calculate_account_balance_materials(self, ws):",
            "def count_account_documents_materials(self, ws):"
        ]
        
        missing_functions = []
        for func in required_functions:
            if func in content:
                print(f"✅ تم العثور على: {func}")
            else:
                print(f"❌ مفقود: {func}")
                missing_functions.append(func)
        
        # فحص تحديث النصوص
        if "📦 تقرير حسابات المواد" in content:
            print("✅ تم تحديث نص الزر الثاني")
        else:
            print("❌ لم يتم تحديث نص الزر الثاني")
            missing_functions.append("نص الزر الثاني")
        
        if "تقرير حسابات المواد (جديد)" in content:
            print("✅ تم تحديث نافذة المساعدة")
        else:
            print("❌ لم يتم تحديث نافذة المساعدة")
            missing_functions.append("نافذة المساعدة")
        
        if len(missing_functions) == 0:
            print("✅ جميع الدوال والتحديثات موجودة")
            return True
        else:
            print(f"❌ مفقود: {len(missing_functions)} عنصر")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص الدوال: {str(e)}")
        return False

def test_button_configuration():
    """اختبار تكوين الأزرار"""
    try:
        print("\n🔧 فحص تكوين الأزرار...")
        
        # قراءة ملف النافذة
        with open('account_balances_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص تكوين الزر الأول
        if "command=self.create_receipts_accounts_report" in content:
            print("✅ الزر الأول مرتبط بالدالة الصحيحة")
        else:
            print("❌ الزر الأول غير مرتبط بالدالة الصحيحة")
            return False
        
        # فحص تكوين الزر الثاني
        if "command=self.create_materials_accounts_report" in content:
            print("✅ الزر الثاني مرتبط بالدالة الصحيحة")
        else:
            print("❌ الزر الثاني غير مرتبط بالدالة الصحيحة")
            return False
        
        # فحص معلومات الأزرار
        if "accounting_system.xlsx" in content and "Accounting system deductions.xlsx" in content:
            print("✅ معلومات الملفات صحيحة")
        else:
            print("❌ معلومات الملفات غير صحيحة")
            return False
        
        print("✅ جميع تكوينات الأزرار صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص التكوين: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نافذة تقارير الأرصدة المحدثة")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # اختبار وجود الدوال
    if test_functions_exist():
        success_count += 1
    
    # اختبار تكوين الأزرار
    if test_button_configuration():
        success_count += 1
    
    # اختبار النافذة
    if test_updated_window():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات النافذة المحدثة!")
        print("\n📝 التحديثات المطبقة:")
        print("✅ تحديث الزر الثاني إلى 'تقرير حسابات المواد'")
        print("✅ إضافة دالة إنشاء تقرير المواد المحسنة")
        print("✅ إضافة دوال حساب الأرصدة وعد المستندات للمواد")
        print("✅ تحديث نافذة المساعدة")
        print("✅ تحديث معلومات الأزرار")
        
        print("\n🎯 الوظائف الجديدة:")
        print("💰 الزر الأول: تقرير حسابات المقبوضات")
        print("   📁 الملف: Accounting system deductions.xlsx")
        print("   🎨 اللون: أزرق")
        
        print("📦 الزر الثاني: تقرير حسابات المواد")
        print("   📁 الملف: accounting_system.xlsx")
        print("   🎨 اللون: أخضر")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات النافذة المحدثة")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
