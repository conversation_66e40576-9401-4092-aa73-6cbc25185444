#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة مطابقة المستندات - النافذة الرئيسية
نظام مطابقة مستندات الصرف والمقبوضات
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os

class DocumentMatchingMainWindow(tk.Toplevel):
    """النافذة الرئيسية لاختيار نوع المطابقة"""
    
    def __init__(self, parent=None):
        if parent:
            super().__init__(parent)
            self.transient(parent)
            self.grab_set()
        else:
            # إذا تم تشغيل النافذة بشكل مستقل
            super().__init__()
        
        self.title("🧮 نظام مطابقة المستندات")
        self.geometry("700x500")
        self.configure(bg='#f8f9fa')
        self.resizable(False, False)
        
        # تعيين أيقونة النافذة (إذا كانت متوفرة)
        try:
            self.iconbitmap('icon.ico')
        except:
            pass
        
        self.create_widgets()
        self.center_window()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان الرئيسي
        title_label = ttk.Label(main_frame, text="🧮 نظام مطابقة المستندات", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="اختر نوع المطابقة المطلوبة", 
                                  font=('Arial', 12))
        subtitle_label.pack(pady=(0, 30))
        
        # إطار مطابقة الحسابات
        accounts_frame = ttk.LabelFrame(main_frame, text="📊 مطابقة مستندات الحسابات", 
                                       padding="25")
        accounts_frame.pack(fill=tk.X, pady=(0, 20))
        
        # وصف مطابقة الحسابات
        accounts_desc = ttk.Label(accounts_frame, 
                                 text="مطابقة مستندات الصرف من نظام الحسابات\n"
                                      "حسب نطاق أرقام المستندات والمبالغ\n"
                                      "البحث عن أفضل مجموعة مبالغ للمطابقة", 
                                 font=('Arial', 10), justify=tk.CENTER)
        accounts_desc.pack(pady=(0, 15))
        
        # زر مطابقة الحسابات
        accounts_btn = ttk.Button(accounts_frame, text="🔍 مطابقة الحسابات", 
                                 command=self.open_accounts_matching,
                                 style='Accent.TButton')
        accounts_btn.pack(pady=5)
        
        # إطار مطابقة المقبوضات
        receipts_frame = ttk.LabelFrame(main_frame, text="💰 مطابقة مستندات المقبوضات", 
                                       padding="25")
        receipts_frame.pack(fill=tk.X, pady=(0, 30))
        
        # وصف مطابقة المقبوضات
        receipts_desc = ttk.Label(receipts_frame, 
                                 text="مطابقة مستندات المقبوضات من نظام المقبوضات\n"
                                      "حسب نطاق أرقام المستندات والمبالغ\n"
                                      "البحث عن أفضل مجموعة مبالغ للمطابقة", 
                                 font=('Arial', 10), justify=tk.CENTER)
        receipts_desc.pack(pady=(0, 15))
        
        # زر مطابقة المقبوضات
        receipts_btn = ttk.Button(receipts_frame, text="💵 مطابقة المقبوضات", 
                                 command=self.open_receipts_matching,
                                 style='Accent.TButton')
        receipts_btn.pack(pady=5)
        
        # إطار الأزرار السفلية
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(20, 0))
        
        # زر المساعدة
        help_btn = ttk.Button(bottom_frame, text="❓ مساعدة", 
                             command=self.show_help)
        help_btn.pack(side=tk.LEFT)
        
        # زر الإغلاق
        close_btn = ttk.Button(bottom_frame, text="❌ إغلاق", 
                              command=self.destroy)
        close_btn.pack(side=tk.RIGHT)
    
    def open_accounts_matching(self):
        """فتح نافذة مطابقة الحسابات"""
        try:
            from document_matching_accounts import AccountsMatchingWindow
            
            # التحقق من وجود ملف الحسابات
            accounts_file = "accounting_system.xlsx"
            if not os.path.exists(accounts_file):
                messagebox.showerror("خطأ", 
                                   f"ملف نظام الحسابات غير موجود:\n{accounts_file}\n\n"
                                   "تأكد من وجود الملف في نفس مجلد البرنامج")
                return
            
            # فتح نافذة مطابقة الحسابات
            AccountsMatchingWindow(self, accounts_file)
            
        except ImportError as e:
            messagebox.showerror("خطأ", 
                               f"فشل في تحميل نافذة مطابقة الحسابات:\n{str(e)}\n\n"
                               "تأكد من وجود ملف document_matching_accounts.py")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة مطابقة الحسابات:\n{str(e)}")
    
    def open_receipts_matching(self):
        """فتح نافذة مطابقة المقبوضات"""
        try:
            from document_matching_receipts import ReceiptsMatchingWindow
            
            # التحقق من وجود ملف المقبوضات
            receipts_file = "receipts_system.xlsx"
            if not os.path.exists(receipts_file):
                messagebox.showerror("خطأ", 
                                   f"ملف نظام المقبوضات غير موجود:\n{receipts_file}\n\n"
                                   "تأكد من وجود الملف في نفس مجلد البرنامج")
                return
            
            # فتح نافذة مطابقة المقبوضات
            ReceiptsMatchingWindow(self, receipts_file)
            
        except ImportError as e:
            messagebox.showerror("خطأ", 
                               f"فشل في تحميل نافذة مطابقة المقبوضات:\n{str(e)}\n\n"
                               "تأكد من وجود ملف document_matching_receipts.py")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة مطابقة المقبوضات:\n{str(e)}")
    
    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = """
🧮 نظام مطابقة المستندات

📋 الوظائف الرئيسية:
• مطابقة مستندات الحسابات حسب المبالغ
• مطابقة مستندات المقبوضات حسب المبالغ
• البحث عن أفضل مجموعة مطابقة تلقائياً
• عرض أرقام المستندات المطابقة للبحث السهل

🔍 كيفية الاستخدام:
1. اختر نوع المطابقة (حسابات أو مقبوضات)
2. أدخل نطاق أرقام المستندات (من - إلى)
3. أدخل المبلغ المطلوب مطابقته
4. اضغط "تحميل المستندات وحساب المجموع"
5. استخدم "البحث عن مطابقة تلقائية" للحصول على أفضل نتيجة

💡 نصائح:
• يمكن تحديد/إلغاء تحديد المستندات يدوياً
• استخدم "نسخ أرقام المستندات" للبحث السريع
• احفظ النتائج في ملف Excel للمراجعة اللاحقة

📞 للدعم الفني: اتصل بقسم تقنية المعلومات
        """
        
        messagebox.showinfo("مساعدة - نظام مطابقة المستندات", help_text)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')


def main():
    """تشغيل النافذة بشكل مستقل"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # تطبيق ستايل حديث
    style = ttk.Style()
    try:
        style.theme_use('clam')
    except:
        pass
    
    # إنشاء النافذة
    app = DocumentMatchingMainWindow()
    
    # تشغيل التطبيق
    app.mainloop()


if __name__ == "__main__":
    main()
