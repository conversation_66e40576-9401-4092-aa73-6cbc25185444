#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إعادة تنظيم واجهة التطبيق
"""

import sys
import os

def test_removed_duplicate_button():
    """اختبار حذف الزر المكرر"""
    try:
        print("🔍 فحص حذف الزر المكرر...")
        
        # قراءة ملف app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الزر المحذوف
        duplicate_button = "'text': '📊 تقرير أرصدة الحسابات'"
        if duplicate_button in content:
            print("❌ الزر المكرر ما زال موجود")
            return False
        else:
            print("✅ تم حذف الزر المكرر بنجاح")
        
        # البحث عن الدالة المحذوفة
        removed_function = "def create_summary_report(self):"
        if removed_function in content:
            print("❌ الدالة المحذوفة ما زالت موجودة")
            return False
        else:
            print("✅ تم حذف الدالة غير المستخدمة")
        
        # التأكد من وجود الزر الصحيح
        correct_button = "'text': '📊 نافذة تقارير الأرصدة'"
        if correct_button in content:
            print("✅ الزر الصحيح موجود")
        else:
            print("❌ الزر الصحيح مفقود")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأزرار: {str(e)}")
        return False

def test_button_organization():
    """اختبار تنظيم الأزرار في أقسام"""
    try:
        print("\n📋 فحص تنظيم الأزرار...")
        
        # قراءة ملف app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # قائمة الأقسام المطلوبة
        required_sections = [
            "# قسم الحسابات الرئيسية",
            "# قسم المقبوضات", 
            "# قسم البحث والتقارير",
            "# قسم إدارة النظام"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section in content:
                print(f"✅ تم العثور على: {section}")
            else:
                print(f"❌ مفقود: {section}")
                missing_sections.append(section)
        
        # فحص ترتيب الأزرار
        expected_buttons = [
            "📁 إضافة حساب جديد",
            "📝 إضافة مستند", 
            "⚙️ إدارة الحسابات",
            "💰 إضافة حساب مقبوضات",
            "📄 إضافة مستند مقبوضات",
            "⚙️ إدارة حسابات المقبوضات",
            "🔍 بحث في الحسابات",
            "📊 نافذة تقارير الأرصدة",
            "👥 إدارة المستخدمين",
            "🚪 خروج من النظام"
        ]
        
        missing_buttons = []
        for button in expected_buttons:
            if button in content:
                print(f"✅ الزر موجود: {button}")
            else:
                print(f"❌ الزر مفقود: {button}")
                missing_buttons.append(button)
        
        if len(missing_sections) == 0 and len(missing_buttons) == 0:
            print("✅ جميع الأقسام والأزرار منظمة بشكل صحيح")
            return True
        else:
            print(f"❌ مفقود: {len(missing_sections)} قسم و {len(missing_buttons)} زر")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص التنظيم: {str(e)}")
        return False

def test_layout_documentation():
    """اختبار وجود ملف تخطيط النوافذ"""
    try:
        print("\n📄 فحص ملف تخطيط النوافذ...")
        
        layout_file = "تخطيط_نوافذ_التطبيق.txt"
        
        if not os.path.exists(layout_file):
            print("❌ ملف تخطيط النوافذ غير موجود")
            return False
        
        # قراءة الملف وفحص المحتوى
        with open(layout_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # قائمة الأقسام المطلوبة في الملف
        required_sections = [
            "الواجهة الرئيسية",
            "نافذة إضافة حساب جديد",
            "نافذة إضافة مستند",
            "نافذة تقارير الأرصدة",
            "هيكل الملفات والبيانات",
            "قواعد التسمية والتنظيم",
            "إرشادات التطوير والتحديث"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section in content:
                print(f"✅ القسم موجود: {section}")
            else:
                print(f"❌ القسم مفقود: {section}")
                missing_sections.append(section)
        
        # فحص معلومات الإصدار
        if "الإصدار الحالي: 2.1" in content:
            print("✅ معلومات الإصدار محدثة")
        else:
            print("❌ معلومات الإصدار غير محدثة")
            missing_sections.append("معلومات الإصدار")
        
        if len(missing_sections) == 0:
            print("✅ ملف تخطيط النوافذ مكتمل")
            return True
        else:
            print(f"❌ مفقود: {len(missing_sections)} قسم في ملف التخطيط")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص ملف التخطيط: {str(e)}")
        return False

def test_interface_functionality():
    """اختبار وظائف الواجهة الأساسية"""
    try:
        print("\n🔧 اختبار وظائف الواجهة...")
        
        # محاولة استيراد الملف الرئيسي
        sys.path.append('.')
        
        # فحص استيراد الوحدات الأساسية
        try:
            import tkinter as tk
            print("✅ مكتبة tkinter متاحة")
        except ImportError:
            print("❌ مكتبة tkinter غير متاحة")
            return False
        
        # فحص وجود الملفات الأساسية
        essential_files = [
            'app.py',
            'account_balances_window.py',
            'user_manager.py',
            'excel_manager.py'
        ]
        
        missing_files = []
        for file in essential_files:
            if os.path.exists(file):
                print(f"✅ الملف موجود: {file}")
            else:
                print(f"❌ الملف مفقود: {file}")
                missing_files.append(file)
        
        if len(missing_files) == 0:
            print("✅ جميع الملفات الأساسية موجودة")
            return True
        else:
            print(f"❌ مفقود: {len(missing_files)} ملف أساسي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {str(e)}")
        return False

def generate_summary_report():
    """إنشاء تقرير ملخص للتحديثات"""
    try:
        print("\n📊 إنشاء تقرير ملخص...")
        
        summary = """
# ===============================================================================
# تقرير ملخص إعادة تنظيم الواجهة
# ===============================================================================

## التحديثات المطبقة:

### 1. حذف العناصر المكررة:
✅ حذف زر "📊 تقرير أرصدة الحسابات" المكرر
✅ حذف دالة create_summary_report() غير المستخدمة
✅ تنظيف الكود من التكرارات

### 2. إعادة ترتيب الأزرار:
✅ تنظيم الأزرار في 4 أقسام منطقية:
   - قسم الحسابات الرئيسية (3 أزرار)
   - قسم المقبوضات (3 أزرار)  
   - قسم البحث والتقارير (2 زر)
   - قسم إدارة النظام (2 زر)

### 3. تحسين التنظيم:
✅ إضافة تعليقات توضيحية للأقسام
✅ ترتيب منطقي للوظائف المترابطة
✅ ألوان متناسقة لكل قسم

### 4. إنشاء ملف التخطيط:
✅ ملف "تخطيط_نوافذ_التطبيق.txt" شامل
✅ توثيق جميع النوافذ ووظائفها
✅ إرشادات التطوير والصيانة
✅ قواعد التسمية والتنظيم

## الترتيب النهائي للواجهة:

### الصف الأول:
[📁 إضافة حساب جديد] [📝 إضافة مستند] [⚙️ إدارة الحسابات]

### الصف الثاني:  
[💰 إضافة حساب مقبوضات] [📄 إضافة مستند مقبوضات] [⚙️ إدارة حسابات المقبوضات]

### الصف الثالث:
[🔍 بحث في الحسابات] [📊 نافذة تقارير الأرصدة] [👥 إدارة المستخدمين]

### الصف الرابع:
[🚪 خروج من النظام] [فارغ] [فارغ]

## الفوائد المحققة:
- واجهة أكثر تنظيماً ووضوحاً
- سهولة الوصول للوظائف المترابطة
- تقليل التشويش والتكرار
- توثيق شامل للصيانة المستقبلية

## ملفات التوثيق:
- تخطيط_نوافذ_التطبيق.txt: دليل شامل للنوافذ
- test_interface_reorganization.py: اختبار التحديثات

تاريخ التحديث: 2025-06-28
الإصدار: 2.1
المطور: Augment Agent
"""
        
        with open('تقرير_إعادة_تنظيم_الواجهة.txt', 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print("✅ تم إنشاء تقرير الملخص بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار إعادة تنظيم واجهة التطبيق")
    print("=" * 80)
    
    success_count = 0
    total_tests = 4
    
    # اختبار حذف الزر المكرر
    if test_removed_duplicate_button():
        success_count += 1
    
    # اختبار تنظيم الأزرار
    if test_button_organization():
        success_count += 1
    
    # اختبار ملف التخطيط
    if test_layout_documentation():
        success_count += 1
    
    # اختبار وظائف الواجهة
    if test_interface_functionality():
        success_count += 1
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات إعادة التنظيم!")
        
        # إنشاء تقرير الملخص
        generate_summary_report()
        
        print("\n📝 التحديثات المطبقة:")
        print("✅ حذف الزر المكرر 'تقرير أرصدة الحسابات'")
        print("✅ حذف الدالة غير المستخدمة create_summary_report()")
        print("✅ إعادة ترتيب الأزرار في أقسام منطقية")
        print("✅ إضافة تعليقات توضيحية للأقسام")
        print("✅ إنشاء ملف تخطيط النوافذ الشامل")
        
        print("\n📄 الملفات المنشأة:")
        print("- تخطيط_نوافذ_التطبيق.txt")
        print("- تقرير_إعادة_تنظيم_الواجهة.txt")
        print("- test_interface_reorganization.py")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات إعادة التنظيم")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
