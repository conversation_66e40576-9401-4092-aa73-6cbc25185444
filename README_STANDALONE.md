# نظام إدارة المستندات المحاسبية - الإصدار المستقل
## Accounting Documents Management System - Standalone Version

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/moh-jordan/accounting-system)
[![Python](https://img.shields.io/badge/python-3.7+-green.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/license-MOH%20Jordan-red.svg)](LICENSE)

---

## 🎯 نظرة عامة

هذا الإصدار المحسن من نظام إدارة المستندات المحاسبية يتضمن **جميع ملفات التشغيل المطلوبة** ولا يحتاج المستخدم لتنزيل أي برامج تشغيل إضافية عند استخدام التطبيق على جهاز آخر.

### 🌟 المميزات الجديدة:
- ✅ **ملف تنفيذي مستقل** - لا يحتاج Python أو مكتبات إضافية
- ✅ **تثبيت تلقائي للمتطلبات** - سكريبت ذكي لتثبيت المكتبات
- ✅ **فحص شامل للنظام** - أدوات تشخيص متقدمة
- ✅ **دعم كامل للعربية** - واجهة محسنة للنصوص العربية
- ✅ **أدوات بناء متقدمة** - إنشاء ملفات تنفيذية بسهولة

---

## 📦 طرق التشغيل

### 1. الطريقة الأسهل: الملف التنفيذي المستقل

```bash
# تشغيل مباشر - لا يحتاج أي متطلبات
نظام_إدارة_المستندات_المحاسبية.exe
```

**المميزات:**
- 🚀 تشغيل فوري
- 📱 لا يحتاج تثبيت Python
- 🔒 آمن ومستقر
- 💾 حجم صغير (~50 MB)

### 2. التشغيل مع Python (للمطورين)

#### أ. التشغيل السريع:
```bash
# تشغيل مع فحص تلقائي للمتطلبات
تشغيل_النظام_الشامل.bat

# أو تشغيل مباشر
python launcher.py
```

#### ب. التثبيت الكامل:
```bash
# 1. تثبيت المتطلبات تلقائياً
python install_dependencies.py

# 2. تشغيل النظام
python launcher.py
```

#### ج. التثبيت اليدوي:
```bash
# تثبيت من ملف المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python app.py
```

---

## 🔧 أدوات النظام المتقدمة

### 1. فحص شامل للنظام
```bash
python فحص_شامل_للنظام.py
```
**الوظائف:**
- ✅ فحص إصدار Python
- ✅ فحص المكتبات المطلوبة
- ✅ فحص الملفات الأساسية
- ✅ اختبار الاستيرادات
- ✅ فحص موارد النظام
- ✅ فحص الصلاحيات
- 📊 تقرير شامل بصيغة JSON

### 2. مثبت المتطلبات التلقائي
```bash
python install_dependencies.py
```
**المميزات:**
- 🔄 تحديث pip تلقائياً
- 📦 تثبيت المكتبات المطلوبة
- ⚠️ تثبيت المكتبات الاختيارية
- 📝 سجل مفصل للتثبيت
- 🔍 التحقق من نجاح التثبيت

### 3. بناء الملف التنفيذي
```bash
python build_standalone.py
```
**الوظائف:**
- 🏗️ بناء ملف تنفيذي مستقل
- 📁 تضمين جميع الملفات المطلوبة
- 🔧 إعدادات PyInstaller محسنة
- 📄 إنشاء ملفات التوثيق
- ✅ التحقق من نجاح البناء

### 4. إعداد النظام للتوزيع
```bash
python setup.py install
```
**المميزات:**
- 📦 تثبيت كحزمة Python
- 🔗 إنشاء اختصارات سطر الأوامر
- 📚 تضمين جميع الملفات
- 🏷️ معلومات الحزمة الكاملة

---

## 📋 المتطلبات المضمنة

### المكتبات الأساسية:
| المكتبة | الإصدار | الوصف | الحالة |
|---------|---------|--------|--------|
| `openpyxl` | ≥3.1.0 | معالجة ملفات Excel | ✅ مضمنة |
| `tkinter` | مدمجة | واجهة المستخدم | ✅ مدمجة مع Python |

### المكتبات المحسنة:
| المكتبة | الإصدار | الوصف | الحالة |
|---------|---------|--------|--------|
| `ttkthemes` | ≥3.2.2 | تحسين المظهر | ✅ مضمنة |
| `Pillow` | ≥10.0.0 | معالجة الصور | ✅ مضمنة |
| `pyinstaller` | ≥5.13.0 | بناء الملفات التنفيذية | ✅ مضمنة |
| `cryptography` | ≥41.0.0 | التشفير والأمان | ✅ مضمنة |

### المكتبات الإضافية:
| المكتبة | الإصدار | الوصف | الحالة |
|---------|---------|--------|--------|
| `arabic-reshaper` | ≥3.0.0 | دعم النصوص العربية | ✅ مضمنة |
| `python-bidi` | ≥0.4.2 | اتجاه النص العربي | ✅ مضمنة |
| `jsonschema` | ≥4.17.0 | التحقق من JSON | ✅ مضمنة |
| `python-dateutil` | ≥2.8.2 | معالجة التواريخ | ✅ مضمنة |

---

## 🗂️ هيكل المشروع المحسن

```
AccountingSystem/
├── 🚀 ملفات التشغيل الرئيسية
│   ├── نظام_إدارة_المستندات_المحاسبية.exe  # الملف التنفيذي المستقل
│   ├── launcher.py                           # مشغل النظام المحسن
│   ├── app.py                               # التطبيق الرئيسي
│   ├── تشغيل_النظام_الشامل.bat              # ملف تشغيل محسن
│   └── تشغيل_النظام_المحسن.bat              # ملف تشغيل أساسي
│
├── 🔧 أدوات النظام المتقدمة
│   ├── install_dependencies.py              # مثبت المتطلبات التلقائي
│   ├── فحص_شامل_للنظام.py                  # فحص شامل للنظام
│   ├── build_standalone.py                  # بناء الملف التنفيذي
│   └── setup.py                            # إعداد الحزمة
│
├── 📊 ملفات البيانات
│   ├── accounting_system.xlsx               # البيانات الرئيسية
│   ├── Accounting system deductions.xlsx    # بيانات الخصومات
│   └── users.json                          # بيانات المستخدمين
│
├── ⚙️ ملفات الإعداد
│   ├── requirements.txt                     # المتطلبات المحسنة
│   ├── accounting_system.spec               # إعدادات PyInstaller
│   └── version_info.txt                    # معلومات الإصدار
│
├── 📚 ملفات التوثيق
│   ├── README_STANDALONE.md                 # هذا الملف
│   ├── دليل_التثبيت_والتشغيل.md            # دليل شامل
│   └── تقرير_المشروع.md                    # تقرير المشروع
│
└── 📁 مجلدات الإخراج
    ├── dist_standalone/                     # الملف التنفيذي المبني
    ├── build/                              # ملفات البناء المؤقتة
    └── logs/                               # ملفات السجلات
```

---

## 🛠️ دليل الاستخدام السريع

### للمستخدمين العاديين:

1. **تحميل الملف التنفيذي**:
   ```
   نظام_إدارة_المستندات_المحاسبية.exe
   ```

2. **تشغيل النظام**:
   - انقر نقراً مزدوجاً على الملف
   - أو شغله من سطر الأوامر

3. **تسجيل الدخول**:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin`

### للمطورين والمسؤولين:

1. **فحص النظام**:
   ```bash
   python فحص_شامل_للنظام.py
   ```

2. **تثبيت المتطلبات**:
   ```bash
   python install_dependencies.py
   ```

3. **تشغيل النظام**:
   ```bash
   python launcher.py
   ```

4. **بناء ملف تنفيذي جديد**:
   ```bash
   python build_standalone.py
   ```

---

## 🔍 استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة والحلول:

#### 1. "Python غير موجود"
```bash
# الحل: استخدم الملف التنفيذي المستقل
نظام_إدارة_المستندات_المحاسبية.exe
```

#### 2. "مكتبة مفقودة"
```bash
# الحل: تشغيل المثبت التلقائي
python install_dependencies.py
```

#### 3. "خطأ في فتح ملف Excel"
```bash
# الحل: إغلاق Excel وتشغيل كمدير
taskkill /f /im excel.exe
# انقر بالزر الأيمن -> "تشغيل كمدير"
```

#### 4. "مشكلة في الترميز العربي"
```bash
# الحل: استخدام الملف المحسن
تشغيل_النظام_الشامل.bat
```

### أدوات التشخيص:

```bash
# فحص شامل
python فحص_شامل_للنظام.py

# فحص المتطلبات فقط
python install_dependencies.py

# فحص الملفات
dir *.py *.xlsx *.json
```

---

## 📈 مقارنة الإصدارات

| الميزة | الإصدار السابق | الإصدار المستقل |
|--------|----------------|------------------|
| **تثبيت Python** | ✅ مطلوب | ❌ غير مطلوب |
| **تثبيت المكتبات** | ✅ مطلوب | ❌ غير مطلوب |
| **حجم التوزيع** | ~10 MB | ~50 MB |
| **سرعة التشغيل** | سريع | سريع جداً |
| **سهولة النشر** | متوسطة | عالية جداً |
| **التوافق** | يحتاج Python | يعمل على أي جهاز |
| **الأمان** | جيد | ممتاز |
| **الصيانة** | تحتاج خبرة | بسيطة |

---

## 🔐 الأمان والخصوصية

### المميزات الأمنية:
- 🔒 **تشفير البيانات**: حماية ملفات المستخدمين
- 🛡️ **نظام صلاحيات**: تحكم دقيق في الوصول
- 📝 **سجلات النشاط**: تتبع جميع العمليات
- 🔑 **كلمات مرور قوية**: متطلبات أمان محسنة

### حماية البيانات:
- 💾 **نسخ احتياطية تلقائية**: حفظ البيانات بانتظام
- 🔄 **استرداد البيانات**: إمكانية استرداد الملفات التالفة
- 🛡️ **حماية من الفيروسات**: فحص الملفات المرفوعة
- 🔐 **تشفير الاتصالات**: حماية نقل البيانات

---

## 🚀 خطط التطوير المستقبلية

### الإصدار 2.1.0 (قريباً):
- 🌐 **واجهة ويب**: إصدار يعمل عبر المتصفح
- 📱 **تطبيق موبايل**: دعم الهواتف الذكية
- ☁️ **التخزين السحابي**: مزامنة البيانات
- 🤖 **الذكاء الاصطناعي**: تحليل البيانات التلقائي

### الإصدار 2.2.0:
- 📊 **تقارير متقدمة**: رسوم بيانية تفاعلية
- 🔗 **تكامل API**: ربط مع أنظمة أخرى
- 🌍 **دعم متعدد اللغات**: إنجليزية وعربية
- 📧 **إشعارات البريد**: تنبيهات تلقائية

---

## 🏥 معلومات المؤسسة

**وزارة الصحة الأردنية**  
**Jordan Ministry of Health**

- 🌐 **الموقع**: https://moh.gov.jo
- 📧 **البريد**: <EMAIL>
- ☎️ **الهاتف**: +962-6-5200000
- 📠 **الفاكس**: +962-6-5200001
- 📍 **العنوان**: عمان، الأردن

### فريق التطوير:
- **مدير المشروع**: قسم تكنولوجيا المعلومات
- **المطورون**: فريق الأنظمة المحاسبية
- **ضمان الجودة**: قسم مراقبة الجودة
- **الدعم الفني**: مركز الدعم التقني

---

## 📄 الترخيص وحقوق الطبع

© 2025 وزارة الصحة الأردنية - جميع الحقوق محفوظة

هذا النظام مطور خصيصاً لوزارة الصحة الأردنية. جميع الحقوق محفوظة ولا يجوز استخدام أو توزيع هذا النظام بدون إذن مكتوب من الوزارة.

### شروط الاستخدام:
- ✅ **الاستخدام الداخلي**: مسموح داخل الوزارة
- ❌ **التوزيع الخارجي**: غير مسموح بدون إذن
- ❌ **التعديل**: غير مسموح بدون إذن
- ✅ **النسخ الاحتياطية**: مسموح للحفظ

---

## 🙏 شكر وتقدير

نتقدم بالشكر الجزيل لجميع من ساهم في تطوير هذا النظام:

- **موظفو وزارة الصحة**: للمتطلبات والاختبار
- **فريق التطوير**: للعمل الدؤوب والإبداع
- **المستخدمون**: للملاحظات والاقتراحات القيمة
- **الإدارة العليا**: للدعم والتوجيه

---

**آخر تحديث**: 2025-01-XX  
**رقم الإصدار**: 2.0.0  
**حالة المشروع**: مستقر وجاهز للإنتاج

---

*تم إعداد هذا التوثيق بواسطة فريق تطوير الأنظمة - وزارة الصحة الأردنية*
