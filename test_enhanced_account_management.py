#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة الحسابات المحسن
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def test_enhanced_account_management():
    """اختبار شامل لنظام إدارة الحسابات المحسن"""
    try:
        print("\n" + "=" * 80)
        print("🧪 اختبار نظام إدارة الحسابات المحسن")
        print("=" * 80)
        
        # إنشاء نافذة رئيسية للاختبار
        root = tk.Tk()
        root.title("اختبار نظام إدارة الحسابات المحسن")
        root.geometry("400x300")
        root.configure(bg='#f8f9fa')
        
        # إنشاء excel_manager
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        print("✅ تم إنشاء ExcelManager بنجاح")
        
        # اختبار دوال إدارة الحسابات الجديدة
        print("\n📋 اختبار دوال إدارة الحسابات:")
        
        # 1. اختبار جلب جميع الحسابات
        print("1️⃣ اختبار جلب جميع الحسابات...")
        accounts = excel.get_all_accounts()
        print(f"   ✅ تم جلب {len(accounts)} حساب")
        
        if accounts:
            print("   📊 أول 3 حسابات:")
            for i, account in enumerate(accounts[:3], 1):
                print(f"      {i}. {account['account_num']} - {account['account_name']} (الرصيد: {account['balance']})")
        
        # 2. اختبار جلب مستندات حساب (إذا كان هناك حسابات)
        if accounts:
            test_account = accounts[0]
            print(f"\n2️⃣ اختبار جلب مستندات الحساب: {test_account['sheet_name']}")
            documents = excel.get_account_documents(test_account['sheet_name'])
            print(f"   ✅ تم جلب {len(documents)} مستند")
            
            if documents:
                print("   📄 أول 3 مستندات:")
                for i, doc in enumerate(documents[:3], 1):
                    print(f"      {i}. المبلغ: {doc['amount']}, المستند: {doc['doc_num']}, التأدية: {doc['pay_num']}")
        
        # إنشاء إطار للأزرار
        buttons_frame = tk.Frame(root, bg='#f8f9fa')
        buttons_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(buttons_frame, text="🧪 اختبار نظام إدارة الحسابات المحسن",
                              font=('Arial', 14, 'bold'), bg='#f8f9fa')
        title_label.pack(pady=(0, 20))
        
        # زر اختبار نافذة إضافة حساب محسنة
        def test_add_account_dialog():
            try:
                from app import AddAccountDialog
                AddAccountDialog(root, excel)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إضافة الحساب: {str(e)}")
        
        tk.Button(buttons_frame, text="🏦 اختبار نافذة إضافة حساب محسنة",
                 command=test_add_account_dialog,
                 font=('Arial', 10), bg='#007bff', fg='white',
                 padx=20, pady=10).pack(pady=5, fill=tk.X)
        
        # زر اختبار نافذة إدارة الحسابات
        def test_manage_accounts_dialog():
            try:
                from manage_accounts import ManageAccountsDialog
                ManageAccountsDialog(root, excel)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الحسابات: {str(e)}")
        
        tk.Button(buttons_frame, text="📊 اختبار نافذة إدارة الحسابات",
                 command=test_manage_accounts_dialog,
                 font=('Arial', 10), bg='#28a745', fg='white',
                 padx=20, pady=10).pack(pady=5, fill=tk.X)
        
        # زر اختبار نافذة تفاصيل الحساب المحسنة
        def test_account_details():
            try:
                if accounts:
                    test_account = accounts[0]
                    from manage_accounts import EnhancedAccountDetailsWindow
                    EnhancedAccountDetailsWindow(root, excel, test_account['sheet_name'], 
                                               test_account['account_num'], test_account['account_name'], 
                                               test_account['balance'])
                else:
                    messagebox.showinfo("تنبيه", "لا توجد حسابات للاختبار")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح نافذة تفاصيل الحساب: {str(e)}")
        
        tk.Button(buttons_frame, text="📄 اختبار نافذة تفاصيل الحساب المحسنة",
                 command=test_account_details,
                 font=('Arial', 10), bg='#ffc107', fg='black',
                 padx=20, pady=10).pack(pady=5, fill=tk.X)
        
        # زر إغلاق
        tk.Button(buttons_frame, text="❌ إغلاق",
                 command=root.destroy,
                 font=('Arial', 10), bg='#dc3545', fg='white',
                 padx=20, pady=10).pack(pady=(20, 0), fill=tk.X)
        
        # معلومات الاختبار
        info_text = f"""
📊 معلومات الاختبار:
• عدد الحسابات: {len(accounts)}
• الدوال الجديدة: ✅ تعمل
• النوافذ المحسنة: ✅ جاهزة للاختبار

🎯 ميزات جديدة:
• قائمة منسدلة للحسابات الموجودة
• أزرار تعديل وحذف الحسابات
• عرض المستندات بشكل منسق
• تعديل المستندات مع الحفظ التلقائي
• رسائل تأكيد للحذف
        """
        
        info_label = tk.Label(buttons_frame, text=info_text,
                             font=('Arial', 9), bg='#f8f9fa', fg='#666',
                             justify=tk.LEFT)
        info_label.pack(pady=(10, 0), fill=tk.X)
        
        print("\n🖥️ عرض نافذة الاختبار...")
        print("   يمكنك الآن اختبار جميع الميزات الجديدة")
        print("   أغلق النافذة عند الانتهاء من الاختبار")
        
        # تشغيل النافذة
        root.mainloop()
        
        print("\n🎉 تم اختبار نظام إدارة الحسابات المحسن بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_new_functions():
    """اختبار الدوال الجديدة في excel_manager"""
    try:
        print("\n" + "=" * 80)
        print("🔧 اختبار الدوال الجديدة في excel_manager")
        print("=" * 80)
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # اختبار get_all_accounts
        print("1️⃣ اختبار get_all_accounts()...")
        accounts = excel.get_all_accounts()
        print(f"   ✅ النتيجة: {len(accounts)} حساب")
        
        # اختبار get_account_documents
        if accounts:
            test_sheet = accounts[0]['sheet_name']
            print(f"2️⃣ اختبار get_account_documents('{test_sheet}')...")
            documents = excel.get_account_documents(test_sheet)
            print(f"   ✅ النتيجة: {len(documents)} مستند")
            
            # اختبار تعديل مستند (إذا كان هناك مستندات)
            if documents:
                test_doc = documents[0]
                print(f"3️⃣ اختبار update_document()...")
                original_amount = test_doc['amount']
                new_amount = original_amount + 0.001  # تغيير طفيف للاختبار
                
                success = excel.update_document(test_sheet, test_doc['row'], test_doc['col'], 
                                              new_amount, test_doc['doc_num'], test_doc['pay_num'])
                if success:
                    print(f"   ✅ تم تعديل المبلغ من {original_amount} إلى {new_amount}")
                    
                    # إعادة المبلغ الأصلي
                    excel.update_document(test_sheet, test_doc['row'], test_doc['col'], 
                                        original_amount, test_doc['doc_num'], test_doc['pay_num'])
                    print(f"   ✅ تم إعادة المبلغ الأصلي {original_amount}")
                else:
                    print("   ❌ فشل في تعديل المستند")
        
        print("\n🎉 تم اختبار جميع الدوال الجديدة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبارات نظام إدارة الحسابات المحسن")
    
    # تشغيل اختبار الدوال الجديدة
    functions_test_result = test_new_functions()
    
    # تشغيل اختبار النوافذ المحسنة
    ui_test_result = test_enhanced_account_management()
    
    # النتيجة النهائية
    if functions_test_result and ui_test_result:
        print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("✅ نظام إدارة الحسابات المحسن جاهز للاستخدام")
    else:
        print("\n❌ فشل في بعض الاختبارات")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
