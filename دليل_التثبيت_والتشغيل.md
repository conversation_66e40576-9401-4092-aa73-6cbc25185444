# دليل التثبيت والتشغيل
## نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية

---

## 📋 المحتويات
1. [متطلبات النظام](#متطلبات-النظام)
2. [طرق التثبيت](#طرق-التثبيت)
3. [طرق التشغيل](#طرق-التشغيل)
4. [حل المشاكل الشائعة](#حل-المشاكل-الشائعة)
5. [الدعم الفني](#الدعم-الفني)

---

## 🖥️ متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل**: Windows 10 أو أحدث
- **المعالج**: Intel Core i3 أو معادل
- **الذاكرة**: 4 GB RAM
- **مساحة القرص**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

### المستحسن:
- **نظام التشغيل**: Windows 11
- **المعالج**: Intel Core i5 أو أحدث
- **الذاكرة**: 8 GB RAM أو أكثر
- **مساحة القرص**: 2 GB مساحة فارغة
- **الشاشة**: دقة 1920x1080 أو أعلى

---

## 🔧 طرق التثبيت

### الطريقة الأولى: الملف التنفيذي المستقل (مستحسن)

#### المميزات:
- ✅ **لا يحتاج تثبيت Python**
- ✅ **لا يحتاج تثبيت مكتبات إضافية**
- ✅ **تشغيل فوري**
- ✅ **مناسب لجميع المستخدمين**

#### خطوات التثبيت:
1. **تحميل الملف التنفيذي**:
   - احصل على ملف `نظام_إدارة_المستندات_المحاسبية.exe`
   
2. **إنشاء مجلد للنظام**:
   ```
   C:\AccountingSystem\
   ```
   
3. **نسخ الملفات**:
   - انسخ الملف التنفيذي إلى المجلد
   - انسخ ملفات البيانات (*.xlsx, *.json)
   
4. **التشغيل**:
   - انقر نقراً مزدوجاً على الملف التنفيذي
   - أو شغله من سطر الأوامر

#### بناء الملف التنفيذي:
إذا كنت تريد بناء الملف التنفيذي بنفسك:

```batch
# تشغيل سكريبت البناء
python build_standalone.py
```

---

### الطريقة الثانية: التثبيت اليدوي مع Python

#### المتطلبات:
- Python 3.7 أو أحدث
- pip (مدير الحزم)

#### خطوات التثبيت:

1. **تثبيت Python**:
   - تحميل من: https://www.python.org/downloads/
   - تأكد من تحديد "Add Python to PATH"
   
2. **التحقق من التثبيت**:
   ```cmd
   python --version
   pip --version
   ```
   
3. **تثبيت المتطلبات التلقائي**:
   ```cmd
   python install_dependencies.py
   ```
   
4. **أو التثبيت اليدوي**:
   ```cmd
   pip install -r requirements.txt
   ```

---

### الطريقة الثالثة: التثبيت باستخدام setup.py

```cmd
# التثبيت الكامل
python setup.py install

# أو التثبيت للتطوير
python setup.py develop
```

---

## 🚀 طرق التشغيل

### 1. الملف التنفيذي المستقل
```cmd
# تشغيل مباشر
نظام_إدارة_المستندات_المحاسبية.exe
```

### 2. باستخدام Python

#### الطريقة المفضلة:
```cmd
python launcher.py
```

#### طرق بديلة:
```cmd
# تشغيل التطبيق مباشرة
python app.py

# تشغيل مع فحص شامل
python تشغيل_النظام_الشامل.bat
```

### 3. ملفات Batch المساعدة

#### للتشغيل السريع:
```cmd
تشغيل_النظام_الشامل.bat
```

#### للتشغيل مع فحص المتطلبات:
```cmd
تشغيل_النظام_المحسن.bat
```

---

## 🔍 حل المشاكل الشائعة

### مشكلة: "Python غير موجود"

**الحل**:
1. تثبيت Python من الموقع الرسمي
2. إضافة Python إلى PATH
3. إعادة تشغيل سطر الأوامر

```cmd
# فحص Python
python --version

# إذا لم يعمل، جرب:
py --version
```

### مشكلة: "openpyxl غير موجود"

**الحل**:
```cmd
# تثبيت openpyxl
pip install openpyxl

# أو تشغيل المثبت التلقائي
python install_dependencies.py
```

### مشكلة: "tkinter غير موجود"

**الحل**:
- tkinter يأتي مع Python عادة
- إذا كان مفقوداً، أعد تثبيت Python مع تحديد "tcl/tk and IDLE"

### مشكلة: "خطأ في فتح ملف Excel"

**الأسباب المحتملة**:
1. الملف مفتوح في Excel
2. عدم وجود صلاحيات كتابة
3. الملف تالف

**الحل**:
```cmd
# إغلاق Excel تماماً
taskkill /f /im excel.exe

# تشغيل البرنامج كمدير
# انقر بالزر الأيمن -> "تشغيل كمدير"
```

### مشكلة: "خطأ في الترميز العربي"

**الحل**:
```cmd
# تعيين ترميز UTF-8
chcp 65001

# أو تشغيل الملف المحسن
تشغيل_النظام_الشامل.bat
```

### مشكلة: "الواجهة لا تظهر بشكل صحيح"

**الحل**:
1. تحديث برامج تشغيل الشاشة
2. تغيير إعدادات DPI في Windows
3. تشغيل البرنامج في وضع التوافق

---

## 🛠️ أدوات التشخيص

### فحص شامل للنظام:
```cmd
python comprehensive_check.py
```

### فحص المتطلبات:
```cmd
python install_dependencies.py
```

### فحص الملفات:
```cmd
python run_debug.py
```

---

## 📊 معلومات المكتبات المطلوبة

### المكتبات الأساسية:
| المكتبة | الإصدار | الوصف |
|---------|---------|--------|
| openpyxl | ≥3.1.0 | معالجة ملفات Excel |
| tkinter | مدمجة | واجهة المستخدم |

### المكتبات الإضافية:
| المكتبة | الإصدار | الوصف |
|---------|---------|--------|
| ttkthemes | ≥3.2.2 | تحسين المظهر |
| Pillow | ≥10.0.0 | معالجة الصور |
| pyinstaller | ≥5.13.0 | بناء الملف التنفيذي |

### المكتبات الاختيارية:
| المكتبة | الإصدار | الوصف |
|---------|---------|--------|
| arabic-reshaper | ≥3.0.0 | دعم النصوص العربية |
| python-bidi | ≥0.4.2 | اتجاه النص العربي |
| cryptography | ≥41.0.0 | التشفير والأمان |

---

## 🔐 معلومات تسجيل الدخول الافتراضية

### المدير الرئيسي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin`
- **الصلاحيات**: كاملة

### مستخدم عادي:
- **اسم المستخدم**: `user`
- **كلمة المرور**: `user`
- **الصلاحيات**: محدودة

> ⚠️ **تنبيه أمني**: يُنصح بتغيير كلمات المرور الافتراضية فور التشغيل الأول

---

## 📁 هيكل الملفات

```
AccountingSystem/
├── 📄 ملفات التشغيل
│   ├── launcher.py                 # مشغل النظام
│   ├── app.py                     # التطبيق الرئيسي
│   ├── تشغيل_النظام_الشامل.bat    # ملف تشغيل محسن
│   └── نظام_إدارة_المستندات_المحاسبية.exe  # ملف تنفيذي
│
├── 📊 ملفات البيانات
│   ├── accounting_system.xlsx     # البيانات الرئيسية
│   ├── Accounting system deductions.xlsx  # بيانات الخصومات
│   └── users.json                # بيانات المستخدمين
│
├── 🔧 ملفات الإعداد
│   ├── requirements.txt           # المتطلبات
│   ├── setup.py                  # ملف الإعداد
│   └── install_dependencies.py   # مثبت المتطلبات
│
├── 🏗️ ملفات البناء
│   ├── build_standalone.py       # بناء الملف التنفيذي
│   ├── accounting_system.spec    # إعدادات PyInstaller
│   └── version_info.txt          # معلومات الإصدار
│
└── 📚 ملفات التوثيق
    ├── دليل_التثبيت_والتشغيل.md   # هذا الملف
    ├── README.md                 # معلومات المشروع
    └── تقرير_المشروع.md          # تقرير شامل
```

---

## 🆘 الدعم الفني

### معلومات الاتصال:
- **الموقع الرسمي**: https://moh.gov.jo
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +962-6-5200000
- **الفاكس**: +962-6-5200001

### ساعات العمل:
- **الأحد - الخميس**: 8:00 ص - 4:00 م
- **الجمعة - السبت**: مغلق

### المعلومات المطلوبة عند طلب الدعم:
1. إصدار النظام
2. إصدار Windows
3. رسالة الخطأ (إن وجدت)
4. خطوات إعادة إنتاج المشكلة
5. لقطة شاشة للمشكلة

---

## 📝 سجل التحديثات

### الإصدار 2.0.0 (2025-01-XX)
- ✅ دعم الملف التنفيذي المستقل
- ✅ تحسين نظام التثبيت التلقائي
- ✅ إضافة أدوات التشخيص
- ✅ تحسين دعم اللغة العربية
- ✅ تحسين الأمان والاستقرار

### الإصدار 1.1.0 (2024-XX-XX)
- ✅ إضافة نظام المستخدمين
- ✅ تحسين واجهة المستخدم
- ✅ إضافة وظائف البحث المتقدم

### الإصدار 1.0.0 (2024-XX-XX)
- ✅ الإصدار الأولي
- ✅ الوظائف الأساسية

---

## ⚖️ حقوق الطبع والنشر

© 2025 وزارة الصحة الأردنية - جميع الحقوق محفوظة

هذا النظام مطور خصيصاً لوزارة الصحة الأردنية ولا يجوز استخدامه أو توزيعه بدون إذن مكتوب من الوزارة.

---

## 🌟 شكر وتقدير

نتقدم بالشكر لجميع الموظفين في وزارة الصحة الأردنية الذين ساهموا في تطوير وتحسين هذا النظام.

---

**تم إعداد هذا الدليل بواسطة فريق تطوير الأنظمة - وزارة الصحة الأردنية**

*آخر تحديث: 2025-01-XX*
