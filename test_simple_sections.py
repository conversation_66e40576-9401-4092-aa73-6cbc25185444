#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لأسماء الأقسام المحدثة
"""

import sys
import traceback
import tkinter as tk
from tkinter import messagebox

def main():
    """اختبار أسماء الأقسام"""

    print("🧪 اختبار أسماء الأقسام المحدثة")
    print("=" * 50)
    print("📝 تم تعديل عرض أسماء الأقسام لتظهر فقط 'القسم 1', 'القسم 2', إلخ")
    print("=" * 50)

    try:
        # استيراد الوحدات
        print("📦 استيراد الوحدات...")
        from excel_manager import ExcelManager
        from manage_accounts import ManageAccountsDialog

        print("✅ تم استيراد الوحدات بنجاح")

        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        root.title("اختبار أسماء الأقسام المحدثة")
        root.geometry("800x600")

        # إنشاء مدير Excel
        print("📊 إنشاء مدير Excel...")
        excel_manager = ExcelManager()

        # تحميل ملف Excel
        print("📁 تحميل ملف Excel...")
        # البيانات محملة بالفعل في المنشئ
        if True:
            print("✅ تم تحميل ملف Excel بنجاح")

            # إنشاء نافذة إدارة الحسابات
            print("🏗️ إنشاء نافذة إدارة الحسابات...")
            manage_dialog = ManageAccountsDialog(root, excel_manager)

            # إنشاء واجهة بسيطة
            main_frame = tk.Frame(root, padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # عنوان
            title_label = tk.Label(main_frame,
                                 text="اختبار أسماء الأقسام المحدثة",
                                 font=("Arial", 18, "bold"),
                                 fg="#2c3e50")
            title_label.pack(pady=(0, 20))

            # معلومات التحديث
            update_info = tk.Label(main_frame,
                                 text="تم تعديل عرض أسماء الأقسام لتظهر بشكل مبسط",
                                 font=("Arial", 12),
                                 fg="#27ae60")
            update_info.pack(pady=(0, 20))

            # زر فتح إدارة الحسابات
            open_btn = tk.Button(main_frame,
                                text="فتح إدارة الحسابات لاختبار التحديث",
                                command=lambda: manage_dialog.deiconify(),
                                bg='#3498db', fg='white',
                                font=("Arial", 14, "bold"),
                                padx=30, pady=15)
            open_btn.pack(pady=20)

            # معلومات إضافية
            info_label = tk.Label(main_frame,
                                text="اختر أي حساب وافتح تفاصيله لرؤية أسماء الأقسام المحدثة",
                                font=("Arial", 10),
                                fg="#7f8c8d")
            info_label.pack(pady=(20, 0))

            print("✅ تم إعداد واجهة الاختبار")
            print("🚀 بدء تشغيل الاختبار...")
            print("-" * 50)

            # تشغيل التطبيق
            root.mainloop()

        else:
            print("❌ فشل في تحميل ملف Excel")
            messagebox.showerror("خطأ", "فشل في تحميل ملف Excel")

    except Exception as e:
        error_msg = f"خطأ عام: {str(e)}"
        print(f"🚨 {error_msg}")
        print(f"📋 التفاصيل:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
