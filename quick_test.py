#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاح مشكلة calculate
"""

def quick_test():
    """اختبار سريع للإصلاحات"""
    import os
    print("🧪 اختبار سريع للإصلاحات")
    print("=" * 40)

    try:
        # حذف الملف التالف إذا كان موجوداً
        if os.path.exists("accounting_system.xlsx"):
            os.remove("accounting_system.xlsx")
            print("🗑️ تم حذف الملف التالف")

        # استيراد ExcelManager
        from excel_manager import ExcelManager

        # إنشاء ExcelManager جديد
        print("📝 إنشاء ExcelManager جديد...")
        excel = ExcelManager()

        if excel.workbook:
            print("✅ تم إنشاء الملف بنجاح")
            print(f"📋 الصفحات: {excel.workbook.sheetnames}")

            # إنشاء حساب اختبار
            print("💼 إنشاء حساب اختبار...")
            result = excel.create_account_sheet("001", "حساب تجريبي", 1000.0)
            print(f"✅ إنشاء الحساب: {'نجح' if result else 'فشل'}")

            if result:
                print(f"📋 الصفحات بعد إضافة الحساب: {excel.workbook.sheetnames}")

                # إضافة مستند
                print("📄 إضافة مستند...")
                doc_result = excel.add_document("001-حساب تجريبي", 500.0, "DOC001", "PAY001")
                print(f"✅ إضافة المستند: {'نجح' if doc_result else 'فشل'}")

                # حفظ الملف
                print("💾 حفظ الملف...")
                save_result = excel.save_workbook()
                print(f"✅ حفظ الملف: {'نجح' if save_result else 'فشل'}")

                # اختبار إعادة التحميل
                print("🔄 اختبار إعادة التحميل...")
                excel2 = ExcelManager()
                if excel2.workbook:
                    print(f"📋 الصفحات المحملة: {excel2.workbook.sheetnames}")
                    if "001-حساب تجريبي" in excel2.workbook.sheetnames:
                        print("✅ تم استرجاع الحساب بنجاح")
                        return True
                    else:
                        print("❌ فشل في استرجاع الحساب")
                        return False
                else:
                    print("❌ فشل في إعادة التحميل")
                    return False

        else:
            print("❌ فشل في إنشاء الملف")
            return False

    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 اختبار سريع لإصلاح مشكلة calculate")
    print("=" * 40)

    success = quick_test()

    if success:
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ يمكن الآن إضافة المستندات بدون أخطاء")
    else:
        print("\n❌ لا تزال هناك مشكلة")

    input("\nاضغط Enter للإغلاق...")
