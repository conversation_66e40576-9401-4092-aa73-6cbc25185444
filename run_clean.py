#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظيف لنظام إدارة المستندات المحاسبية
وزارة الصحة - التأمين الصحي
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import subprocess

def main():
    """تشغيل التطبيق مع إغلاق CMD"""
    try:
        # التأكد من وجود الملفات المطلوبة
        required_files = ['app.py', 'user_manager.py', 'excel_manager.py']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            input("اضغط Enter للخروج...")
            return
        
        print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية")
        print("🏥 وزارة الصحة - التأمين الصحي")
        print("=" * 60)
        
        # حذف الملفات التالفة
        files_to_clean = ["users.json", "accounting_system.xlsx"]
        for file in files_to_clean:
            if os.path.exists(file):
                try:
                    os.remove(file)
                    print(f"🔄 تم تنظيف: {file}")
                except:
                    pass
        
        print("✅ تم تنظيف الملفات")
        print("🔄 تحميل النظام...")
        
        # استيراد التطبيق
        from app import AccountingApp
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")
        
        print("✅ تم تحميل النظام بنجاح")
        print("🔐 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin")
        print("=" * 60)
        print("💡 ستختفي هذه النافذة تلقائياً بعد تسجيل الدخول")
        
        # دالة لإغلاق CMD بعد تسجيل الدخول
        def on_login_success_wrapper():
            # إخفاء نافذة CMD
            try:
                import ctypes
                ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
            except:
                pass
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        
        # ربط دالة إخفاء CMD مع نجاح تسجيل الدخول
        original_on_login_success = app.on_login_success
        def enhanced_on_login_success():
            on_login_success_wrapper()
            original_on_login_success()
        
        app.on_login_success = enhanced_on_login_success
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {str(e)}")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("   pip install openpyxl")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
