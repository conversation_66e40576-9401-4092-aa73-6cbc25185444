#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة المستندات المحاسبية
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import subprocess

def install_required_packages():
    """تثبيت المكتبات المطلوبة تلقائياً"""
    required_packages = ['openpyxl']

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ المكتبة {package} مثبتة بالفعل")
        except ImportError:
            print(f"📦 تثبيت المكتبة {package}...")
            try:
                # محاولة تثبيت المكتبة باستخدام py -m pip
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', package],
                                      capture_output=True, text=True, check=True)
                print(f"✅ تم تثبيت {package} بنجاح")
            except subprocess.CalledProcessError as e:
                print(f"❌ فشل في تثبيت {package}: {e}")
                print(f"💡 يرجى تثبيت المكتبة يدوياً: py -m pip install {package}")
                return False
            except Exception as e:
                print(f"❌ خطأ غير متوقع أثناء تثبيت {package}: {e}")
                return False

    return True

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية...")
        print("📋 وزارة الصحة - التأمين الصحي")
        print("=" * 50)

        # تثبيت المكتبات المطلوبة تلقائياً
        print("🔍 فحص وتثبيت المكتبات المطلوبة...")
        if not install_required_packages():
            print("❌ فشل في تثبيت المكتبات المطلوبة")
            input("اضغط Enter للخروج...")
            return
        print("✅ جميع المكتبات جاهزة")
        print()

        # التأكد من وجود الملفات المطلوبة
        required_files = ['app.py', 'user_manager.py', 'excel_manager.py']
        missing_files = []

        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)

        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            input("اضغط Enter للخروج...")
            return

        # استيراد التطبيق
        from app import AccountingApp

        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")

        print("✅ تم تحميل النظام بنجاح")
        print("🔐 سيتم عرض نافذة تسجيل الدخول...")
        print()
        print("📝 معلومات المستخدم الرئيسي:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin")
        print("=" * 50)

        # إنشاء التطبيق
        app = AccountingApp(root)

    except ImportError as e:
        print(f"❌ خطأ في استيراد الملفات: {str(e)}")
        print("💡 تأكد من وجود جميع ملفات النظام")
        input("اضغط Enter للخروج...")

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
