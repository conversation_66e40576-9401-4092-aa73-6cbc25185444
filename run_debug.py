#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشخيص وتشغيل مع عرض فوري للأخطاء
"""

import sys
import traceback
from datetime import datetime
import os

def print_separator(title=""):
    """طباعة خط فاصل"""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def test_file_exists():
    """فحص وجود الملفات المطلوبة"""
    print_separator("فحص الملفات المطلوبة")
    
    required_files = [
        'app.py',
        'excel_manager.py',
        'manage_accounts.py',
        'user_manager.py',
        'document_window.py',
        'search_window.py',
        'accounting_system.xlsx'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - موجود")
        else:
            print(f"❌ {file} - غير موجود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {len(missing_files)}")
        return False
    
    print("\n✅ جميع الملفات المطلوبة موجودة")
    return True

def test_imports():
    """اختبار استيراد الوحدات"""
    print_separator("اختبار استيراد الوحدات")
    
    imports = [
        ('tkinter', 'import tkinter as tk'),
        ('openpyxl', 'import openpyxl'),
        ('app', 'from app import AccountingApp'),
        ('excel_manager', 'from excel_manager import ExcelManager'),
        ('manage_accounts', 'from manage_accounts import ManageAccountsDialog'),
        ('user_manager', 'from user_manager import UserManager'),
        ('document_window', 'from document_window import AddDocumentWindow'),
        ('search_window', 'from search_window import SearchWindow')
    ]
    
    failed_imports = []
    
    for name, import_cmd in imports:
        try:
            exec(import_cmd)
            print(f"✅ {name} - OK")
        except Exception as e:
            print(f"❌ {name} - خطأ: {str(e)}")
            failed_imports.append((name, str(e)))
    
    if failed_imports:
        print(f"\n⚠️ فشل في استيراد {len(failed_imports)} وحدة")
        for name, error in failed_imports:
            print(f"   {name}: {error}")
        return False
    
    print("\n✅ تم استيراد جميع الوحدات بنجاح")
    return True

def run_app_with_debug():
    """تشغيل التطبيق مع تشخيص مفصل"""
    print_separator("تشغيل التطبيق")
    
    try:
        print("📦 استيراد الوحدات...")
        import tkinter as tk
        from app import AccountingApp
        
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        
        print("🏗️ إنشاء كائن التطبيق...")
        
        # إعداد معالج الأخطاء
        def error_handler(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                print("\n⚠️ تم إيقاف التطبيق بواسطة المستخدم")
                return
            
            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            print(f"\n🚨 خطأ في التطبيق:")
            print(f"النوع: {exc_type.__name__}")
            print(f"الرسالة: {str(exc_value)}")
            print(f"التفاصيل:\n{error_msg}")
            
            # اقتراح حلول
            if 'AccountDetailsDialog' in error_msg:
                print("\n💡 مشكلة في نافذة تفاصيل الحساب:")
                print("   - تحقق من وجود الحساب في ملف Excel")
                print("   - تأكد من صحة بنية ملف Excel")
                print("   - جرب إعادة إنشاء الحساب")
            
            if 'Excel' in error_msg or 'openpyxl' in error_msg:
                print("\n💡 مشكلة في ملف Excel:")
                print("   - تأكد من وجود ملف accounting_system.xlsx")
                print("   - أغلق ملف Excel إذا كان مفتوحاً")
                print("   - تحقق من صلاحيات الملف")
        
        sys.excepthook = error_handler
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        print("▶️ بدء تشغيل التطبيق...")
        print("📱 التطبيق يعمل الآن - يمكنك استخدامه")
        print("🔍 راقب هذه النافذة لأي أخطاء")
        print_separator()
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        print(f"\n🚨 خطأ في تشغيل التطبيق:")
        print(f"النوع: {type(e).__name__}")
        print(f"الرسالة: {str(e)}")
        print(f"التفاصيل:\n{traceback.format_exc()}")
        
        # اقتراح حلول حسب نوع الخطأ
        if isinstance(e, ImportError):
            print("\n💡 حلول لخطأ الاستيراد:")
            print("   - تأكد من وجود جميع ملفات المشروع")
            print("   - أعد تثبيت المكتبات المطلوبة")
            print("   - تحقق من إصدار Python")
        
        elif isinstance(e, FileNotFoundError):
            print("\n💡 حلول لخطأ الملف المفقود:")
            print("   - تأكد من وجود ملف accounting_system.xlsx")
            print("   - تحقق من المسار الصحيح")
            print("   - أنشئ ملف Excel جديد إذا لزم الأمر")
        
        else:
            print("\n💡 حلول عامة:")
            print("   - أعد تشغيل التطبيق")
            print("   - أعد تشغيل الكمبيوتر")
            print("   - تحقق من ملفات النظام")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 نظام إدارة المستندات المحاسبية - تشخيص وتشغيل")
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print_separator()
    
    try:
        # فحص الملفات
        if not test_file_exists():
            print("\n❌ لا يمكن المتابعة - ملفات مفقودة")
            return
        
        # اختبار الاستيرادات
        if not test_imports():
            print("\n❌ لا يمكن المتابعة - مشاكل في الاستيراد")
            return
        
        # تشغيل التطبيق
        run_app_with_debug()
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف التطبيق بواسطة المستخدم")
    
    except Exception as e:
        print(f"\n🚨 خطأ عام: {str(e)}")
        print(f"التفاصيل:\n{traceback.format_exc()}")
    
    finally:
        print(f"\n🔚 انتهاء التشغيل - {datetime.now().strftime('%H:%M:%S')}")
        print_separator()

if __name__ == "__main__":
    main()
