/* نمط التطبيق المحاسبي */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

* {
    font-family: 'Cairo', sans-serif;
}

/* الألوان الأساسية */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* الخلفية العامة */
body {
    background-color: var(--light-color);
    font-size: 14px;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.2rem;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* قائمة التنقل الجانبية */
.list-group-item {
    border: none;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #e3f2fd;
    transform: translateX(5px);
}

.list-group-item.active {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
}

.list-group-item i {
    width: 20px;
    text-align: center;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #2ecc71);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #e67e22);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #c0392b);
    border: none;
}

/* حقول الإدخال */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.input-group-text {
    background-color: var(--light-color);
    border: 2px solid #e9ecef;
    border-radius: 8px 0 0 8px;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-footer {
    border-top: none;
    border-radius: 0 0 15px 15px;
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 600;
}

.alert-success {
    background: linear-gradient(45deg, var(--success-color), #2ecc71);
    color: white;
}

.alert-danger {
    background: linear-gradient(45deg, var(--danger-color), #c0392b);
    color: white;
}

.alert-warning {
    background: linear-gradient(45deg, var(--warning-color), #e67e22);
    color: white;
}

.alert-info {
    background: linear-gradient(45deg, var(--info-color), #3498db);
    color: white;
}

/* بطاقات الحسابات */
.account-card {
    border-left: 4px solid var(--secondary-color);
    transition: all 0.3s ease;
}

.account-card:hover {
    border-left-color: var(--success-color);
    transform: translateX(5px);
}

.account-balance {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--success-color);
}

.account-balance.negative {
    color: var(--danger-color);
}

/* إحصائيات لوحة التحكم */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: scale(1.05);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* تحسينات للطباعة */
@media print {
    .navbar,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 12px;
    }
}

/* تأثيرات التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تأثيرات الظهور */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيق خاص للأرقام العربية */
.arabic-numbers {
    font-family: 'Cairo', sans-serif;
    direction: ltr;
    text-align: right;
}

/* تحسينات للنصوص */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* أيقونات ملونة */
.icon-primary { color: var(--primary-color); }
.icon-secondary { color: var(--secondary-color); }
.icon-success { color: var(--success-color); }
.icon-warning { color: var(--warning-color); }
.icon-danger { color: var(--danger-color); }
.icon-info { color: var(--info-color); }
