#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة عدم ظهور الأزرار في الواجهة
"""

import sys
import os

def fix_buttons_display():
    """إصلاح مشكلة عدم ظهور الأزرار"""
    try:
        print("🔧 بدء إصلاح مشكلة عدم ظهور الأزرار...")
        
        # قراءة ملف app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص وجود الدوال المطلوبة
        required_functions = [
            "def create_action_buttons(self, parent_frame):",
            "def setup_modern_ui(self):",
            "def create_main_frame_with_permissions(self):"
        ]
        
        missing_functions = []
        for func in required_functions:
            if func in content:
                print(f"✅ الدالة موجودة: {func}")
            else:
                print(f"❌ الدالة مفقودة: {func}")
                missing_functions.append(func)
        
        if missing_functions:
            print("❌ بعض الدوال مفقودة - لا يمكن المتابعة")
            return False
        
        # فحص استدعاء create_action_buttons
        if "self.create_action_buttons(buttons_frame)" in content:
            print("✅ يتم استدعاء create_action_buttons")
        else:
            print("❌ لا يتم استدعاء create_action_buttons")
            return False
        
        # إنشاء نسخة محسنة من الدالة مع تسجيل مفصل
        enhanced_function = '''
    def create_action_buttons(self, parent_frame):
        """إنشاء أزرار العمليات الرئيسية مع تسجيل مفصل"""
        print("🔧 بدء إنشاء الأزرار...")
        
        # قائمة الأزرار مع الصلاحيات
        buttons_config = [
            # =============================================================================
            # قسم الحسابات الرئيسية
            # =============================================================================
            {
                'text': '📁 إضافة حساب جديد',
                'command': self.add_account,
                'permission': 'add_account',
                'color': '#3498db',
                'hover_color': '#2980b9'
            },
            {
                'text': '📝 إضافة مستند',
                'command': self.add_document,
                'permission': 'add_document',
                'color': '#27ae60',
                'hover_color': '#229954'
            },
            {
                'text': '⚙️ إدارة الحسابات',
                'command': self.manage_accounts,
                'permission': 'edit_account',
                'color': '#e74c3c',
                'hover_color': '#c0392b'
            },
            
            # =============================================================================
            # قسم المقبوضات
            # =============================================================================
            {
                'text': '💰 إضافة حساب مقبوضات',
                'command': self.add_receipts_account,
                'permission': 'add_account',
                'color': '#16a085',
                'hover_color': '#138d75'
            },
            {
                'text': '📄 إضافة مستند مقبوضات',
                'command': self.add_receipts_document,
                'permission': 'add_document',
                'color': '#2ecc71',
                'hover_color': '#27ae60'
            },
            {
                'text': '⚙️ إدارة حسابات المقبوضات',
                'command': self.manage_receipts_accounts,
                'permission': 'edit_account',
                'color': '#8e44ad',
                'hover_color': '#7d3c98'
            },
            
            # =============================================================================
            # قسم البحث والتقارير
            # =============================================================================
            {
                'text': '🔍 بحث في الحسابات',
                'command': self.search_accounts,
                'permission': None,  # متاح للجميع
                'color': '#f39c12',
                'hover_color': '#e67e22'
            },
            {
                'text': '📊 نافذة تقارير الأرصدة',
                'command': self.show_account_balances_window,
                'permission': 'view_reports',
                'color': '#e67e22',
                'hover_color': '#d35400'
            },
            
            # =============================================================================
            # قسم إدارة النظام
            # =============================================================================
            {
                'text': '👥 إدارة المستخدمين',
                'command': self.manage_users,
                'permission': 'manage_users',
                'color': '#34495e',
                'hover_color': '#2c3e50'
            },
            {
                'text': '🚪 خروج من النظام',
                'command': self.exit_application,
                'permission': None,  # متاح للجميع
                'color': '#95a5a6',
                'hover_color': '#7f8c8d'
            }
        ]
        
        print(f"📋 إجمالي الأزرار المطلوبة: {len(buttons_config)}")
        
        # إنشاء الأزرار في شبكة
        row = 0
        col = 0
        max_cols = 3
        created_buttons = 0
        
        for i, btn_config in enumerate(buttons_config):
            try:
                # فحص الصلاحية
                if btn_config['permission']:
                    if hasattr(self, 'user_manager') and hasattr(self.user_manager, 'has_permission'):
                        if not self.user_manager.has_permission(btn_config['permission']):
                            print(f"⚠️ تم تخطي الزر '{btn_config['text']}' - لا توجد صلاحية")
                            continue
                    else:
                        print(f"⚠️ لا يمكن فحص الصلاحية للزر '{btn_config['text']}' - سيتم إنشاؤه")
                
                print(f"🔨 إنشاء الزر {i+1}: {btn_config['text']}")
                
                # إنشاء الزر
                btn = tk.Button(parent_frame,
                               text=btn_config['text'],
                               command=btn_config['command'],
                               font=("Arial", 12, "bold"),
                               bg=btn_config['color'],
                               fg='white',
                               relief='flat',
                               padx=20,
                               pady=15,
                               cursor='hand2',
                               width=25)
                
                btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
                
                # إضافة تأثيرات بصرية
                if hasattr(self, 'add_button_hover_effect'):
                    self.add_button_hover_effect(btn, btn_config['color'], btn_config['hover_color'])
                
                created_buttons += 1
                print(f"✅ تم إنشاء الزر بنجاح في الموقع ({row}, {col})")
                
                # انتقال للعمود التالي
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
                    
            except Exception as e:
                print(f"❌ خطأ في إنشاء الزر '{btn_config['text']}': {str(e)}")
                continue
        
        # تعيين وزن الأعمدة
        try:
            for i in range(max_cols):
                parent_frame.grid_columnconfigure(i, weight=1)
            print(f"✅ تم تعيين وزن الأعمدة")
        except Exception as e:
            print(f"⚠️ خطأ في تعيين وزن الأعمدة: {str(e)}")
        
        print(f"🎉 تم إنشاء {created_buttons} زر من أصل {len(buttons_config)}")
        
        if created_buttons == 0:
            print("❌ لم يتم إنشاء أي زر - فحص المشاكل:")
            print("   1. فحص صلاحيات المستخدم")
            print("   2. فحص وجود parent_frame")
            print("   3. فحص استيراد tkinter")
            
        return created_buttons > 0
'''
        
        # البحث عن الدالة الحالية واستبدالها
        import re
        
        # نمط للعثور على الدالة الحالية
        pattern = r'def create_action_buttons\(self, parent_frame\):.*?(?=\n    def|\nclass|\n\n\nif __name__|$)'
        
        # البحث عن الدالة
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            print("🔍 تم العثور على الدالة الحالية")
            
            # استبدال الدالة
            new_content = content.replace(match.group(0), enhanced_function.strip())
            
            # حفظ الملف المحدث
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ تم تحديث الدالة بنجاح")
            return True
        else:
            print("❌ لم يتم العثور على الدالة للتحديث")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأزرار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_debug_version():
    """إنشاء نسخة تشخيص للتطبيق"""
    try:
        print("\n🔍 إنشاء نسخة تشخيص...")
        
        debug_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة تشخيص لاختبار عرض الأزرار
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

class DebugApp:
    def __init__(self, root):
        self.root = root
        self.setup_debug_ui()
    
    def setup_debug_ui(self):
        """إعداد واجهة التشخيص"""
        self.root.title("تشخيص عرض الأزرار")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f8ff')
        
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(main_frame,
                              text="🔍 تشخيص عرض الأزرار",
                              font=("Arial", 18, "bold"),
                              bg='#f8f9fa',
                              fg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # معلومات التشخيص
        info_text = tk.Text(main_frame, height=8, width=80, font=("Arial", 10))
        info_text.pack(pady=(0, 20))
        
        info_content = """
🔧 معلومات التشخيص:
✅ تم تحميل tkinter بنجاح
✅ تم إنشاء النافذة الرئيسية
✅ تم إعداد الإطار الرئيسي
✅ جاري إنشاء الأزرار...

إذا ظهرت الأزرار أدناه، فالمشكلة في التطبيق الأصلي.
إذا لم تظهر، فالمشكلة في النظام أو المكتبات.
        """
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg='#f8f9fa')
        buttons_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الأزرار
        self.create_test_buttons(buttons_frame)
    
    def create_test_buttons(self, parent_frame):
        """إنشاء أزرار الاختبار"""
        buttons_config = [
            {'text': '📁 إضافة حساب جديد', 'color': '#3498db'},
            {'text': '📝 إضافة مستند', 'color': '#27ae60'},
            {'text': '⚙️ إدارة الحسابات', 'color': '#e74c3c'},
            {'text': '💰 إضافة حساب مقبوضات', 'color': '#16a085'},
            {'text': '📄 إضافة مستند مقبوضات', 'color': '#2ecc71'},
            {'text': '⚙️ إدارة حسابات المقبوضات', 'color': '#8e44ad'},
            {'text': '🔍 بحث في الحسابات', 'color': '#f39c12'},
            {'text': '📊 نافذة تقارير الأرصدة', 'color': '#e67e22'},
            {'text': '👥 إدارة المستخدمين', 'color': '#34495e'},
            {'text': '🚪 خروج من النظام', 'color': '#95a5a6'}
        ]
        
        row = 0
        col = 0
        max_cols = 3
        
        for i, btn_config in enumerate(buttons_config):
            btn = tk.Button(parent_frame,
                           text=btn_config['text'],
                           command=lambda t=btn_config['text']: self.test_button(t),
                           font=("Arial", 12, "bold"),
                           bg=btn_config['color'],
                           fg='white',
                           relief='flat',
                           padx=20,
                           pady=15,
                           cursor='hand2',
                           width=25)
            
            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
        
        # تعيين وزن الأعمدة
        for i in range(max_cols):
            parent_frame.grid_columnconfigure(i, weight=1)
    
    def test_button(self, button_text):
        """اختبار الزر"""
        messagebox.showinfo("اختبار الزر", f"تم النقر على: {button_text}")

def main():
    """تشغيل تطبيق التشخيص"""
    try:
        print("🚀 بدء تطبيق التشخيص...")
        
        root = tk.Tk()
        app = DebugApp(root)
        
        print("✅ تم إنشاء تطبيق التشخيص")
        print("🖱️ انقر على الأزرار لاختبارها")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق التشخيص: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''
        
        with open('debug_buttons.py', 'w', encoding='utf-8') as f:
            f.write(debug_content)
        
        print("✅ تم إنشاء ملف debug_buttons.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نسخة التشخيص: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🔧 بدء إصلاح مشكلة عدم ظهور الأزرار")
    print("=" * 60)
    
    success_count = 0
    total_fixes = 2
    
    # إصلاح الأزرار
    if fix_buttons_display():
        success_count += 1
        print("✅ تم إصلاح دالة الأزرار")
    else:
        print("❌ فشل في إصلاح دالة الأزرار")
    
    # إنشاء نسخة التشخيص
    if create_debug_version():
        success_count += 1
        print("✅ تم إنشاء نسخة التشخيص")
    else:
        print("❌ فشل في إنشاء نسخة التشخيص")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الإصلاح: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print("🎉 تم إصلاح المشكلة بنجاح!")
        print("\n📝 الخطوات التالية:")
        print("1. تشغيل debug_buttons.py لاختبار الأزرار")
        print("2. تشغيل التطبيق الأصلي للتحقق من الإصلاح")
        print("3. إذا استمرت المشكلة، فحص صلاحيات المستخدم")
        
        print("\n🔧 أوامر الاختبار:")
        print("python debug_buttons.py")
        print("python test_buttons_display.py")
        print("python app.py")
        
        return True
    else:
        print("❌ فشل في إصلاح المشكلة")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
'''
