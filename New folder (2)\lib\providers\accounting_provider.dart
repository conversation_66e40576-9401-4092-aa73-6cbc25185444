import 'package:flutter/material.dart';
import '../models/account.dart';
import '../services/api_service.dart';

class AccountingProvider with ChangeNotifier {
  List<Account> _accounts = [];
  AccountDetails? _selectedAccountDetails;
  List<Document> _searchResults = [];
  bool _isLoading = false;
  String? _error;

  List<Account> get accounts => _accounts;
  AccountDetails? get selectedAccountDetails => _selectedAccountDetails;
  List<Document> get searchResults => _searchResults;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل قائمة الحسابات
  Future<void> loadAccounts() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _accounts = await ApiService.getAccounts();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل تفاصيل حساب
  Future<void> loadAccountDetails(int accountId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _selectedAccountDetails = await ApiService.getAccountDetails(accountId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // إضافة مستند جديد
  Future<String?> addDocument({
    required int accountNumber,
    required String accountName,
    required double amount,
    required String documentNumber,
    required String paymentNumber,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      final result = await ApiService.addDocument(
        accountNumber: accountNumber,
        accountName: accountName,
        amount: amount,
        documentNumber: documentNumber,
        paymentNumber: paymentNumber,
      );

      _isLoading = false;
      
      if (result['success']) {
        // إعادة تحميل الحسابات لتحديث الأرصدة
        await loadAccounts();
        notifyListeners();
        return null; // نجح
      } else {
        notifyListeners();
        return result['message'];
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return 'خطأ في إضافة المستند: $e';
    }
  }

  // إضافة حساب جديد
  Future<String?> addAccount({
    required int accountNumber,
    required String accountName,
    required double openingBalance,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      final result = await ApiService.addAccount(
        accountNumber: accountNumber,
        accountName: accountName,
        openingBalance: openingBalance,
      );

      _isLoading = false;
      
      if (result['success']) {
        // إعادة تحميل الحسابات
        await loadAccounts();
        notifyListeners();
        return null; // نجح
      } else {
        notifyListeners();
        return result['message'];
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return 'خطأ في إضافة الحساب: $e';
    }
  }

  // البحث في المستندات
  Future<void> searchDocuments({
    required String query,
    String searchType = 'all',
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _searchResults = await ApiService.searchDocuments(
        query: query,
        searchType: searchType,
      );
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // مسح نتائج البحث
  void clearSearchResults() {
    _searchResults = [];
    notifyListeners();
  }

  // مسح الأخطاء
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
