================================================================
دليل التشغيل الصامت للنظام المحاسبي
Silent Operation Guide for Accounting System
================================================================

🔇 نظرة عامة:
تم إنشاء نظام تشغيل صامت يعمل في الخلفية بدون إظهار أي نوافذ أو رسائل.

================================================================
📁 الملفات المتاحة للتشغيل الصامت:
================================================================

1. launcher_silent_fixed.py     - المشغل الصامت المحسن (يعمل)
2. تشغيل_صامت_محسن.bat         - ملف تشغيل صامت محسن
3. تشغيل_صامت_محسن.vbs         - تشغيل صامت تام (مخفي تماماً)
4. app_service.py               - نسخة خدمة من التطبيق
5. install_dependencies_silent.py - مثبت صامت للمتطلبات

================================================================
🚀 طرق التشغيل الصامت (مرتبة حسب الأفضلية):
================================================================

الطريقة الأولى (الأفضل):
-----------------------
تشغيل_صامت_محسن.vbs

المميزات:
✅ مخفي تماماً (لا توجد نوافذ)
✅ لا يظهر في شريط المهام
✅ يعمل في الخلفية بصمت تام
✅ يبحث عن Python تلقائياً

الطريقة الثانية:
-----------------
python launcher_silent_fixed.py

المميزات:
✅ مشغل محسن وموثوق
✅ يثبت المتطلبات تلقائياً
✅ يراقب النظام ويعيد التشغيل عند الحاجة
✅ ينشئ سجلات مفصلة

الطريقة الثالثة:
-----------------
تشغيل_صامت_محسن.bat

المميزات:
✅ يبحث عن Python
✅ تشغيل في الخلفية
✅ سهل الاستخدام

================================================================
🔧 للتشغيل على جهاز آخر بدون مكتبات:
================================================================

الملفات المطلوبة للنسخ:
------------------------
✅ تشغيل_صامت_محسن.vbs          (الملف الرئيسي)
✅ launcher_silent_fixed.py       (المشغل)
✅ app.py                        (التطبيق الرئيسي)
✅ excel_manager.py               (مدير Excel)
✅ user_manager.py                (مدير المستخدمين)
✅ document_window.py             (نافذة المستندات)
✅ search_window.py               (نافذة البحث)
✅ manage_accounts.py             (إدارة الحسابات)
✅ accounting_system.xlsx         (ملف البيانات)
✅ users.json                    (بيانات المستخدمين)

خطوات التشغيل:
---------------
1. انسخ جميع الملفات المطلوبة للجهاز الآخر
2. شغل الملف: تشغيل_صامت_محسن.vbs
3. النظام سيعمل في الخلفية بصمت

================================================================
📊 مراقبة النظام الصامت:
================================================================

فحص حالة النظام:
-----------------
فحص_حالة_النظام_الصامت.bat

إيقاف النظام:
--------------
إيقاف_النظام_الصامت.bat

ملفات السجلات:
---------------
- accounting_silent.log          (سجل النظام الصامت)
- service_status.txt            (حالة الخدمة)

================================================================
🔍 استكشاف الأخطاء:
================================================================

المشكلة: launcher_silent.py لا يعمل
الحل: استخدم launcher_silent_fixed.py بدلاً منه

المشكلة: لا توجد نوافذ ولا أعرف إذا كان يعمل
الحل: افحص ملف accounting_silent.log

المشكلة: Python غير موجود
الحل: استخدم تشغيل_صامت_محسن.vbs (يبحث عن Python تلقائياً)

المشكلة: مكتبات مفقودة
الحل: النظام يثبت المكتبات تلقائياً

================================================================
📝 ملاحظات مهمة:
================================================================

1. النظام الصامت لا يظهر أي نوافذ أو رسائل
2. للتأكد من عمل النظام، افحص ملفات السجلات
3. النظام يراقب نفسه ويعيد التشغيل عند الحاجة
4. يمكن إيقاف النظام باستخدام ملف الإيقاف المخصص

================================================================
🎯 التوصية النهائية:
================================================================

للتشغيل على جهاز آخر بدون مكتبات:

1. انسخ جميع ملفات المشروع
2. شغل: تشغيل_صامت_محسن.vbs
3. النظام سيعمل في الخلفية بصمت تام

هذا هو الحل الأمثل والأكثر موثوقية!

================================================================
تاريخ الإنشاء: 2025-01-XX
آخر تحديث: 2025-01-XX
================================================================
