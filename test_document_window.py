#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات نافذة إضافة المستندات
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_document_window():
    """اختبار نافذة إضافة المستندات"""
    try:
        print("🧪 اختبار نافذة إضافة المستندات...")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # محاكاة التطبيق الرئيسي
        class MockApp:
            def __init__(self):
                from excel_manager import ExcelManager
                self.excel = ExcelManager()
                # إنشاء حساب تجريبي
                self.excel.create_account_sheet("TEST", "حساب اختبار", 1000)
        
        app = MockApp()
        
        # إنشاء نافذة إضافة المستندات
        from document_window import AddDocumentWindow
        doc_window = AddDocumentWindow(app)
        
        print("✅ تم إنشاء نافذة إضافة المستندات")
        
        # اختبار الخيارات المتاحة
        print("📋 الخيارات المتاحة:")
        print("   - إغلاق تلقائي بعد الإضافة")
        print("   - البقاء مفتوحاً لإضافة مستندات أخرى")
        
        print("🎮 الأزرار المتاحة:")
        print("   - إضافة المستند")
        print("   - إضافة وإغلاق")
        print("   - إغلاق")
        
        print("⌨️ اختصارات لوحة المفاتيح:")
        print("   - Enter: التنقل أو الإضافة")
        print("   - Ctrl+Enter: إضافة وإغلاق")
        print("   - Escape: إغلاق")
        print("   - F1: المساعدة")
        
        # عرض النافذة للمستخدم
        print("\n🖥️ عرض النافذة للاختبار...")
        print("   يمكنك الآن اختبار النافذة يدوياً")
        print("   أغلق النافذة عند الانتهاء من الاختبار")
        
        # تشغيل النافذة
        root.deiconify()  # إظهار النافذة الرئيسية
        root.mainloop()
        
        print("✅ تم إغلاق النافذة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_preferences():
    """اختبار حفظ وتحميل التفضيلات"""
    try:
        print("\n🧪 اختبار حفظ وتحميل التفضيلات...")
        
        # حذف ملف التفضيلات إن وجد
        prefs_file = "document_window_prefs.txt"
        if os.path.exists(prefs_file):
            os.remove(prefs_file)
            print("🗑️ تم حذف ملف التفضيلات السابق")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        class MockApp:
            def __init__(self):
                from excel_manager import ExcelManager
                self.excel = ExcelManager()
                self.excel.create_account_sheet("PREF", "اختبار تفضيلات", 500)
        
        app = MockApp()
        
        # اختبار التفضيل الافتراضي
        from document_window import AddDocumentWindow
        doc_window1 = AddDocumentWindow(app)
        
        if doc_window1.close_option.get() == "auto":
            print("✅ التفضيل الافتراضي صحيح: إغلاق تلقائي")
        else:
            print("❌ التفضيل الافتراضي خاطئ")
            return False
        
        # تغيير التفضيل وحفظه
        doc_window1.close_option.set("stay")
        doc_window1.save_preferences()
        doc_window1.destroy()
        
        print("💾 تم حفظ التفضيل الجديد: البقاء مفتوحاً")
        
        # إنشاء نافذة جديدة واختبار تحميل التفضيل
        doc_window2 = AddDocumentWindow(app)
        
        if doc_window2.close_option.get() == "stay":
            print("✅ تم تحميل التفضيل المحفوظ بنجاح")
        else:
            print("❌ فشل في تحميل التفضيل المحفوظ")
            return False
        
        doc_window2.destroy()
        root.destroy()
        
        # تنظيف ملف التفضيلات
        if os.path.exists(prefs_file):
            os.remove(prefs_file)
            print("🧹 تم تنظيف ملف التفضيلات")
        
        print("✅ اختبار التفضيلات نجح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التفضيلات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_keyboard_shortcuts():
    """اختبار اختصارات لوحة المفاتيح"""
    try:
        print("\n🧪 اختبار اختصارات لوحة المفاتيح...")
        
        root = tk.Tk()
        root.withdraw()
        
        class MockApp:
            def __init__(self):
                from excel_manager import ExcelManager
                self.excel = ExcelManager()
                self.excel.create_account_sheet("KEY", "اختبار مفاتيح", 750)
        
        app = MockApp()
        
        from document_window import AddDocumentWindow
        doc_window = AddDocumentWindow(app)
        
        # اختبار وجود الاختصارات
        bindings = doc_window.bind()
        
        shortcuts_found = 0
        expected_shortcuts = ['<Return>', '<Control-Return>', '<Escape>', '<F1>']
        
        for shortcut in expected_shortcuts:
            if shortcut in str(bindings):
                shortcuts_found += 1
                print(f"✅ تم العثور على اختصار: {shortcut}")
            else:
                print(f"⚠️ لم يتم العثور على اختصار: {shortcut}")
        
        doc_window.destroy()
        root.destroy()
        
        if shortcuts_found >= 2:  # على الأقل اختصارين
            print("✅ اختبار اختصارات لوحة المفاتيح نجح")
            return True
        else:
            print("❌ فشل في اختبار اختصارات لوحة المفاتيح")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاختصارات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_help_function():
    """اختبار دالة المساعدة"""
    try:
        print("\n🧪 اختبار دالة المساعدة...")
        
        root = tk.Tk()
        root.withdraw()
        
        class MockApp:
            def __init__(self):
                from excel_manager import ExcelManager
                self.excel = ExcelManager()
                self.excel.create_account_sheet("HELP", "اختبار مساعدة", 250)
        
        app = MockApp()
        
        from document_window import AddDocumentWindow
        doc_window = AddDocumentWindow(app)
        
        # اختبار وجود دالة المساعدة
        if hasattr(doc_window, 'show_help'):
            print("✅ دالة المساعدة موجودة")
            
            # يمكن اختبار استدعاء الدالة (لكن سيظهر messagebox)
            # doc_window.show_help()
            
            doc_window.destroy()
            root.destroy()
            return True
        else:
            print("❌ دالة المساعدة غير موجودة")
            doc_window.destroy()
            root.destroy()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة المساعدة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار تحسينات نافذة إضافة المستندات")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # اختبار التفضيلات
    if test_preferences():
        success_count += 1
    
    # اختبار اختصارات لوحة المفاتيح
    if test_keyboard_shortcuts():
        success_count += 1
    
    # اختبار دالة المساعدة
    if test_help_function():
        success_count += 1
    
    # اختبار النافذة (تفاعلي)
    print(f"\n🖥️ اختبار تفاعلي للنافذة...")
    print("هذا الاختبار يتطلب تفاعل المستخدم")
    user_input = input("هل تريد اختبار النافذة تفاعلياً؟ (y/n): ")
    
    if user_input.lower() in ['y', 'yes', 'نعم']:
        if test_document_window():
            success_count += 1
    else:
        print("تم تخطي الاختبار التفاعلي")
        success_count += 1  # نعتبره نجح
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات نافذة إضافة المستندات!")
        print("\n✅ المميزات الجديدة:")
        print("   - خيارات إغلاق متقدمة")
        print("   - اختصارات لوحة مفاتيح")
        print("   - حفظ التفضيلات")
        print("   - دالة مساعدة")
        print("   - أزرار متعددة للتحكم")
        return True
    else:
        print("❌ فشل في بعض اختبارات النافذة")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
