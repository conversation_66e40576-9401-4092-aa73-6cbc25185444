#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء ملف تنفيذي مبسط للنظام المحاسبي
"""

import os
import sys
import shutil
import subprocess

def create_standalone_executable():
    """إنشاء ملف تنفيذي مستقل"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    print("🔨 إنشاء ملف تنفيذي مستقل...")
    
    # إنشاء مجلد التوزيع
    dist_dir = os.path.join(current_dir, "dist_standalone")
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)
    
    # نسخ الملفات الأساسية
    essential_files = [
        "launcher.py",
        "app.py", 
        "excel_manager.py",
        "document_window.py",
        "search_window.py", 
        "manage_accounts.py",
        "requirements.txt",
        "accounting_system.xlsx"
    ]
    
    for file in essential_files:
        src = os.path.join(current_dir, file)
        dst = os.path.join(dist_dir, file)
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ نسخ {file}")
    
    # إنشاء ملف تشغيل
    launcher_bat = os.path.join(dist_dir, "تشغيل_النظام.bat")
    with open(launcher_bat, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 >nul
title نظام إدارة المستندات المحاسبية

echo ========================================
echo    نظام إدارة المستندات المحاسبية
echo        وزارة الصحة الأردنية  
echo ========================================
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM تثبيت المكتبات إذا لزم الأمر
python -c "import openpyxl" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 تثبيت المكتبات المطلوبة...
    python -m pip install openpyxl ttkthemes
)

echo 🚀 تشغيل النظام...
python launcher.py

pause
""")
    
    # إنشاء ملف README
    readme_file = os.path.join(dist_dir, "اقرأني.txt")
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write("""نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية

طريقة التشغيل:
1. تأكد من تثبيت Python 3.7 أو أحدث
2. انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
3. انتظر حتى يتم تثبيت المكتبات المطلوبة (إن لم تكن مثبتة)
4. سيتم تشغيل النظام تلقائياً

المتطلبات:
- Python 3.7+
- اتصال بالإنترنت (لتثبيت المكتبات في المرة الأولى)

للدعم الفني:
تواصل مع فريق تطوير الأنظمة المحاسبية
""")
    
    print(f"✅ تم إنشاء النظام المستقل في: {dist_dir}")
    print("📁 يمكنك نسخ مجلد dist_standalone إلى أي جهاز آخر")
    return True

def try_pyinstaller_build():
    """محاولة إنشاء ملف تنفيذي باستخدام PyInstaller"""
    try:
        # التحقق من وجود PyInstaller
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            return False
        
        print("✅ PyInstaller متوفر")
        
        # إنشاء ملف تنفيذي
        cmd = [
            'pyinstaller',
            '--onefile',
            '--windowed', 
            '--name=نظام_المحاسبة',
            'launcher.py'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        if result.returncode == 0:
            print("✅ تم إنشاء ملف تنفيذي بـ PyInstaller")
            return True
        else:
            print(f"❌ فشل PyInstaller: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في PyInstaller: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بناء النظام المحاسبي")
    print("=" * 40)
    
    # محاولة PyInstaller أولاً
    if try_pyinstaller_build():
        print("🎉 تم إنشاء ملف تنفيذي بـ PyInstaller")
    
    # إنشاء نسخة مستقلة دائماً
    if create_standalone_executable():
        print("🎉 تم إنشاء النظام المستقل")
    
    print("\n✅ انتهى البناء")
    print("يمكنك استخدام:")
    print("1. dist/نظام_المحاسبة.exe (إذا نجح PyInstaller)")
    print("2. dist_standalone/تشغيل_النظام.bat (النسخة المستقلة)")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للإغلاق...")
