#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت المتطلبات التلقائي
Automatic Dependencies Installer
نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية
"""

import subprocess
import sys
import os
import importlib
import json
from datetime import datetime

class DependencyInstaller:
    def __init__(self):
        self.required_packages = [
            # المكتبات الأساسية المطلوبة
            'openpyxl>=3.1.0',
            'ttkthemes>=3.2.2',
            'Pillow>=10.0.0',
            'pyinstaller>=5.13.0',
            'setuptools>=68.0.0',
            'wheel>=0.41.0',
            'cryptography>=41.0.0',
            'python-dateutil>=2.8.2',
            'jsonschema>=4.17.0',
            'arabic-reshaper>=3.0.0',
            'python-bidi>=0.4.2',
            'requests>=2.31.0',
            'urllib3>=2.0.0',
            'certifi>=2023.7.22',
            'charset-normalizer>=3.2.0',
            'idna>=3.4',
            'psutil>=5.9.0',
            'packaging>=23.1',
            'six>=1.16.0'
        ]

        self.optional_packages = [
            # مكتبات اختيارية للتحسين
            'zstandard>=0.21.0'
        ]

        self.builtin_modules = [
            'tkinter', 'os', 'sys', 'datetime', 'threading',
            'locale', 'tempfile', 'subprocess', 'webbrowser',
            'json', 'traceback', 'logging'
        ]

        self.installation_log = []

    def print_header(self):
        """طباعة رأس البرنامج"""
        print("\n" + "="*70)
        print("🔧 مثبت المتطلبات التلقائي")
        print("   Automatic Dependencies Installer")
        print("🏥 نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية")
        print("="*70)

    def check_python_version(self):
        """فحص إصدار Python"""
        print("\n🐍 فحص إصدار Python...")
        version = sys.version_info
        print(f"   الإصدار الحالي: Python {version.major}.{version.minor}.{version.micro}")

        if version.major < 3 or (version.major == 3 and version.minor < 7):
            print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
            print("   يرجى تحديث Python من: https://www.python.org/downloads/")
            return False

        print("✅ إصدار Python مناسب")
        return True

    def check_pip(self):
        """فحص وجود pip"""
        print("\n📦 فحص مدير الحزم pip...")
        try:
            import pip
            print("✅ pip متوفر")

            # محاولة تحديث pip
            print("🔄 تحديث pip...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'],
                         check=True, capture_output=True)
            print("✅ تم تحديث pip")
            return True

        except ImportError:
            print("❌ pip غير متوفر")
            print("💡 يرجى تثبيت pip أولاً")
            return False
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم تحديث pip - {str(e)}")
            return True

    def check_builtin_modules(self):
        """فحص المكتبات المدمجة"""
        print("\n🔍 فحص المكتبات المدمجة...")
        missing_builtin = []

        for module in self.builtin_modules:
            try:
                importlib.import_module(module)
                print(f"✅ {module}")
            except ImportError:
                print(f"❌ {module} - غير متوفر")
                missing_builtin.append(module)

        if missing_builtin:
            print(f"\n⚠️ مكتبات مدمجة مفقودة: {', '.join(missing_builtin)}")
            print("💡 قد تحتاج لإعادة تثبيت Python")
            return False

        print("✅ جميع المكتبات المدمجة متوفرة")
        return True

    def install_package(self, package):
        """تثبيت حزمة واحدة"""
        package_name = package.split('>=')[0].split('==')[0]

        try:
            # فحص إذا كانت الحزمة مثبتة بالفعل
            importlib.import_module(package_name.replace('-', '_'))
            print(f"✅ {package_name} - مثبت بالفعل")
            self.installation_log.append(f"✅ {package_name} - موجود مسبقاً")
            return True

        except ImportError:
            print(f"📦 تثبيت {package_name}...")

            try:
                # تثبيت الحزمة
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package, '--upgrade'
                ], capture_output=True, text=True, check=True)

                print(f"✅ تم تثبيت {package_name} بنجاح")
                self.installation_log.append(f"✅ {package_name} - تم التثبيت")
                return True

            except subprocess.CalledProcessError as e:
                print(f"❌ فشل تثبيت {package_name}")
                print(f"   الخطأ: {e.stderr}")
                self.installation_log.append(f"❌ {package_name} - فشل التثبيت: {e.stderr}")
                return False

    def install_required_packages(self):
        """تثبيت الحزم المطلوبة"""
        print("\n📋 تثبيت الحزم المطلوبة...")
        failed_packages = []

        for package in self.required_packages:
            if not self.install_package(package):
                failed_packages.append(package)

        return len(failed_packages) == 0, failed_packages

    def install_optional_packages(self):
        """تثبيت الحزم الاختيارية"""
        print("\n🔧 تثبيت الحزم الاختيارية...")

        for package in self.optional_packages:
            # تجاهل الأخطاء للحزم الاختيارية
            try:
                self.install_package(package)
            except Exception as e:
                print(f"⚠️ تخطي {package} - {str(e)}")

    def verify_installation(self):
        """التحقق من التثبيت"""
        print("\n🔍 التحقق من التثبيت...")

        # فحص الحزم الأساسية
        essential_modules = ['openpyxl', 'tkinter']
        all_good = True

        for module in essential_modules:
            try:
                importlib.import_module(module)
                print(f"✅ {module} - يعمل بشكل صحيح")
            except ImportError:
                print(f"❌ {module} - لا يعمل")
                all_good = False

        return all_good

    def save_installation_log(self):
        """حفظ سجل التثبيت"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'platform': sys.platform,
            'installation_log': self.installation_log
        }

        try:
            with open('installation_log.json', 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            print("📄 تم حفظ سجل التثبيت في: installation_log.json")
        except Exception as e:
            print(f"⚠️ لم يتم حفظ السجل: {str(e)}")

    def run(self):
        """تشغيل عملية التثبيت الكاملة"""
        self.print_header()

        # فحص Python
        if not self.check_python_version():
            return False

        # فحص pip
        if not self.check_pip():
            return False

        # فحص المكتبات المدمجة
        if not self.check_builtin_modules():
            return False

        # تثبيت الحزم المطلوبة
        success, failed = self.install_required_packages()

        if not success:
            print(f"\n❌ فشل تثبيت بعض الحزم المطلوبة: {', '.join(failed)}")
            print("💡 يرجى تثبيتها يدوياً:")
            for package in failed:
                print(f"   pip install {package}")

        # تثبيت الحزم الاختيارية
        self.install_optional_packages()

        # التحقق من التثبيت
        verification_success = self.verify_installation()

        # حفظ السجل
        self.save_installation_log()

        # النتيجة النهائية
        print("\n" + "="*70)
        if success and verification_success:
            print("🎉 تم تثبيت جميع المتطلبات بنجاح!")
            print("✅ النظام جاهز للتشغيل")
            print("\n🚀 لتشغيل النظام:")
            print("   python launcher.py")
            print("   أو")
            print("   python app.py")
        else:
            print("⚠️ تم التثبيت مع بعض المشاكل")
            print("💡 راجع الرسائل أعلاه لحل المشاكل")

        print("="*70)
        return success and verification_success

def main():
    """الدالة الرئيسية"""
    installer = DependencyInstaller()
    success = installer.run()

    input("\nاضغط Enter للإغلاق...")
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
