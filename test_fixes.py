#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتأكد من إصلاح الأخطاء
"""

import sys
import os

def test_imports():
    """اختبار استيراد الملفات"""
    try:
        print("🧪 اختبار استيراد الملفات...")
        
        # اختبار استيراد app.py
        print("1️⃣ اختبار استيراد app.py...")
        from app import AccountingApp, AddAccountDialog, EditAccountDialog
        print("   ✅ تم استيراد app.py بنجاح")
        
        # اختبار استيراد excel_manager.py
        print("2️⃣ اختبار استيراد excel_manager.py...")
        from excel_manager import ExcelManager
        print("   ✅ تم استيراد excel_manager.py بنجاح")
        
        # اختبار استيراد document_window.py
        print("3️⃣ اختبار استيراد document_window.py...")
        from document_window import AddDocumentWindow
        print("   ✅ تم استيراد document_window.py بنجاح")
        
        # اختبار استيراد manage_accounts.py
        print("4️⃣ اختبار استيراد manage_accounts.py...")
        from manage_accounts import ManageAccountsDialog, EnhancedAccountDetailsWindow, EditDocumentDialog
        print("   ✅ تم استيراد manage_accounts.py بنجاح")
        
        print("\n🎉 تم اجتياز جميع اختبارات الاستيراد بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_manager_functions():
    """اختبار دوال excel_manager الجديدة"""
    try:
        print("\n🧪 اختبار دوال excel_manager الجديدة...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # اختبار وجود الدوال الجديدة
        print("1️⃣ فحص وجود الدوال الجديدة...")
        
        if hasattr(excel, 'check_document_number_globally'):
            print("   ✅ check_document_number_globally موجودة")
        else:
            print("   ❌ check_document_number_globally غير موجودة")
            return False
        
        if hasattr(excel, 'check_payment_number_in_account'):
            print("   ✅ check_payment_number_in_account موجودة")
        else:
            print("   ❌ check_payment_number_in_account غير موجودة")
            return False
        
        if hasattr(excel, 'get_all_accounts'):
            print("   ✅ get_all_accounts موجودة")
        else:
            print("   ❌ get_all_accounts غير موجودة")
            return False
        
        if hasattr(excel, 'get_account_documents'):
            print("   ✅ get_account_documents موجودة")
        else:
            print("   ❌ get_account_documents غير موجودة")
            return False
        
        print("\n🎉 جميع الدوال الجديدة موجودة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال excel_manager: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_app_class():
    """اختبار كلاس AccountingApp"""
    try:
        print("\n🧪 اختبار كلاس AccountingApp...")
        
        import tkinter as tk
        from app import AccountingApp
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # فحص وجود الدوال المطلوبة
        print("1️⃣ فحص وجود الدوال في AccountingApp...")
        
        # إنشاء كائن AccountingApp (بدون تشغيل الواجهة)
        app = AccountingApp.__new__(AccountingApp)
        app.root = root
        
        # فحص وجود start_auto_refresh
        if hasattr(AccountingApp, 'start_auto_refresh'):
            print("   ✅ start_auto_refresh موجودة")
        else:
            print("   ❌ start_auto_refresh غير موجودة")
            return False
        
        # تنظيف
        root.destroy()
        
        print("\n🎉 كلاس AccountingApp يعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار AccountingApp: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبارات إصلاح الأخطاء")
    print("=" * 60)
    
    # تشغيل الاختبارات
    test1 = test_imports()
    test2 = test_excel_manager_functions()
    test3 = test_app_class()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if test1 and test2 and test3:
        print("🎉 تم إصلاح جميع الأخطاء بنجاح!")
        print("✅ النظام جاهز للتشغيل")
    else:
        print("❌ لا تزال هناك أخطاء تحتاج إلى إصلاح")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
