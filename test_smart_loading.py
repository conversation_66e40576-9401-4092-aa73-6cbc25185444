#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحميل الذكي للملفات
"""

import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_smart_loading():
    """اختبار التحميل الذكي للملفات"""
    print("🧪 اختبار التحميل الذكي للملفات")
    print("=" * 50)
    
    try:
        from excel_manager import ExcelManager
        
        # السيناريو 1: لا يوجد ملف - إنشاء ملف جديد
        print("\n📝 السيناريو 1: لا يوجد ملف")
        print("-" * 30)
        
        # حذف الملف إذا كان موجوداً
        if os.path.exists("accounting_system.xlsx"):
            os.remove("accounting_system.xlsx")
            print("🗑️ تم حذف الملف السابق")
        
        excel1 = ExcelManager()
        if excel1.workbook:
            print("✅ تم إنشاء ملف جديد بنجاح")
            print(f"📋 الصفحات: {excel1.workbook.sheetnames}")
        else:
            print("❌ فشل في إنشاء ملف جديد")
            return False
        
        # السيناريو 2: إضافة بيانات وحفظ
        print("\n💾 السيناريو 2: إضافة بيانات وحفظ")
        print("-" * 30)
        
        # إنشاء حساب تجريبي
        result = excel1.create_account_sheet("TEST001", "حساب اختبار", 5000.0)
        print(f"إنشاء الحساب: {'نجح' if result else 'فشل'}")
        
        if result:
            # إضافة مستندات
            for i in range(3):
                doc_result = excel1.add_document("TEST001-حساب اختبار", 1000.0 * (i+1), f"DOC{i+1:03d}", f"PAY{i+1:03d}")
                print(f"   مستند {i+1}: {'نجح' if doc_result else 'فشل'}")
            
            # حفظ الملف
            save_result = excel1.save_workbook()
            print(f"حفظ الملف: {'نجح' if save_result else 'فشل'}")
        
        # السيناريو 3: إعادة تحميل الملف الموجود
        print("\n🔄 السيناريو 3: إعادة تحميل الملف الموجود")
        print("-" * 30)
        
        excel2 = ExcelManager()
        if excel2.workbook:
            print("✅ تم تحميل الملف الموجود بنجاح")
            print(f"📋 الصفحات المحملة: {excel2.workbook.sheetnames}")
            
            # التحقق من وجود الحساب
            if "TEST001-حساب اختبار" in excel2.workbook.sheetnames:
                print("✅ تم استرجاع الحساب المحفوظ")
                
                # فحص المستندات
                ws = excel2.workbook["TEST001-حساب اختبار"]
                documents_found = 0
                for row in range(10, 33):
                    for col in range(1, 19, 3):
                        cell_value = ws.cell(row=row, column=col).value
                        if cell_value and isinstance(cell_value, (int, float)) and cell_value > 0:
                            documents_found += 1
                
                print(f"📄 المستندات المسترجعة: {documents_found}")
            else:
                print("❌ لم يتم العثور على الحساب المحفوظ")
                return False
        else:
            print("❌ فشل في تحميل الملف الموجود")
            return False
        
        # السيناريو 4: اختبار ملف تالف
        print("\n🔧 السيناريو 4: اختبار ملف تالف")
        print("-" * 30)
        
        # إنشاء ملف تالف
        with open("accounting_system.xlsx", "w") as f:
            f.write("ملف تالف")
        
        print("📝 تم إنشاء ملف تالف للاختبار")
        
        excel3 = ExcelManager()
        if excel3.workbook:
            print("✅ تم التعامل مع الملف التالف وإنشاء ملف جديد")
            
            # التحقق من وجود نسخة احتياطية
            backup_files = [f for f in os.listdir('.') if f.startswith('accounting_system.xlsx.backup_')]
            if backup_files:
                print(f"💾 تم إنشاء نسخة احتياطية: {backup_files[-1]}")
            else:
                print("⚠️ لم يتم إنشاء نسخة احتياطية")
        else:
            print("❌ فشل في التعامل مع الملف التالف")
            return False
        
        # السيناريو 5: اختبار ملف فارغ
        print("\n📄 السيناريو 5: اختبار ملف فارغ")
        print("-" * 30)
        
        # إنشاء ملف فارغ
        with open("accounting_system.xlsx", "w") as f:
            pass  # ملف فارغ
        
        print("📝 تم إنشاء ملف فارغ للاختبار")
        
        excel4 = ExcelManager()
        if excel4.workbook:
            print("✅ تم التعامل مع الملف الفارغ وإنشاء ملف جديد")
        else:
            print("❌ فشل في التعامل مع الملف الفارغ")
            return False
        
        print("\n🎉 جميع السيناريوهات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # تنظيف الملفات
        print("\n🧹 تنظيف ملفات الاختبار...")
        try:
            if os.path.exists("accounting_system.xlsx"):
                os.remove("accounting_system.xlsx")
                print("🗑️ تم حذف ملف الاختبار")
            
            # حذف النسخ الاحتياطية
            backup_files = [f for f in os.listdir('.') if f.startswith('accounting_system.xlsx.backup_')]
            for backup_file in backup_files:
                os.remove(backup_file)
                print(f"🗑️ تم حذف النسخة الاحتياطية: {backup_file}")
                
        except Exception as cleanup_error:
            print(f"⚠️ خطأ في التنظيف: {str(cleanup_error)}")

def test_file_scenarios():
    """اختبار سيناريوهات مختلفة للملفات"""
    print("\n🔍 اختبار سيناريوهات إضافية")
    print("=" * 50)
    
    try:
        from excel_manager import ExcelManager
        
        # اختبار 1: ملف بحجم صغير جداً
        print("\n📏 اختبار ملف بحجم صغير")
        print("-" * 30)
        
        with open("accounting_system.xlsx", "w") as f:
            f.write("x")  # ملف بحجم 1 بايت
        
        excel = ExcelManager()
        if excel.workbook:
            print("✅ تم التعامل مع الملف الصغير")
        
        # اختبار 2: ملف بصيغة خاطئة
        print("\n📄 اختبار ملف بصيغة خاطئة")
        print("-" * 30)
        
        with open("accounting_system.xlsx", "w") as f:
            f.write("هذا ليس ملف Excel صحيح")
        
        excel = ExcelManager()
        if excel.workbook:
            print("✅ تم التعامل مع الملف ذو الصيغة الخاطئة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار السيناريوهات: {str(e)}")
        return False
    
    finally:
        # تنظيف
        try:
            if os.path.exists("accounting_system.xlsx"):
                os.remove("accounting_system.xlsx")
        except:
            pass

if __name__ == "__main__":
    print("🚀 بدء اختبار التحميل الذكي للملفات")
    print("=" * 60)
    
    # تشغيل الاختبارات
    test1_result = test_smart_loading()
    test2_result = test_file_scenarios()
    
    print("\n" + "=" * 60)
    print(f"🏁 نتائج الاختبارات:")
    print(f"   الاختبار الأساسي: {'✅ نجح' if test1_result else '❌ فشل'}")
    print(f"   اختبار السيناريوهات: {'✅ نجح' if test2_result else '❌ فشل'}")
    
    if test1_result and test2_result:
        print("\n🎉 جميع الاختبارات نجحت! التحميل الذكي يعمل بشكل صحيح.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الكود.")
    
    input("\nاضغط Enter للإغلاق...")
