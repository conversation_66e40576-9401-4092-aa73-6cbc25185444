@echo off
chcp 65001 >nul
title Accounting System - Ministry of Health

echo ========================================
echo    Accounting System
echo    Ministry of Health - Jordan
echo ========================================
echo.

echo Starting system...
echo.

REM Use the specific Python path found
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists at this path
if exist %PYTHON_PATH% (
    echo Python found at: %PYTHON_PATH%
    echo.
    echo Checking required libraries...

    REM Check openpyxl
    %PYTHON_PATH% -c "import openpyxl; print('openpyxl: OK')" 2>nul
    if %errorlevel% neq 0 (
        echo Installing openpyxl...
        %PYTHON_PATH% -m pip install openpyxl
        if %errorlevel% neq 0 (
            echo Failed to install openpyxl
            pause
            exit /b 1
        )
    )

    REM Check tkinter
    %PYTHON_PATH% -c "import tkinter; print('tkinter: OK')" 2>nul
    if %errorlevel% neq 0 (
        echo tkinter not available - this is required for GUI
        pause
        exit /b 1
    )

    echo All libraries OK!
    
    echo.
    echo Starting Accounting System...
    echo.
    
    REM Try different launcher files
    if exist "launcher.py" (
        %PYTHON_PATH% launcher.py
    ) else if exist "main.py" (
        %PYTHON_PATH% main.py
    ) else if exist "start.py" (
        %PYTHON_PATH% start.py
    ) else if exist "app.py" (
        %PYTHON_PATH% app.py
    ) else (
        echo ERROR: No launcher file found!
        echo Looking for: launcher.py, main.py, start.py, or app.py
        pause
        exit /b 1
    )
    
) else (
    echo ERROR: Python not found at expected location
    echo Expected: %PYTHON_PATH%
    echo.
    echo Please check Python installation or update the path in this script
    pause
    exit /b 1
)

echo.
echo System closed.
pause
