@echo off
chcp 65001 >nul
title Build Executable - Accounting System

echo ========================================
echo    Build Executable
echo    Accounting System
echo ========================================
echo.

echo Building standalone executable...
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found at: %PYTHON_PATH%
    echo.
    
    echo Installing required packages...
    %PYTHON_PATH% -m pip install --upgrade pip
    %PYTHON_PATH% -m pip install -r requirements.txt
    
    if %errorlevel% neq 0 (
        echo Failed to install requirements
        pause
        exit /b 1
    )
    
    echo.
    echo Building executable with PyInstaller...
    echo This may take several minutes...
    echo.
    
    REM Clean previous builds
    if exist "build" rmdir /s /q "build"
    if exist "dist" rmdir /s /q "dist"
    if exist "__pycache__" rmdir /s /q "__pycache__"
    
    REM Build the executable
    %PYTHON_PATH% -m PyInstaller accounting_system.spec --clean --noconfirm
    
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo    Build Successful!
        echo ========================================
        echo.
        echo Executable created at: dist\نظام_إدارة_المستندات_المحاسبية.exe
        echo.
        echo You can now copy the executable to any Windows computer
        echo and run it without installing Python.
        echo.
        
        REM Create a simple launcher
        echo @echo off > "dist\تشغيل_النظام.bat"
        echo chcp 65001 ^>nul >> "dist\تشغيل_النظام.bat"
        echo title نظام إدارة المستندات المحاسبية >> "dist\تشغيل_النظام.bat"
        echo echo Starting Accounting System... >> "dist\تشغيل_النظام.bat"
        echo "نظام_إدارة_المستندات_المحاسبية.exe" >> "dist\تشغيل_النظام.bat"
        
        echo Simple launcher created: dist\تشغيل_النظام.bat
        echo.
        
    ) else (
        echo.
        echo ========================================
        echo    Build Failed!
        echo ========================================
        echo.
        echo Please check the error messages above.
        echo.
    )
    
) else (
    echo ERROR: Python not found at expected location
    echo Expected: %PYTHON_PATH%
    echo.
    echo Please install Python or update the path in this script
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul
