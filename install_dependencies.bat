@echo off
echo ========================================
echo تثبيت متطلبات تطبيق الويب المحاسبي
echo ========================================
echo.

echo 📦 فحص Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً
    echo 🔗 تحميل من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo 📦 فحص pip...
pip --version
if %errorlevel% neq 0 (
    echo ❌ pip غير متاح! يرجى تثبيت pip
    pause
    exit /b 1
)

echo ✅ pip متاح
echo.

echo 🔄 تحديث pip...
python -m pip install --upgrade pip

echo.
echo 📦 تثبيت المتطلبات من web_requirements.txt...
pip install -r web_requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم تثبيت جميع المتطلبات بنجاح!
    echo.
    echo 🚀 يمكنك الآن تشغيل التطبيق باستخدام:
    echo    python web_app.py
    echo.
    echo 🌐 التطبيق سيكون متاحاً على: http://localhost:5000
    echo 👤 معلومات الدخول: admin / admin
) else (
    echo.
    echo ❌ حدث خطأ في التثبيت!
    echo 💡 جرب تشغيل الأمر التالي يدوياً:
    echo    pip install Flask Flask-CORS openpyxl
)

echo.
echo ========================================
pause
