# 📊 نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية

## 🎯 نظرة عامة

نظام شامل لإدارة المستندات المحاسبية مصمم خصيصاً لوزارة الصحة الأردنية، يوفر حلولاً متقدمة لتتبع وإدارة الحسابات والمستندات المالية بطريقة منظمة وفعالة.

---

## 🌟 الميزات الرئيسية

### 💼 إدارة الحسابات
- **إنشاء حسابات جديدة** مع رقم الحساب واسم الحساب والرصيد الافتتاحي
- **تنسيقان متاحان**: التنسيق الرسمي الحكومي والتنسيق الكلاسيكي
- **اتجاه عربي طبيعي** من اليمين لليسار لسهولة القراءة
- **تنقل سريع** بزر Enter بين حقول الإدخال

### 📝 إدارة المستندات
- **إضافة مستندات** بثلاث معلومات أساسية:
  - 💰 المبلغ (بتنسيق رقمي مع فواصل)
  - 📄 رقم مستند الصرف (تنسيق عام)
  - 💳 رقم التأدية (تنسيق عام)
- **6 أقسام منفصلة** في كل ورقة حساب
- **إنشاء جداول جديدة تلقائياً** عند امتلاء الجدول الحالي

### 🎨 تصميم جميل ومنظم
- **ألوان متدرجة** لكل نوع عمود:
  - 🟢 أخضر للمبالغ
  - 🔵 أزرق لأرقام المستندات  
  - 🔴 أحمر لأرقام التأدية
- **تلوين متناوب للصفوف** لسهولة القراءة
- **حدود قوية ومميزة** للعناوين والمجاميع
- **أيقونات تعبيرية** لتحسين الواجهة

### 📊 حسابات تلقائية دقيقة
- **صيغ جمع شاملة** في جميع الأعمدة:
  - عمود المبلغ: `=SUM()` لحساب إجمالي المبالغ
  - عمود رقم المستند: نص "الإجمالي"
  - عمود رقم التأدية: `=COUNTA()` لعد المستندات
- **ترحيل الأرصدة** بين الأقسام تلقائياً
- **شمول الرصيد الافتتاحي والمرحل** في الحسابات

---

## 🏗️ البنية التقنية

### 📁 الملفات الأساسية

#### 🚀 ملفات التشغيل
- **`main.py`** - نقطة الدخول الرئيسية للبرنامج
- **`run_silent.pyw`** - تشغيل صامت بدون نافذة الأوامر
- **`launcher.py`** - مشغل متقدم مع فحص المتطلبات

#### 🎮 ملفات الواجهة
- **`app.py`** - الواجهة الرئيسية والتحكم العام
- **`document_window.py`** - نافذة إضافة المستندات
- **`search_window.py`** - نافذة البحث والاستعلام
- **`manage_accounts.py`** - إدارة وتعديل الحسابات

#### 🔐 نظام المستخدمين
- **`user_manager.py`** - إدارة المستخدمين والصلاحيات
- **`users.json`** - قاعدة بيانات المستخدمين

#### 📊 معالجة البيانات
- **`excel_manager.py`** - المحرك الأساسي لمعالجة ملفات Excel
- **`accounting_system.xlsx`** - ملف البيانات الرئيسي

### 🔧 المكتبات المطلوبة
- **Python 3.7+** - لغة البرمجة الأساسية
- **tkinter** - واجهة المستخدم الرسومية (مدمجة مع Python)
- **openpyxl** - معالجة ملفات Excel المتقدمة

---

## 👥 نظام المستخدمين والصلاحيات

### 🔑 أنواع المستخدمين

#### 👑 المدير الرئيسي (Admin)
- **اسم المستخدم**: `admin`
- **كلمة المرور الافتراضية**: `admin`
- **الصلاحيات الكاملة**:
  - إضافة وتعديل وحذف الحسابات
  - إضافة وتعديل المستندات
  - عرض جميع التقارير
  - إدارة المستخدمين
  - تغيير كلمات المرور

#### 📝 موظف إدخال البيانات
- **الصلاحيات**:
  - إضافة المستندات
  - عرض الحسابات الموجودة
  - البحث في المستندات

#### 📊 مدير الحسابات
- **الصلاحيات**:
  - إضافة وتعديل الحسابات
  - إضافة المستندات
  - عرض التقارير المالية

#### 👀 مستخدم التقارير فقط
- **الصلاحيات**:
  - عرض التقارير
  - البحث في البيانات
  - تصدير التقارير

---

## 📋 دليل الاستخدام

### 🚀 بدء التشغيل

#### الطريقة الأولى - التشغيل المباشر:
```
انقر مزدوج على: run_silent.pyw
```

#### الطريقة الثانية - من سطر الأوامر:
```bash
python main.py
```

#### الطريقة الثالثة - المشغل المتقدم:
```bash
python launcher.py
```

### 🔐 تسجيل الدخول
1. أدخل اسم المستخدم وكلمة المرور
2. اضغط Enter أو انقر "تسجيل الدخول"
3. **للمرة الأولى**: استخدم `admin` / `admin`

### 💼 إضافة حساب جديد
1. انقر "إضافة حساب" من الشاشة الرئيسية
2. أدخل **رقم الحساب** واضغط Enter
3. أدخل **اسم الحساب** واضغط Enter  
4. أدخل **الرصيد الأولي** واضغط Enter (أو اتركه فارغاً للصفر)
5. انقر "✅ إضافة الحساب" أو اضغط Enter

### 📝 إضافة مستند جديد
1. انقر "إضافة مستند" من الشاشة الرئيسية
2. اختر الحساب من القائمة المنسدلة
3. أدخل **المبلغ**
4. أدخل **رقم مستند الصرف**
5. أدخل **رقم التأدية**
6. انقر "إضافة المستند"

### 🔍 البحث والاستعلام
1. انقر "البحث" من الشاشة الرئيسية
2. اختر نوع البحث (حسابات أو مستندات)
3. أدخل معايير البحث
4. اعرض النتائج أو صدرها

### 📊 عرض التقارير
1. انقر "التقارير" من الشاشة الرئيسية
2. اختر نوع التقرير المطلوب:
   - تقرير إجمالي للحسابات
   - تقرير مفصل للمستندات
   - تقرير مخصص
3. صدر التقرير أو اطبعه

---

## 📊 تفاصيل التنسيق

### 🎨 التنسيق الرسمي الحكومي
- **الترويسة**: معلومات وزارة الصحة
- **التاريخ والشهر**: حقول تلقائية
- **20 صف للمستندات** في كل قسم
- **صف المجاميع**: في الصف 28
- **التذييل**: توقيع المحاسب والتاريخ

### 🎨 التنسيق الكلاسيكي
- **عنوان مبسط**: سجل المستندات والحوالات المالية
- **23 صف للمستندات** في كل قسم  
- **صف المجاميع**: في الصف 32
- **مرونة أكبر** في التخصيص

### 📐 تخطيط الأقسام
```
القسم 1: الأعمدة A, B, C
القسم 2: الأعمدة D, E, F  
القسم 3: الأعمدة G, H, I
القسم 4: الأعمدة J, K, L
القسم 5: الأعمدة M, N, O
القسم 6: الأعمدة P, Q, R
```

### 🔢 صيغ الحساب
```excel
التنسيق الرسمي:
- المبلغ: =SUM(A7:A27)
- عدد المستندات: =COUNTA(C8:C27)

التنسيق الكلاسيكي:  
- المبلغ: =SUM(A8:A30)
- عدد المستندات: =COUNTA(C8:C30)
```

---

## 🔧 الصيانة والتحديث

### 💾 النسخ الاحتياطي
- **تلقائي**: يتم حفظ الملف تلقائياً عند كل تغيير
- **يدوي**: انقر "حفظ" من قائمة الملف
- **موقع الملف**: `accounting_system.xlsx` في مجلد البرنامج

### 🔄 التحديثات
- **فحص دوري** للمكتبات المطلوبة
- **تحديث تلقائي** لصيغ الحساب
- **إصلاح تلقائي** للأخطاء البسيطة

### 🛠️ استكشاف الأخطاء
- **رسائل خطأ واضحة** باللغة العربية
- **سجل مفصل** للعمليات في وحدة التحكم
- **إعادة تشغيل آمنة** في حالة الأخطاء

---

## 📈 الإحصائيات والأداء

### ⚡ السرعة
- **فتح سريع**: أقل من 3 ثوانٍ
- **إضافة مستند**: أقل من ثانية واحدة
- **إنشاء تقرير**: 2-5 ثوانٍ حسب حجم البيانات

### 📊 السعة
- **الحسابات**: غير محدودة عملياً
- **المستندات لكل حساب**: 138 مستند لكل جدول
- **الجداول لكل حساب**: غير محدودة
- **حجم الملف**: ينمو تدريجياً مع البيانات

### 🔒 الأمان
- **تشفير كلمات المرور**: SHA-256
- **صلاحيات متدرجة**: حسب نوع المستخدم
- **حماية من الأخطاء**: تحقق من صحة البيانات
- **نسخ احتياطية**: تلقائية ومتعددة

---

## 🎯 الاستخدامات المثلى

### 🏥 وزارة الصحة
- **إدارة ميزانيات المراكز الصحية**
- **تتبع مصروفات المستشفيات**
- **مراقبة المشتريات الطبية**
- **إعداد التقارير المالية الشهرية**

### 🏢 المؤسسات الحكومية
- **إدارة الحسابات الإدارية**
- **تتبع المصروفات التشغيلية**
- **مراقبة الميزانيات السنوية**
- **إعداد التقارير للجهات العليا**

### 🏪 الشركات الصغيرة والمتوسطة
- **إدارة الحسابات اليومية**
- **تتبع المبيعات والمشتريات**
- **مراقبة التدفق النقدي**
- **إعداد التقارير الضريبية**

---

## 🔮 التطوير المستقبلي

### 📱 الميزات المخططة
- **واجهة ويب**: للوصول من أي مكان
- **تطبيق موبايل**: لإدخال البيانات في الميدان
- **تكامل مع البنوك**: لاستيراد كشوف الحساب
- **ذكاء اصطناعي**: لتحليل الأنماط المالية

### 🌐 التوسعات المحتملة
- **دعم عملات متعددة**: للمؤسسات الدولية
- **تقارير متقدمة**: رسوم بيانية وتحليلات
- **تكامل مع أنظمة ERP**: للشركات الكبيرة
- **API متقدم**: للتكامل مع أنظمة أخرى

---

## 📞 الدعم والمساعدة

### 🆘 الحصول على المساعدة
- **دليل المستخدم**: هذا الملف
- **رسائل الخطأ**: واضحة ومفصلة باللغة العربية
- **سجل العمليات**: متاح في وحدة التحكم

### 🔧 الدعم التقني
- **فحص تلقائي**: للمتطلبات والمكتبات
- **إصلاح ذاتي**: للمشاكل البسيطة
- **تشخيص متقدم**: لتحديد مصدر المشاكل

---

## 📜 الخلاصة

نظام إدارة المستندات المحاسبية هو حل شامل ومتطور مصمم خصيصاً للبيئة العربية والمتطلبات الحكومية الأردنية. يجمع النظام بين السهولة في الاستخدام والقوة في الأداء، مع تصميم جميل وواجهة عربية طبيعية.

### ✨ النقاط المميزة:
- 🎨 **تصميم جميل ومنظم** مع ألوان متدرجة وتنسيق احترافي
- ⚡ **سرعة في الأداء** مع حسابات تلقائية دقيقة
- 🔐 **نظام أمان متقدم** مع صلاحيات متدرجة
- 🌍 **دعم كامل للعربية** مع اتجاه طبيعي من اليمين لليسار
- 🔄 **مرونة في التشغيل** مع خيارات متعددة للتشغيل والتخصيص

النظام جاهز للاستخدام الفوري ويمكن تخصيصه بسهولة لتلبية احتياجات مختلفة. مع التطوير المستمر والدعم التقني المتاح، يعتبر هذا النظام الخيار الأمثل لإدارة المستندات المحاسبية في البيئة العربية.

---

**تم تطوير هذا النظام بعناية فائقة ليكون الحل الأمثل لإدارة المستندات المحاسبية في وزارة الصحة الأردنية والمؤسسات المماثلة.**

*آخر تحديث: يونيو 2025*
