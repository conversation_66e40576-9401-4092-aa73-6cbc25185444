#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص شامل للتطبيق للتأكد من عدم وجود مشاكل
"""

import sys
import os
import ast
import re

def check_imports():
    """فحص الاستيرادات في app.py"""
    print("🔍 فحص الاستيرادات...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استخراج الاستيرادات
        imports = []
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('from ') or line.startswith('import '):
                if not line.startswith('#'):
                    imports.append(line)
        
        print(f"📋 تم العثور على {len(imports)} استيراد")
        
        # فحص الملفات المطلوبة
        required_files = [
            'excel_manager.py',
            'multi_excel_manager.py', 
            'document_window.py',
            'search_window.py',
            'manage_accounts.py',
            'user_manager.py',
            'receipts_account_window.py',
            'receipts_document_window.py',
            'manage_receipts_accounts.py',
            'account_balances_window.py'
        ]
        
        missing_files = []
        for file in required_files:
            if os.path.exists(file):
                print(f"✅ {file}")
            else:
                print(f"❌ {file} مفقود")
                missing_files.append(file)
        
        return len(missing_files) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص الاستيرادات: {str(e)}")
        return False

def check_function_calls():
    """فحص استدعاءات الدوال في app.py"""
    print("\n🔍 فحص استدعاءات الدوال...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن استدعاءات دوال محتملة مفقودة
        potential_issues = []
        
        # فحص استدعاءات SearchWindow
        if "SearchWindow(self.root, self.excel)" in content:
            potential_issues.append("استدعاء SearchWindow خاطئ - يجب أن يكون SearchWindow(self)")
        
        # فحص استدعاءات create_summary_report
        if "self.create_summary_report" in content:
            potential_issues.append("استدعاء create_summary_report - الدالة محذوفة")
        
        # فحص وجود الدوال المطلوبة
        required_functions = [
            "def create_action_buttons",
            "def setup_modern_ui", 
            "def show_account_balances_window",
            "def show_summary_report",
            "def search_accounts"
        ]
        
        missing_functions = []
        for func in required_functions:
            if func in content:
                print(f"✅ {func}")
            else:
                print(f"❌ {func} مفقود")
                missing_functions.append(func)
        
        if potential_issues:
            print("\n⚠️ مشاكل محتملة:")
            for issue in potential_issues:
                print(f"   - {issue}")
        
        return len(missing_functions) == 0 and len(potential_issues) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص الدوال: {str(e)}")
        return False

def check_syntax():
    """فحص صحة بناء الجملة في app.py"""
    print("\n🔍 فحص صحة بناء الجملة...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # محاولة تحليل الكود
        ast.parse(content)
        print("✅ بناء الجملة صحيح")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ في بناء الجملة: {str(e)}")
        print(f"   السطر {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص بناء الجملة: {str(e)}")
        return False

def check_button_configuration():
    """فحص تكوين الأزرار"""
    print("\n🔍 فحص تكوين الأزرار...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن buttons_config
        if "buttons_config = [" in content:
            print("✅ تكوين الأزرار موجود")
        else:
            print("❌ تكوين الأزرار مفقود")
            return False
        
        # فحص الأزرار المطلوبة
        expected_buttons = [
            "📁 إضافة حساب جديد",
            "📝 إضافة مستند", 
            "⚙️ إدارة الحسابات",
            "💰 إضافة حساب مقبوضات",
            "📄 إضافة مستند مقبوضات",
            "⚙️ إدارة حسابات المقبوضات",
            "🔍 بحث في الحسابات",
            "📊 نافذة تقارير الأرصدة",
            "👥 إدارة المستخدمين",
            "🚪 خروج من النظام"
        ]
        
        missing_buttons = []
        for button in expected_buttons:
            if button in content:
                print(f"✅ {button}")
            else:
                print(f"❌ {button} مفقود")
                missing_buttons.append(button)
        
        return len(missing_buttons) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأزرار: {str(e)}")
        return False

def check_menu_configuration():
    """فحص تكوين القوائم"""
    print("\n🔍 فحص تكوين القوائم...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص القوائم المطلوبة
        required_menus = [
            "def create_menu_with_permissions",
            "reports_menu.add_command",
            "command=self.show_summary_report",
            "command=self.show_account_balances_window"
        ]
        
        missing_menus = []
        for menu in required_menus:
            if menu in content:
                print(f"✅ {menu}")
            else:
                print(f"❌ {menu} مفقود")
                missing_menus.append(menu)
        
        return len(missing_menus) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص القوائم: {str(e)}")
        return False

def check_window_management():
    """فحص إدارة النوافذ"""
    print("\n🔍 فحص إدارة النوافذ...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص نظام إدارة النوافذ
        required_windows = [
            "'account_balances': None",
            "def is_window_open",
            "def register_window",
            "def focus_existing_window"
        ]
        
        missing_windows = []
        for window in required_windows:
            if window in content:
                print(f"✅ {window}")
            else:
                print(f"❌ {window} مفقود")
                missing_windows.append(window)
        
        return len(missing_windows) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص إدارة النوافذ: {str(e)}")
        return False

def check_account_balances_window():
    """فحص نافذة تقارير الأرصدة"""
    print("\n🔍 فحص نافذة تقارير الأرصدة...")
    
    try:
        if not os.path.exists('account_balances_window.py'):
            print("❌ ملف account_balances_window.py مفقود")
            return False
        
        with open('account_balances_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص المكونات المطلوبة
        required_components = [
            "class AccountBalancesWindow",
            "def create_receipts_accounts_report",
            "def create_materials_accounts_report",
            "def setup_enhanced_receipts_report",
            "def setup_enhanced_materials_report"
        ]
        
        missing_components = []
        for component in required_components:
            if component in content:
                print(f"✅ {component}")
            else:
                print(f"❌ {component} مفقود")
                missing_components.append(component)
        
        return len(missing_components) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص نافذة الأرصدة: {str(e)}")
        return False

def run_import_test():
    """اختبار الاستيراد الفعلي"""
    print("\n🔍 اختبار الاستيراد الفعلي...")
    
    try:
        # اختبار استيراد التطبيق
        import sys
        sys.path.append('.')
        
        from app import AccountingApp
        print("✅ تم استيراد AccountingApp بنجاح")
        
        # اختبار استيراد النوافذ
        from account_balances_window import AccountBalancesWindow
        print("✅ تم استيراد AccountBalancesWindow بنجاح")
        
        from search_window import SearchWindow
        print("✅ تم استيراد SearchWindow بنجاح")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام في الاستيراد: {str(e)}")
        return False

def generate_health_report():
    """إنشاء تقرير صحة التطبيق"""
    print("\n📊 إنشاء تقرير صحة التطبيق...")
    
    report = """
# 🏥 تقرير صحة التطبيق
## تاريخ الفحص: {date}

### ✅ المكونات المفحوصة:
1. **الاستيرادات والملفات المطلوبة**
2. **استدعاءات الدوال**
3. **صحة بناء الجملة**
4. **تكوين الأزرار**
5. **تكوين القوائم**
6. **إدارة النوافذ**
7. **نافذة تقارير الأرصدة**
8. **اختبار الاستيراد الفعلي**

### 📋 النتائج:
{results}

### 🎯 التوصيات:
{recommendations}

### 🚀 حالة التطبيق:
{status}
""".format(
        date=__import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        results="{results}",
        recommendations="{recommendations}",
        status="{status}"
    )
    
    return report

def main():
    """الدالة الرئيسية للفحص الشامل"""
    print("🏥 فحص شامل لصحة التطبيق")
    print("=" * 60)
    
    checks = [
        ("الاستيرادات والملفات", check_imports),
        ("استدعاءات الدوال", check_function_calls),
        ("صحة بناء الجملة", check_syntax),
        ("تكوين الأزرار", check_button_configuration),
        ("تكوين القوائم", check_menu_configuration),
        ("إدارة النوافذ", check_window_management),
        ("نافذة تقارير الأرصدة", check_account_balances_window),
        ("اختبار الاستيراد", run_import_test)
    ]
    
    results = []
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            result = check_func()
            results.append((name, result))
            if result:
                passed += 1
                print(f"✅ {name}: نجح")
            else:
                print(f"❌ {name}: فشل")
        except Exception as e:
            print(f"❌ {name}: خطأ - {str(e)}")
            results.append((name, False))
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الفحص الشامل: {passed}/{total}")
    
    if passed == total:
        print("🎉 التطبيق في حالة ممتازة - لا توجد مشاكل!")
        status = "✅ ممتاز - جاهز للاستخدام"
        recommendations = "لا توجد توصيات - التطبيق يعمل بشكل مثالي"
    elif passed >= total * 0.8:
        print("✅ التطبيق في حالة جيدة - مشاكل طفيفة")
        status = "⚠️ جيد - مشاكل طفيفة"
        recommendations = "راجع المشاكل المذكورة أعلاه وأصلحها"
    else:
        print("⚠️ التطبيق يحتاج إصلاحات")
        status = "❌ يحتاج إصلاحات"
        recommendations = "أصلح المشاكل الأساسية قبل الاستخدام"
    
    # إنشاء تقرير مفصل
    detailed_results = []
    for name, result in results:
        status_icon = "✅" if result else "❌"
        detailed_results.append(f"{status_icon} {name}")
    
    report = f"""
# 🏥 تقرير صحة التطبيق
## تاريخ الفحص: {__import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

### 📊 النتائج: {passed}/{total}

### 📋 تفاصيل الفحص:
{chr(10).join(detailed_results)}

### 🎯 التوصيات:
{recommendations}

### 🚀 حالة التطبيق:
{status}

### 📝 ملاحظات:
- تم فحص جميع المكونات الأساسية
- تم التحقق من صحة الكود والاستيرادات
- تم اختبار الوظائف الرئيسية

### 🔧 للاستخدام:
```bash
python app.py
```
"""
    
    with open('تقرير_صحة_التطبيق.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 تم إنشاء تقرير مفصل: تقرير_صحة_التطبيق.md")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
