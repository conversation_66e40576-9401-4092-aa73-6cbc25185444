import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/accounting_provider.dart';
import '../utils/app_theme.dart';

class AddAccountScreen extends StatefulWidget {
  const AddAccountScreen({super.key});

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountNumberController = TextEditingController();
  final _accountNameController = TextEditingController();
  final _openingBalanceController = TextEditingController(text: '0');

  @override
  void dispose() {
    _accountNumberController.dispose();
    _accountNameController.dispose();
    _openingBalanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AccountingProvider>(
        builder: (context, accountingProvider, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الصفحة
                  Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.userPlus,
                        color: AppTheme.accentColor,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'إضافة حساب جديد',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // بطاقة النموذج
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // معلومات الحساب
                          Text(
                            'معلومات الحساب',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // رقم الحساب
                          TextFormField(
                            controller: _accountNumberController,
                            decoration: const InputDecoration(
                              labelText: 'رقم الحساب',
                              prefixIcon: Icon(FontAwesomeIcons.hashtag),
                              helperText: 'رقم فريد للحساب',
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال رقم الحساب';
                              }
                              if (int.tryParse(value) == null) {
                                return 'يرجى إدخال رقم صحيح';
                              }
                              if (int.parse(value) <= 0) {
                                return 'يجب أن يكون رقم الحساب أكبر من صفر';
                              }
                              
                              // التحقق من عدم تكرار رقم الحساب
                              final existingAccount = accountingProvider.accounts
                                  .where((account) => account.number == int.parse(value))
                                  .firstOrNull;
                              if (existingAccount != null) {
                                return 'رقم الحساب موجود مسبقاً';
                              }
                              
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // اسم الحساب
                          TextFormField(
                            controller: _accountNameController,
                            decoration: const InputDecoration(
                              labelText: 'اسم الحساب',
                              prefixIcon: Icon(FontAwesomeIcons.user),
                              helperText: 'اسم وصفي للحساب',
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال اسم الحساب';
                              }
                              if (value.length < 3) {
                                return 'يجب أن يكون اسم الحساب 3 أحرف على الأقل';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // الرصيد الافتتاحي
                          TextFormField(
                            controller: _openingBalanceController,
                            decoration: const InputDecoration(
                              labelText: 'الرصيد الافتتاحي',
                              prefixIcon: Icon(FontAwesomeIcons.coins),
                              suffixText: 'د.أ',
                              helperText: 'الرصيد الأولي للحساب (اختياري)',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                              signed: true,
                            ),
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                if (double.tryParse(value) == null) {
                                  return 'يرجى إدخال رصيد صحيح';
                                }
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // أزرار الإجراءات
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                  onPressed: _clearForm,
                                  child: const Text('مسح'),
                                ),
                              ),
                              
                              const SizedBox(width: 16),
                              
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: accountingProvider.isLoading 
                                      ? null 
                                      : _addAccount,
                                  child: accountingProvider.isLoading
                                      ? const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                          ),
                                        )
                                      : const Text('إنشاء الحساب'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // معلومات إضافية
                  Card(
                    color: Colors.green[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                FontAwesomeIcons.lightbulb,
                                color: Colors.green,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'نصائح مهمة',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green[800],
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 8),
                          
                          Text(
                            '• اختر رقم حساب فريد وسهل التذكر\n'
                            '• استخدم اسماً وصفياً واضحاً للحساب\n'
                            '• يمكن ترك الرصيد الافتتاحي صفراً\n'
                            '• سيتم إنشاء ورقة منفصلة للحساب تلقائياً',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // قائمة الحسابات الموجودة
                  if (accountingProvider.accounts.isNotEmpty) ...[
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  FontAwesomeIcons.list,
                                  color: AppTheme.secondaryColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'الحسابات الموجودة',
                                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 12),
                            
                            ...accountingProvider.accounts.take(5).map((account) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 4),
                                child: Text(
                                  '${account.number} - ${account.name}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.subtitleColor,
                                  ),
                                ),
                              );
                            }),
                            
                            if (accountingProvider.accounts.length > 5)
                              Text(
                                'و ${accountingProvider.accounts.length - 5} حسابات أخرى...',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.subtitleColor,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _clearForm() {
    _accountNumberController.clear();
    _accountNameController.clear();
    _openingBalanceController.text = '0';
  }

  Future<void> _addAccount() async {
    if (_formKey.currentState!.validate()) {
      final accountingProvider = Provider.of<AccountingProvider>(context, listen: false);
      
      final error = await accountingProvider.addAccount(
        accountNumber: int.parse(_accountNumberController.text),
        accountName: _accountNameController.text,
        openingBalance: double.tryParse(_openingBalanceController.text) ?? 0.0,
      );

      if (mounted) {
        if (error == null) {
          // نجح الإنشاء
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء الحساب بنجاح'),
              backgroundColor: AppTheme.accentColor,
            ),
          );
          _clearForm();
        } else {
          // فشل الإنشاء
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(error),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    }
  }
}
