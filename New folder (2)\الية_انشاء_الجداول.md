# 📊 آلية إنشاء الجداول في نظام إدارة المستندات المحاسبية

## 🔍 تتبع دورة حياة ملف Excel والجداول

### 📁 1. إنشاء ملف Excel بشكل عام

#### متى يتم إنشاء الملف:
- عند **أول تشغيل للنظام** وعدم وجود ملف `accounting_system.xlsx`
- عند **فشل تحميل الملف الموجود** (تالف أو مفقود)

#### الدالة المسؤولة:
```python
def load_or_create_workbook(self):
    """تحميل الملف الموجود أو إنشاء ملف جديد"""
    if os.path.exists(self.current_file):
        # محاولة تحميل الملف الموجود
        try:
            self.workbook = openpyxl.load_workbook(self.current_file)
        except:
            # إذا فشل التحميل، إنشاء ملف جديد
            self._backup_and_create_new()
    else:
        # إنشاء ملف جديد
        self.create_new_workbook()
```

#### دالة الإنشاء الفعلية:
```python
def create_new_workbook(self):
    """إنشاء ملف عمل جديد"""
    self.workbook = openpyxl.Workbook()
    
    # حذف الورقة الافتراضية
    if 'Sheet' in self.workbook.sheetnames:
        self.workbook.remove(self.workbook['Sheet'])
    
    # إنشاء ورقة ترحيب أولية
    welcome_sheet = self.workbook.create_sheet("مرحباً")
    welcome_sheet['A1'] = "مرحباً بك في نظام إدارة المستندات المحاسبية"
```

---

### 📋 2. إضافة الجدول الأول إلى صفحة الحساب

#### متى يتم إنشاء الجدول الأول:
- عند **إنشاء حساب جديد** من واجهة "إضافة حساب"
- **فوراً** مع إنشاء صفحة الحساب

#### الدالة المسؤولة:
```python
def create_account_sheet(self, account_num, account_name, initial_balance=0, format_type="classic"):
    """إنشاء صفحة حساب جديدة مع دعم أنواع تنسيق متعددة"""
    # إنشاء الورقة
    ws = self.workbook.create_sheet(sheet_name)
    
    # اختيار نوع التنسيق
    if format_type == "official":
        self._setup_official_format(ws, account_num, account_name, initial_balance)
    else:
        # التنسيق الكلاسيكي (الافتراضي) - يتضمن الجدول الأول
        self._setup_header(ws, account_num, account_name)
        self._setup_sections(ws, initial_balance)  # ← هنا يتم إنشاء الجدول الأول
```

#### دالة إنشاء الجدول الأول:
```python
def _setup_sections(self, ws, initial_balance):
    """إعداد أقسام الصفحة"""
    # إعداد عنوان الأقسام
    ws.merge_cells('A6:R6')
    ws['A6'] = "سجل المستندات والحوالات المالية"
    
    # إعداد عناوين الأقسام الستة (الصفوف 7-8)
    for i in range(6):
        col = chr(65 + (i * 3))  # A, D, G, J, M, P
        # إعداد العناوين...
    
    # إعداد صفوف البيانات (الصفوف 9-28) - 20 صف
    # إعداد صف المجاميع (الصف 29)
```

---

### ➕ 3. إضافة جدول جديد إلى صفحة الحساب

#### متى يتم إنشاء جدول جديد:
- عند **إضافة مستند جديد** والجدول الحالي **ممتلئ بالكامل**
- **تلقائياً** دون تدخل المستخدم

#### الدالة المسؤولة (نقطة البداية):
```python
def add_document(self, sheet_name, amount, doc_num, pay_num):
    """إضافة مستند جديد"""
    # البحث عن أول خلية فارغة
    empty_cell = self._find_empty_cell_sequential_sections(ws)
    
    if not empty_cell:
        # محاولة إنشاء جدول جديد تلقائياً ← هنا يتم اتخاذ القرار
        print("📝 جميع الأقسام في الجدول الحالي ممتلئة - إنشاء جدول جديد تلقائياً...")
        
        # استخراج معلومات الحساب
        account_info = self._extract_account_info(sheet_name)
        if account_info:
            account_num, account_name = account_info
            # إنشاء جدول جديد تلقائياً مع ترحيل الرصيد
            new_table_created = self._create_new_table_with_balance_transfer(ws, account_num, account_name)
```

#### دالة إنشاء الجدول الجديد:
```python
def _create_new_table_with_balance_transfer(self, ws, account_num, account_name):
    """إنشاء جدول جديد مع ترحيل الرصيد"""
    # العثور على آخر جدول موجود
    last_table_end = self._find_last_table_end(ws)
    
    # حساب موقع الجدول الجديد
    new_table_start = last_table_end + 5  # فاصل 5 صفوف
    
    # حساب الرصيد المرحل من الجدول السابق
    carried_balance = self._calculate_last_table_section6_total(ws, last_table_end)
    
    # إنشاء الجدول الجديد
    success = self._create_table_at_position_enhanced(ws, new_table_start, account_num, account_name, carried_balance)
```

#### دالة الإنشاء الفعلية للجدول:
```python
def _create_table_at_position_enhanced(self, ws, start_row, account_num, account_name, carried_balance):
    """إنشاء جدول في موقع محدد مع ترحيل الرصيد المحسن"""
    # إعداد عناوين الجدول
    self._setup_table_headers_at_position(ws, start_row)
    
    # إعداد صفوف البيانات
    self._setup_table_data_rows_at_position(ws, start_row + 3)
    
    # إعداد صف المجاميع مع ترحيل الرصيد المحسن
    self._setup_table_totals_enhanced(ws, start_row + 23, carried_balance)
```

---

## 🎯 هيكل الجدول - 6 أقسام × 3 أعمدة

### تنظيم الأعمدة:
- **القسم 1:** أعمدة A, B, C (المبلغ، رقم المستند، رقم التأدية)
- **القسم 2:** أعمدة D, E, F 
- **القسم 3:** أعمدة G, H, I
- **القسم 4:** أعمدة J, K, L
- **القسم 5:** أعمدة M, N, O
- **القسم 6:** أعمدة P, Q, R

### آلية البحث عن الخلايا الفارغة:
```python
def _find_empty_cell_sequential_sections(self, ws):
    """البحث عن خلية فارغة بالترتيب المطلوب: الجدول الأول-القسم الأول حتى القسم السادس"""
    table_start_row = 8  # بداية الجدول الأول
    
    while table_start_row < 1000:  # حد أقصى للبحث
        # فحص وجود جدول في هذا الموقع
        if self._is_table_header_at_row(ws, table_start_row - 3):
            # البحث عن خلية فارغة في هذا الجدول بالترتيب المطلوب
            empty_cell = self._find_empty_cell_in_table_sequential(ws, table_start_row)
            if empty_cell:
                return empty_cell
            # الانتقال إلى الجدول التالي
            table_start_row += 35  # ارتفاع الجدول الواحد
```

### آلية ترحيل الأرصدة:
```python
def _calculate_last_table_section6_total(self, ws, last_table_end):
    """حساب مجموع القسم السادس من آخر جدول"""
    # القسم السادس يبدأ من العمود P (16)
    section6_col = 16  # P
    
    # البحث عن صف المجاميع (عادة في نهاية الجدول)
    totals_row = last_table_end - 2
    
    # قراءة قيمة المجموع من القسم السادس
    total_cell = ws.cell(row=totals_row, column=section6_col)
    if total_cell.value and isinstance(total_cell.value, (int, float)):
        return total_cell.value
    return 0
```

---

## 🔄 خريطة التدفق الكاملة

### 1. بداية النظام:
```
تشغيل النظام → ExcelManager.__init__() → load_or_create_workbook()
                                        ↓
                                   ملف موجود؟
                                   ↙        ↘
                               نعم          لا
                                ↓           ↓
                        تحميل الملف    create_new_workbook()
```

### 2. إنشاء حساب جديد:
```
المستخدم ينقر "إضافة حساب" → AddAccountDialog → excel.create_account_sheet()
                                                    ↓
                                            إنشاء ورقة جديدة
                                                    ↓
                                            _setup_header()
                                                    ↓
                                            _setup_sections() ← إنشاء الجدول الأول
```

### 3. إضافة مستند:
```
المستخدم ينقر "إضافة مستند" → AddDocumentWindow → excel.add_document()
                                                    ↓
                                        _find_empty_cell_sequential_sections()
                                                    ↓
                                            خلية فارغة موجودة؟
                                            ↙              ↘
                                        نعم               لا
                                         ↓                ↓
                                    إضافة المستند    _create_new_table_with_balance_transfer()
                                                            ↓
                                                    إنشاء جدول جديد
                                                            ↓
                                                    إضافة المستند في الجدول الجديد
```

---

## 📊 ملخص الدوال حسب الوظيفة

| **الوظيفة** | **الدالة المسؤولة** | **متى تُستدعى** |
|-------------|-------------------|-----------------|
| **إنشاء ملف Excel** | `create_new_workbook()` | أول تشغيل أو فشل تحميل |
| **إنشاء الجدول الأول** | `_setup_sections()` | عند إنشاء حساب جديد |
| **إنشاء جدول إضافي** | `_create_new_table_with_balance_transfer()` | عند امتلاء الجدول الحالي |
| **تحديد الحاجة لجدول جديد** | `_find_empty_cell_sequential_sections()` | عند إضافة كل مستند |

---

## 📋 خلاصة الهيكل

```
ورقة الحساب:
├── الصفوف 1-5: الترويسة
├── الصف 6: عنوان الجدول
├── الصفوف 7-8: عناوين الأعمدة
├── الصفوف 9-28: بيانات المستندات (20 صف)
├── الصف 29: صف المجاميع
├── الصفوف 30-34: فاصل
└── الجدول التالي (يبدأ من الصف 35)
```

---

## 🎯 النقاط المهمة

1. **الإنشاء التلقائي:** الجداول الجديدة تُنشأ **تلقائياً** دون تدخل المستخدم
2. **الترحيل التلقائي:** الأرصدة تُرحل **تلقائياً** من الجدول السابق
3. **الفحص المستمر:** كل إضافة مستند تتضمن فحص الحاجة لجدول جديد
4. **التنظيم المنطقي:** الجداول تُرتب **تسلسلياً** في نفس الورقة

---

## 🔧 التنسيقات المختلفة

### أ. التنسيق الكلاسيكي:
- ترويسة بسيطة مع معلومات الحساب
- 6 أقسام × 3 أعمدة
- 20 صف لكل جدول

### ب. التنسيق الرسمي:
- ترويسة رسمية لوزارة الصحة
- نفس هيكل الجدول مع تنسيق محسن
- تذييل رسمي للتوقيعات

---

## 🚀 دورة حياة إضافة المستندات

1. **البحث عن خلية فارغة** في الجدول الحالي
2. **إذا وُجدت:** إضافة المستند مباشرة
3. **إذا لم توجد:** إنشاء جدول جديد تلقائياً
4. **ترحيل الرصيد** من الجدول السابق
5. **إضافة المستند** في الجدول الجديد

هذا النظام يضمن **تنظيماً مثالياً** للبيانات مع **قابلية توسع لا محدودة** للمستندات!

---

*تم إنشاء هذا الملف في: 2025-06-27*
*نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية*
