import tkinter as tk
from tkinter import ttk, messagebox
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
import os

class AddDocumentWindow(tk.Toplevel):
    """نافذة إضافة مستند - مطابقة لنافذة المقبوضات"""

    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("إضافة مستند جديد")
        self.parent = parent

        # تكوين النافذة
        self.geometry("700x600")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent.root)
        self.grab_set()

        # متغيرات النموذج
        self.document_num_var = tk.StringVar()
        self.payment_num_var = tk.StringVar()
        self.total_amount_var = tk.StringVar(value="0.000")

        # متغير رسالة الحالة
        self.status_message_var = tk.StringVar(value="جاهز لإضافة مستند جديد")

        # قائمة الحسابات المتاحة
        self.available_accounts = []

        # قائمة صفوف التوزيع
        self.distribution_rows = []

        # متغير لحفظ الحساب المختار سابقاً
        self.last_selected_account = ""

        # إنشاء الواجهة
        self.create_interface()

        # تحميل قائمة الحسابات
        self.load_accounts_list()

        # إعداد التحديث التلقائي
        self.setup_auto_refresh()

        # توسيط النافذة
        self.center_window()

        # ربط إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # عنوان النافذة
        title_label = ttk.Label(main_frame,
                               text="📄 إضافة مستند جديد",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))

        # إطار معلومات المستند
        document_frame = ttk.LabelFrame(main_frame,
                                       text="📋 معلومات المستند",
                                       padding="15")
        document_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # رقم المستند
        ttk.Label(document_frame, text="رقم المستند:", font=('Arial', 10, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=5)
        self.document_entry = ttk.Entry(document_frame, textvariable=self.document_num_var,
                                       font=('Arial', 12), width=30)
        self.document_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        # رقم التأدية
        ttk.Label(document_frame, text="رقم التأدية:", font=('Arial', 10, 'bold')).grid(
            row=1, column=0, sticky=tk.W, pady=5)
        self.payment_entry = ttk.Entry(document_frame, textvariable=self.payment_num_var,
                                      font=('Arial', 12), width=30)
        self.payment_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        # إجمالي المبلغ (للعرض فقط)
        ttk.Label(document_frame, text="إجمالي المبلغ:", font=('Arial', 10, 'bold')).grid(
            row=2, column=0, sticky=tk.W, pady=5)
        self.total_entry = ttk.Entry(document_frame, textvariable=self.total_amount_var,
                                    font=('Arial', 12, 'bold'), width=30, state='readonly')
        self.total_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        # تكوين العمود
        document_frame.columnconfigure(1, weight=1)

        # إطار توزيع المبالغ
        distribution_frame = ttk.LabelFrame(main_frame,
                                           text="📊 توزيع المبالغ على الحسابات",
                                           padding="15")
        distribution_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))

        # إطار للجدول مع شريط تمرير
        table_container = ttk.Frame(distribution_frame)
        table_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # إنشاء Canvas وScrollbar للجدول
        self.canvas = tk.Canvas(table_container, height=200, bg='white')
        scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        table_container.columnconfigure(0, weight=1)
        table_container.rowconfigure(0, weight=1)

        # عناوين الجدول
        headers_frame = ttk.Frame(self.scrollable_frame)
        headers_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(headers_frame, text="اسم الحساب", font=('Arial', 10, 'bold')).grid(
            row=0, column=0, padx=5, sticky=tk.W)
        ttk.Label(headers_frame, text="المبلغ (0.000)", font=('Arial', 10, 'bold')).grid(
            row=0, column=1, padx=5, sticky=tk.W)
        ttk.Label(headers_frame, text="حذف", font=('Arial', 10, 'bold')).grid(
            row=0, column=2, padx=5, sticky=tk.W)

        # إطار للصفوف
        self.rows_frame = ttk.Frame(self.scrollable_frame)
        self.rows_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # زر إضافة حساب جديد
        add_account_btn = ttk.Button(distribution_frame,
                                    text="➕ إضافة حساب جديد",
                                    command=self.add_account_row)
        add_account_btn.grid(row=1, column=0, pady=(10, 0), sticky=tk.W)

        # تكوين العمود
        distribution_frame.columnconfigure(0, weight=1)
        distribution_frame.rowconfigure(0, weight=1)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # زر حفظ المستند
        save_btn = ttk.Button(buttons_frame,
                             text="💾 حفظ المستند",
                             command=self.save_document,
                             style='Accent.TButton')
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر حساب الإجمالي
        calculate_btn = ttk.Button(buttons_frame,
                                  text="🧮 حساب الإجمالي",
                                  command=self.calculate_total)
        calculate_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث الحسابات
        refresh_accounts_btn = ttk.Button(buttons_frame,
                                         text="🔄 تحديث الحسابات",
                                         command=self.manual_refresh_accounts)
        refresh_accounts_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر مسح الكل
        clear_btn = ttk.Button(buttons_frame,
                              text="🗑️ مسح الكل",
                              command=self.clear_all)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر المساعدة
        help_btn = ttk.Button(buttons_frame,
                             text="❓ مساعدة",
                             command=self.show_help)
        help_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # زر الإغلاق
        close_btn = ttk.Button(buttons_frame,
                              text="❌ إغلاق",
                              command=self.on_closing)
        close_btn.pack(side=tk.RIGHT)

        # إطار رسالة الحالة
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # رسالة الحالة
        self.status_label = ttk.Label(status_frame,
                                     textvariable=self.status_message_var,
                                     font=('Arial', 10),
                                     foreground='green')
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))

        # إعداد التنقل بزر الإدخال
        self.setup_enter_navigation()

        # تركيز على رقم المستند
        self.document_entry.focus()

    def setup_enter_navigation(self):
        """إعداد التنقل بزر الإدخال"""
        def on_document_enter(event):
            self.payment_entry.focus()
            return "break"

        def on_payment_enter(event):
            # الانتقال إلى أول حقل مبلغ في الجدول
            if self.distribution_rows:
                self.distribution_rows[0]['amount_entry'].focus()
            else:
                self.add_account_row()
                if self.distribution_rows:
                    self.distribution_rows[0]['amount_entry'].focus()
            return "break"

        self.document_entry.bind('<Return>', on_document_enter)
        self.payment_entry.bind('<Return>', on_payment_enter)

        # إعداد التنقل في جدول التوزيع
        self.setup_table_navigation()

    def setup_table_navigation(self):
        """إعداد التنقل في جدول التوزيع"""
        def on_amount_enter(event, row_index):
            # الانتقال إلى الصف التالي أو إضافة صف جديد
            if row_index < len(self.distribution_rows) - 1:
                self.distribution_rows[row_index + 1]['amount_entry'].focus()
            else:
                # إضافة صف جديد والانتقال إليه
                self.add_account_row()
                if self.distribution_rows:
                    self.distribution_rows[-1]['amount_entry'].focus()
            return "break"

        # تطبيق التنقل على جميع الصفوف الموجودة
        for i, row_data in enumerate(self.distribution_rows):
            row_data['amount_entry'].bind('<Return>', lambda event, idx=i: on_amount_enter(event, idx))

    def load_accounts_list(self):
        """تحميل قائمة الحسابات مع التحديث التلقائي"""
        try:
            print("🔄 تحميل قائمة الحسابات...")

            # قائمة الأوراق المستبعدة
            excluded_sheets = ['التقارير', 'تقرير المستندات', 'التقرير الإجمالي', 'مرحباً']

            # جلب جميع أسماء الأوراق
            all_sheets = self.parent.excel.workbook.sheetnames

            # تصفية الحسابات الصالحة
            self.available_accounts = [sheet for sheet in all_sheets
                                     if sheet not in excluded_sheets]

            # تحديث قوائم الحسابات في جميع الصفوف
            self.update_all_account_combos()

            # إضافة صف أولي إذا لم يكن موجوداً
            if not self.distribution_rows:
                self.add_account_row()

            # تحديث رسالة الحالة
            self.status_message_var.set(f"تم تحميل {len(self.available_accounts)} حساب")

            print(f"✅ تم تحميل {len(self.available_accounts)} حساب")

        except Exception as e:
            error_msg = f"خطأ في تحميل الحسابات: {str(e)}"
            print(f"❌ {error_msg}")
            self.status_message_var.set(error_msg)
            self.update_status_message(f"❌ {error_msg}", 'red')

    def load_accounts_list_silent(self):
        """تحميل قائمة الحسابات بصمت (للتحديث التلقائي)"""
        try:
            excluded_sheets = ['التقارير', 'تقرير المستندات', 'التقرير الإجمالي', 'مرحباً']
            all_sheets = self.parent.excel.workbook.sheetnames
            self.available_accounts = [sheet for sheet in all_sheets
                                     if sheet not in excluded_sheets]
        except:
            pass

    def update_all_account_combos(self):
        """تحديث قوائم الحسابات في جميع الصفوف"""
        for row_data in self.distribution_rows:
            current_selection = row_data['account_var'].get()
            row_data['account_combo']['values'] = self.available_accounts

            # إعادة تعيين الاختيار إن وجد
            if current_selection and current_selection in self.available_accounts:
                row_data['account_var'].set(current_selection)

    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي لقائمة الحسابات"""
        def refresh_accounts():
            old_accounts = self.available_accounts.copy()
            self.load_accounts_list_silent()

            # تحديث قوائم الحسابات في جميع الصفوف
            if old_accounts != self.available_accounts:
                self.update_all_account_combos()
                print(f"🔄 تم تحديث تلقائي: {len(self.available_accounts)} حساب")

            # جدولة التحديث التالي
            self.after(10000, refresh_accounts)  # 10 ثواني

        # بدء التحديث التلقائي
        self.after(10000, refresh_accounts)

    def manual_refresh_accounts(self):
        """تحديث يدوي لقائمة الحسابات"""
        self.load_accounts_list()
        self.update_status_message(f"✅ تم تحديث قائمة الحسابات بنجاح ({len(self.available_accounts)} حساب)", 'green')

    def add_account_row(self):
        """إضافة صف جديد لاختيار حساب وإدخال مبلغ"""
        row_index = len(self.distribution_rows)

        # إطار الصف
        row_frame = ttk.Frame(self.rows_frame)
        row_frame.grid(row=row_index, column=0, sticky=(tk.W, tk.E), pady=2)

        # متغيرات الصف
        account_var = tk.StringVar()
        amount_var = tk.StringVar()

        # اختيار الحساب
        account_combo = ttk.Combobox(row_frame, textvariable=account_var,
                                    values=self.available_accounts,
                                    state='readonly', width=30)
        account_combo.grid(row=0, column=0, padx=5, sticky=tk.W)

        # إدخال المبلغ
        amount_entry = ttk.Entry(row_frame, textvariable=amount_var,
                                font=('Arial', 10), width=15)
        amount_entry.grid(row=0, column=1, padx=5, sticky=tk.W)

        # زر الحذف
        delete_btn = ttk.Button(row_frame, text="🗑️",
                               command=lambda: self.delete_account_row(row_index),
                               width=3)
        delete_btn.grid(row=0, column=2, padx=5, sticky=tk.W)

        # حفظ بيانات الصف
        row_data = {
            'frame': row_frame,
            'account_var': account_var,
            'amount_var': amount_var,
            'account_combo': account_combo,
            'amount_entry': amount_entry,
            'delete_btn': delete_btn
        }

        self.distribution_rows.append(row_data)

        # ربط تحديث الإجمالي عند تغيير المبلغ
        amount_var.trace('w', lambda *args: self.calculate_total())

        # إعداد التنقل للصف الجديد
        self.setup_table_navigation()

        # تحديث منطقة التمرير
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        # تعيين الحساب المختار سابقاً إذا كان متوفراً
        if self.last_selected_account and self.last_selected_account in self.available_accounts:
            account_var.set(self.last_selected_account)

        return row_data

    def delete_account_row(self, row_index):
        """حذف صف من جدول التوزيع"""
        if len(self.distribution_rows) <= 1:
            messagebox.showwarning("تنبيه", "يجب الاحتفاظ بصف واحد على الأقل")
            return

        # حذف الصف من الواجهة
        self.distribution_rows[row_index]['frame'].destroy()

        # حذف الصف من القائمة
        del self.distribution_rows[row_index]

        # إعادة ترقيم الصفوف
        for i, row_data in enumerate(self.distribution_rows):
            row_data['frame'].grid(row=i, column=0, sticky=(tk.W, tk.E), pady=2)
            # تحديث دالة الحذف
            row_data['delete_btn'].configure(command=lambda idx=i: self.delete_account_row(idx))

        # إعادة إعداد التنقل
        self.setup_table_navigation()

        # تحديث الإجمالي
        self.calculate_total()

        # تحديث منطقة التمرير
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def calculate_total(self):
        """حساب إجمالي المبالغ"""
        total = 0.0
        for row_data in self.distribution_rows:
            try:
                amount = float(row_data['amount_var'].get() or 0)
                total += amount
            except ValueError:
                pass

        self.total_amount_var.set(f"{total:.3f}")

    def clear_all(self):
        """مسح جميع البيانات (بدون رسالة تأكيد)"""
        # مسح الحقول الرئيسية
        self.document_num_var.set("")
        self.payment_num_var.set("")
        self.total_amount_var.set("0.000")

        # مسح جميع صفوف التوزيع
        for row_data in self.distribution_rows:
            row_data['frame'].destroy()
        self.distribution_rows.clear()

        # إضافة صف جديد
        self.add_account_row()

        # تحديث رسالة الحالة
        self.update_status_message("🗑️ تم مسح جميع البيانات", 'blue')

        # التركيز على رقم المستند
        self.document_entry.focus()

    def save_document(self):
        """حفظ المستند"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_inputs():
                return

            # تحديث رسالة الحالة
            self.status_message_var.set("جاري حفظ المستند...")
            self.update()

            # حفظ المستندات لكل حساب
            saved_count = 0
            total_amount = 0.0

            for row_data in self.distribution_rows:
                account = row_data['account_var'].get()
                amount_str = row_data['amount_var'].get()

                if account and amount_str:
                    try:
                        amount = float(amount_str)
                        if amount > 0:
                            # إضافة المستند للحساب
                            success = self.parent.excel.add_document(
                                account,
                                amount,
                                self.document_num_var.get(),
                                self.payment_num_var.get()
                            )

                            if success:
                                saved_count += 1
                                total_amount += amount
                                # حفظ الحساب المختار
                                self.last_selected_account = account
                            else:
                                self.update_status_message(f"❌ فشل في حفظ المستند للحساب: {account}", 'red')
                                return

                    except ValueError:
                        self.update_status_message(f"❌ قيمة غير صحيحة للمبلغ في الحساب: {account}", 'red')
                        return

            if saved_count > 0:
                # تحديث رسالة النجاح في شريط الحالة
                success_msg = (f"✅ تم حفظ المستند {self.document_num_var.get()} بنجاح! "
                             f"عدد الحسابات: {saved_count} | إجمالي المبلغ: {total_amount:.3f} دينار")
                self.update_status_message(success_msg, 'green')

                # مسح البيانات والاحتفاظ بالحساب المختار
                self.clear_for_next_document()

            else:
                # رسالة تحذير في شريط الحالة
                self.update_status_message("⚠️ لم يتم حفظ أي مستند. تأكد من إدخال البيانات بشكل صحيح.", 'orange')

        except Exception as e:
            # عرض رسالة الخطأ في شريط الحالة
            self.update_status_message(f"❌ خطأ في حفظ المستند: {str(e)}", 'red')

    def update_status_message(self, message, color='green'):
        """تحديث رسالة الحالة"""
        self.status_message_var.set(message)
        self.status_label.config(foreground=color)

        # إعادة تعيين الرسالة بعد 8 ثواني للنجاح و 5 ثواني للأخرى
        if color == 'green':
            reset_time = 8000  # 8 ثواني لرسائل النجاح
        else:
            reset_time = 5000  # 5 ثواني لرسائل الخطأ والتحذير

        self.after(reset_time, lambda: self.status_message_var.set("جاهز لإضافة مستند جديد"))
        self.after(reset_time, lambda: self.status_label.config(foreground='green'))

    def clear_for_next_document(self):
        """مسح البيانات للمستند التالي مع الاحتفاظ بالحساب المختار"""
        # مسح الحقول الرئيسية
        self.document_num_var.set("")
        self.payment_num_var.set("")
        self.total_amount_var.set("0.000")

        # مسح المبالغ والاحتفاظ بالحسابات
        for row_data in self.distribution_rows:
            row_data['amount_var'].set("")

        # التركيز على رقم المستند
        self.document_entry.focus()

        # تحديث رسالة الحالة
        self.status_message_var.set("جاهز لإضافة مستند جديد")

    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        # فحص رقم المستند
        if not self.document_num_var.get().strip():
            self.update_status_message("❌ الرجاء إدخال رقم المستند", 'red')
            self.document_entry.focus()
            return False

        # فحص رقم التأدية
        if not self.payment_num_var.get().strip():
            self.update_status_message("❌ الرجاء إدخال رقم التأدية", 'red')
            self.payment_entry.focus()
            return False

        # فحص وجود حساب واحد على الأقل مع مبلغ
        valid_entries = 0
        for row_data in self.distribution_rows:
            account = row_data['account_var'].get()
            amount_str = row_data['amount_var'].get()

            if account and amount_str:
                try:
                    amount = float(amount_str)
                    if amount > 0:
                        valid_entries += 1
                except ValueError:
                    self.update_status_message(f"❌ قيمة غير صحيحة للمبلغ في الحساب: {account}", 'red')
                    row_data['amount_entry'].focus()
                    return False

        if valid_entries == 0:
            self.update_status_message("❌ الرجاء إدخال حساب واحد على الأقل مع مبلغ صحيح", 'red')
            return False

        return True

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = """
نافذة إضافة مستند جديد

الاستخدام:
1. أدخل رقم المستند
2. أدخل رقم التأدية
3. اختر الحساب وأدخل المبلغ
4. يمكن إضافة عدة حسابات للمستند الواحد
5. اضغط "حفظ المستند" لحفظ البيانات

اختصارات لوحة المفاتيح:
• Enter: التنقل بين الحقول
• Escape: إغلاق النافذة
• F1: عرض هذه المساعدة

الميزات:
• تحديث تلقائي لقائمة الحسابات
• حساب تلقائي للإجمالي
• الاحتفاظ بالحساب المختار للمستند التالي
• إنشاء جداول جديدة تلقائياً عند الحاجة
        """
        messagebox.showinfo("المساعدة - إضافة مستند", help_text)

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """معالج إغلاق النافذة"""
        # إلغاء تسجيل النافذة
        if hasattr(self.parent, 'unregister_window'):
            self.parent.unregister_window('add_document')

        # إغلاق النافذة
        self.destroy()
