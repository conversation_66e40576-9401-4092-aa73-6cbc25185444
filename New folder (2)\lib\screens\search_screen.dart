import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/accounting_provider.dart';
import '../models/account.dart';
import '../utils/app_theme.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final _searchController = TextEditingController();
  String _searchType = 'all';
  
  final Map<String, String> _searchTypes = {
    'all': 'البحث في الكل',
    'document': 'رقم المستند',
    'payment': 'رقم التأدية',
    'amount': 'المبلغ',
  };

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AccountingProvider>(
        builder: (context, accountingProvider, _) {
          return Column(
            children: [
              // شريط البحث
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // عنوان الصفحة
                    Row(
                      children: [
                        const Icon(
                          FontAwesomeIcons.search,
                          color: AppTheme.secondaryColor,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'البحث في المستندات',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // حقل البحث
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'أدخل كلمة البحث...',
                        prefixIcon: const Icon(FontAwesomeIcons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(FontAwesomeIcons.times),
                                onPressed: () {
                                  _searchController.clear();
                                  accountingProvider.clearSearchResults();
                                  setState(() {});
                                },
                              )
                            : null,
                      ),
                      onChanged: (value) {
                        setState(() {});
                        if (value.isEmpty) {
                          accountingProvider.clearSearchResults();
                        }
                      },
                      onSubmitted: (value) {
                        if (value.isNotEmpty) {
                          _performSearch();
                        }
                      },
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // نوع البحث
                    Row(
                      children: [
                        Text(
                          'نوع البحث:',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: DropdownButton<String>(
                            value: _searchType,
                            isExpanded: true,
                            underline: Container(),
                            items: _searchTypes.entries.map((entry) {
                              return DropdownMenuItem<String>(
                                value: entry.key,
                                child: Text(entry.value),
                              );
                            }).toList(),
                            onChanged: (String? value) {
                              if (value != null) {
                                setState(() {
                                  _searchType = value;
                                });
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: _searchController.text.isNotEmpty && !accountingProvider.isLoading
                              ? _performSearch
                              : null,
                          child: accountingProvider.isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text('بحث'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // النتائج
              Expanded(
                child: _buildSearchResults(accountingProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchResults(AccountingProvider accountingProvider) {
    if (accountingProvider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FontAwesomeIcons.exclamationTriangle,
              size: 64,
              color: AppTheme.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في البحث',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              accountingProvider.error!,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                accountingProvider.clearError();
                if (_searchController.text.isNotEmpty) {
                  _performSearch();
                }
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_searchController.text.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FontAwesomeIcons.search,
              size: 64,
              color: AppTheme.subtitleColor,
            ),
            const SizedBox(height: 16),
            Text(
              'ابدأ البحث',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'أدخل كلمة البحث في الحقل أعلاه',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.subtitleColor,
              ),
            ),
          ],
        ),
      );
    }

    final results = accountingProvider.searchResults;

    if (results.isEmpty && !accountingProvider.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FontAwesomeIcons.searchMinus,
              size: 64,
              color: AppTheme.subtitleColor,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على مستندات تطابق البحث',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.subtitleColor,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // عدد النتائج
        if (results.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Text(
              'تم العثور على ${results.length} نتيجة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        
        // قائمة النتائج
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: results.length,
            itemBuilder: (context, index) {
              final document = results[index];
              return _buildSearchResultItem(document);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultItem(Document document) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المستند
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    FontAwesomeIcons.fileAlt,
                    color: AppTheme.primaryColor,
                    size: 16,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مستند رقم: ${document.documentNumber}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'رقم التأدية: ${document.paymentNumber}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.subtitleColor,
                        ),
                      ),
                    ],
                  ),
                ),
                
                Text(
                  '${document.amount.toStringAsFixed(2)} د.أ',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.accentColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // معلومات الحساب والقسم
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        const Icon(
                          FontAwesomeIcons.user,
                          size: 14,
                          color: AppTheme.subtitleColor,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            'الحساب: ${document.row}', // يجب تعديل هذا لعرض معلومات الحساب الصحيحة
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.layerGroup,
                        size: 14,
                        color: AppTheme.subtitleColor,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        document.section,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      Provider.of<AccountingProvider>(context, listen: false)
          .searchDocuments(query: query, searchType: _searchType);
    }
  }
}
