#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة AccountDetailsDialog
"""

import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_account_details():
    """تشخيص مشكلة AccountDetailsDialog"""
    print("🔍 تشخيص مشكلة AccountDetailsDialog")
    print("=" * 50)
    
    try:
        # استيراد الكلاسات
        from manage_accounts import AccountDetailsDialog
        import inspect
        
        # فحص الدوال الموجودة في الكلاس
        print("📋 الدوال الموجودة في AccountDetailsDialog:")
        methods = inspect.getmembers(AccountDetailsDialog, predicate=inspect.isfunction)
        for name, method in methods:
            print(f"   - {name}")
        
        # فحص وجود الدالة المطلوبة
        if hasattr(AccountDetailsDialog, '_safe_get_numeric_value'):
            print("✅ الدالة _safe_get_numeric_value موجودة")
        else:
            print("❌ الدالة _safe_get_numeric_value غير موجودة")
        
        if hasattr(AccountDetailsDialog, '_calculate_sum_range'):
            print("✅ الدالة _calculate_sum_range موجودة")
        else:
            print("❌ الدالة _calculate_sum_range غير موجودة")
        
        # محاولة إنشاء instance للاختبار
        print("\n🧪 محاولة إنشاء instance للاختبار...")
        
        # إنشاء ملف Excel بسيط
        from excel_manager import ExcelManager
        excel = ExcelManager()
        excel.create_account_sheet("DEBUG001", "حساب تشخيص", 1000.0)
        
        # إنشاء نافذة tkinter
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        # محاولة إنشاء AccountDetailsDialog
        try:
            dialog = AccountDetailsDialog(
                parent=root,
                excel=excel,
                sheet_name="DEBUG001-حساب تشخيص",
                account_num="DEBUG001",
                account_name="حساب تشخيص"
            )
            print("✅ تم إنشاء AccountDetailsDialog بنجاح")
            
            # اختبار الدالة
            ws = excel.workbook["DEBUG001-حساب تشخيص"]
            test_cell = ws['A9']
            
            if hasattr(dialog, '_safe_get_numeric_value'):
                result = dialog._safe_get_numeric_value(test_cell)
                print(f"✅ الدالة تعمل بنجاح: {result}")
            else:
                print("❌ الدالة غير موجودة في الـ instance")
            
            dialog.destroy()
            root.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء AccountDetailsDialog: {str(e)}")
            import traceback
            traceback.print_exc()
            root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # تنظيف
        try:
            if os.path.exists("accounting_system.xlsx"):
                os.remove("accounting_system.xlsx")
        except:
            pass

def check_file_syntax():
    """فحص صحة syntax الملف"""
    print("\n🔍 فحص صحة syntax الملف")
    print("-" * 30)
    
    try:
        # محاولة استيراد الملف
        import manage_accounts
        print("✅ الملف يمكن استيراده بدون أخطاء")
        
        # إعادة تحميل الملف
        import importlib
        importlib.reload(manage_accounts)
        print("✅ إعادة تحميل الملف نجحت")
        
        # فحص الكلاس
        if hasattr(manage_accounts, 'AccountDetailsDialog'):
            print("✅ كلاس AccountDetailsDialog موجود")
            
            # فحص الدوال
            cls = manage_accounts.AccountDetailsDialog
            if hasattr(cls, '_safe_get_numeric_value'):
                print("✅ _safe_get_numeric_value موجودة في الكلاس")
            else:
                print("❌ _safe_get_numeric_value غير موجودة في الكلاس")
                
            if hasattr(cls, '_calculate_sum_range'):
                print("✅ _calculate_sum_range موجودة في الكلاس")
            else:
                print("❌ _calculate_sum_range غير موجودة في الكلاس")
        else:
            print("❌ كلاس AccountDetailsDialog غير موجود")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ في syntax: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء تشخيص مشكلة AccountDetailsDialog")
    print("=" * 60)
    
    # فحص syntax الملف
    syntax_ok = check_file_syntax()
    
    if syntax_ok:
        # تشخيص المشكلة
        debug_result = debug_account_details()
        
        print("\n" + "=" * 60)
        print(f"🏁 نتائج التشخيص:")
        print(f"   فحص Syntax: {'✅ نجح' if syntax_ok else '❌ فشل'}")
        print(f"   التشخيص العام: {'✅ نجح' if debug_result else '❌ فشل'}")
    else:
        print("\n❌ يوجد خطأ في syntax الملف")
    
    input("\nاضغط Enter للإغلاق...")
