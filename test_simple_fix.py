#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحل البسيط لمشكلة AccountDetailsDialog
"""

import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_simple_fix():
    """اختبار الحل البسيط"""
    print("🧪 اختبار الحل البسيط لمشكلة AccountDetailsDialog")
    print("=" * 60)
    
    try:
        # حذف الملف إذا كان موجوداً
        if os.path.exists("accounting_system.xlsx"):
            os.remove("accounting_system.xlsx")
            print("🗑️ تم حذف الملف السابق")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        import tkinter as tk
        
        # إنشاء ExcelManager جديد
        print("📝 إنشاء ExcelManager جديد...")
        excel = ExcelManager()
        
        if excel.workbook:
            print("✅ تم إنشاء الملف بنجاح")
            
            # إنشاء حساب اختبار
            print("💼 إنشاء حساب اختبار...")
            result = excel.create_account_sheet("SIMPLE001", "حساب اختبار بسيط", 2000.0)
            print(f"إنشاء الحساب: {'نجح' if result else 'فشل'}")
            
            if result:
                # إضافة مستندات
                print("📄 إضافة مستندات...")
                for i in range(2):
                    doc_result = excel.add_document("SIMPLE001-حساب اختبار بسيط", 500.0 * (i+1), f"DOC{i+1:03d}", f"PAY{i+1:03d}")
                    print(f"   مستند {i+1}: {'نجح' if doc_result else 'فشل'}")
                
                # حفظ الملف
                save_result = excel.save_workbook()
                print(f"حفظ الملف: {'نجح' if save_result else 'فشل'}")
                
                # اختبار ManageAccountsWindow
                print("🪟 اختبار ManageAccountsWindow...")
                
                root = tk.Tk()
                root.withdraw()  # إخفاء النافذة
                
                try:
                    from manage_accounts import ManageAccountsWindow
                    
                    # إنشاء ManageAccountsWindow
                    manage_window = ManageAccountsWindow(root, excel)
                    print("✅ تم إنشاء ManageAccountsWindow بنجاح")
                    
                    # اختبار تحميل الحسابات
                    manage_window.load_accounts()
                    print("✅ تم تحميل الحسابات بنجاح")
                    
                    # اختبار AccountDetailsDialog
                    print("🔍 اختبار AccountDetailsDialog...")
                    
                    from manage_accounts import AccountDetailsDialog
                    
                    details_dialog = AccountDetailsDialog(
                        parent=root,
                        excel=excel,
                        sheet_name="SIMPLE001-حساب اختبار بسيط",
                        account_num="SIMPLE001",
                        account_name="حساب اختبار بسيط"
                    )
                    
                    print("✅ تم إنشاء AccountDetailsDialog بنجاح!")
                    print("✅ لا توجد أخطاء في _safe_get_numeric_value")
                    
                    # إغلاق النوافذ
                    details_dialog.destroy()
                    manage_window.destroy()
                    root.destroy()
                    
                    print("\n🎉 جميع الاختبارات نجحت! المشكلة تم حلها.")
                    return True
                    
                except Exception as e:
                    print(f"❌ خطأ في الاختبار: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    root.destroy()
                    return False
            else:
                print("❌ فشل في إنشاء الحساب")
                return False
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # تنظيف الملفات
        try:
            if os.path.exists("accounting_system.xlsx"):
                os.remove("accounting_system.xlsx")
                print("🧹 تم تنظيف ملف الاختبار")
        except:
            pass

if __name__ == "__main__":
    print("🚀 بدء اختبار الحل البسيط")
    print("=" * 60)
    
    # تشغيل الاختبار
    test_result = test_simple_fix()
    
    print("\n" + "=" * 60)
    print(f"🏁 نتيجة الاختبار: {'✅ نجح' if test_result else '❌ فشل'}")
    
    if test_result:
        print("\n🎉 المشكلة تم حلها! يمكنك الآن استخدام البرنامج بدون أخطاء.")
        print("💡 الحل المطبق: استبدال الدوال المعقدة بمعالجة بسيطة وآمنة للقيم.")
    else:
        print("\n⚠️ لا تزال هناك مشكلة. يرجى مراجعة رسائل الخطأ أعلاه.")
    
    input("\nاضغط Enter للإغلاق...")
