@echo off
chcp 65001 >nul
title Test Launcher - Accounting System

echo ========================================
echo    Test Launcher
echo    Accounting System
echo ========================================
echo.

echo Running comprehensive launcher test...
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found, running test...
    echo.
    
    REM Run the launcher test
    %PYTHON_PATH% test_launcher.py
    
) else (
    echo ERROR: Python not found at expected location
    echo Expected: %PYTHON_PATH%
    pause
    exit /b 1
)

echo.
echo Test completed.
pause
