import PyInstaller.__main__
import os
import sys
import shutil

def build_exe():
    """بناء ملف تنفيذي محسن للنظام المحاسبي"""
    print("🔨 بدء عملية بناء الملف التنفيذي...")

    # الحصول على المسار الحالي
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # تنظيف المجلدات السابقة
    for folder in ['build', 'dist']:
        folder_path = os.path.join(current_dir, folder)
        if os.path.exists(folder_path):
            shutil.rmtree(folder_path)
            print(f"🗑️ تم حذف مجلد {folder}")

    # استخدام ملف spec المحسن
    spec_file = os.path.join(current_dir, 'AccountingSystem.spec')

    if os.path.exists(spec_file):
        print("📋 استخدام ملف التكوين المحسن...")
        options = [spec_file]
    else:
        print("⚙️ استخدام التكوين الافتراضي...")
        # تكوين خيارات البناء المحسنة
        options = [
            'start.py',  # الملف الرئيسي المحسن
            '--onefile',  # إنشاء ملف تنفيذي واحد
            '--windowed',  # تطبيق نافذة (بدون نافذة terminal)
            '--clean',  # تنظيف مجلد البناء قبل البدء
            '--noconfirm',  # عدم طلب التأكيد عند حذف مجلد dist
            f'--distpath={os.path.join(current_dir, "dist")}',  # مسار الإخراج
            f'--workpath={os.path.join(current_dir, "build")}',  # مسار ملفات البناء
            '--name=نظام_المحاسبة_وزارة_الصحة',  # اسم الملف التنفيذي
            '--hidden-import=openpyxl',  # استيراد مخفي للمكتبات
            '--hidden-import=openpyxl.styles',
            '--hidden-import=tkinter',
            '--hidden-import=tkinter.ttk',
            '--hidden-import=tkinter.messagebox',
            '--hidden-import=tkinter.filedialog',
            '--hidden-import=ttkthemes',
            '--collect-all=openpyxl',  # جمع جميع ملفات المكتبة
            '--optimize=2',  # تحسين الكود
            '--add-data=excel_manager.py;.',
            '--add-data=document_window.py;.',
            '--add-data=search_window.py;.',
            '--add-data=manage_accounts.py;.',
            '--add-data=app.py;.',
        ]



    print("📦 بدء عملية التجميع...")
    try:
        # بناء التطبيق
        PyInstaller.__main__.run(options)
        print("✅ تم إنشاء الملف التنفيذي بنجاح!")
        print(f"📁 الملف موجود في: {os.path.join(current_dir, 'dist')}")

        # إنشاء ملف Excel افتراضي في مجلد dist
        dist_excel = os.path.join(current_dir, 'dist', 'accounting_system.xlsx')
        source_excel = os.path.join(current_dir, 'accounting_system.xlsx')
        if os.path.exists(source_excel):
            shutil.copy2(source_excel, dist_excel)
            print("📊 تم نسخ ملف Excel الافتراضي")

    except Exception as e:
        print(f"❌ حدث خطأ أثناء البناء: {str(e)}")
        return False

    return True

if __name__ == '__main__':
    success = build_exe()
    if success:
        print("\n🎉 تم الانتهاء من بناء النظام بنجاح!")
        print("يمكنك الآن تشغيل الملف التنفيذي من مجلد dist")
    else:
        print("\n❌ فشل في بناء النظام")

    input("\nاضغط Enter للإغلاق...")
