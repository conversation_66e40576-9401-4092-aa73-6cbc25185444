#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحميل المكتبات الشامل للنظام المحاسبي
Comprehensive Library Downloader for Accounting System
"""

import sys
import os
import subprocess
import json
from datetime import datetime

class LibraryDownloader:
    def __init__(self):
        self.libraries_dir = os.path.dirname(os.path.abspath(__file__))
        self.packages_dir = os.path.join(self.libraries_dir, 'packages')
        self.cache_dir = os.path.join(self.libraries_dir, 'cache')
        self.status_file = os.path.join(self.libraries_dir, 'download_status.json')
        
        # المكتبات المطلوبة
        self.required_libraries = [
            'openpyxl>=3.1.0',
            'ttkthemes>=3.2.2',
            'Pillow>=10.0.0',
            'pyinstaller>=5.13.0',
            'setuptools>=68.0.0',
            'wheel>=0.41.0'
        ]
        
        # إنشاء المجلدات
        os.makedirs(self.packages_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def download_package(self, package_name):
        """تحميل حزمة واحدة مع جميع تبعياتها"""
        try:
            print(f"📥 تحميل {package_name}...")
            
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'download',
                package_name,
                '--dest', self.packages_dir,
                '--cache-dir', self.cache_dir,
                '--no-deps'  # تحميل الحزمة الرئيسية فقط
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ تم تحميل {package_name}")
                return True
            else:
                print(f"❌ فشل تحميل {package_name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحميل {package_name}: {str(e)}")
            return False
    
    def download_with_dependencies(self, package_name):
        """تحميل حزمة مع جميع تبعياتها"""
        try:
            print(f"📦 تحميل {package_name} مع التبعيات...")
            
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'download',
                package_name,
                '--dest', self.packages_dir,
                '--cache-dir', self.cache_dir
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ تم تحميل {package_name} مع التبعيات")
                return True
            else:
                print(f"❌ فشل تحميل {package_name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحميل {package_name}: {str(e)}")
            return False
    
    def download_all_libraries(self):
        """تحميل جميع المكتبات المطلوبة"""
        print("🚀 بدء تحميل جميع المكتبات المطلوبة...")
        print("=" * 60)
        
        downloaded_packages = []
        failed_packages = []
        
        for package in self.required_libraries:
            print(f"\n📦 معالجة: {package}")
            
            # محاولة التحميل مع التبعيات أولاً
            if self.download_with_dependencies(package):
                downloaded_packages.append(package)
            else:
                # في حالة الفشل، محاولة التحميل بدون تبعيات
                print(f"⚠️ محاولة تحميل {package} بدون تبعيات...")
                if self.download_package(package):
                    downloaded_packages.append(package)
                else:
                    failed_packages.append(package)
        
        # حفظ حالة التحميل
        self.save_download_status(downloaded_packages, failed_packages)
        
        # عرض النتائج
        print("\n" + "=" * 60)
        print("📊 ملخص التحميل:")
        print(f"✅ تم تحميل: {len(downloaded_packages)} حزمة")
        print(f"❌ فشل تحميل: {len(failed_packages)} حزمة")
        
        if downloaded_packages:
            print("\n✅ الحزم المحملة:")
            for pkg in downloaded_packages:
                print(f"   - {pkg}")
        
        if failed_packages:
            print("\n❌ الحزم الفاشلة:")
            for pkg in failed_packages:
                print(f"   - {pkg}")
        
        print("=" * 60)
        
        return len(downloaded_packages) > 0
    
    def save_download_status(self, downloaded, failed):
        """حفظ حالة التحميل"""
        try:
            status = {
                'download_date': datetime.now().isoformat(),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'downloaded_packages': downloaded,
                'failed_packages': failed,
                'total_downloaded': len(downloaded),
                'total_failed': len(failed),
                'packages_directory': self.packages_dir,
                'cache_directory': self.cache_dir
            }
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم حفظ حالة التحميل في: {self.status_file}")
            
        except Exception as e:
            print(f"⚠️ فشل في حفظ حالة التحميل: {str(e)}")
    
    def create_offline_installer(self):
        """إنشاء مثبت محلي للمكتبات المحملة"""
        installer_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثبت المكتبات المحلي
Local Library Installer
"""

import sys
import os
import subprocess
import glob

def install_from_local():
    """تثبيت المكتبات من الملفات المحلية"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    packages_dir = os.path.join(script_dir, 'packages')
    
    if not os.path.exists(packages_dir):
        print("❌ مجلد المكتبات غير موجود")
        return False
    
    print("📦 تثبيت المكتبات من الملفات المحلية...")
    
    # البحث عن ملفات الحزم
    wheel_files = glob.glob(os.path.join(packages_dir, "*.whl"))
    tar_files = glob.glob(os.path.join(packages_dir, "*.tar.gz"))
    
    all_files = wheel_files + tar_files
    
    if not all_files:
        print("❌ لا توجد ملفات حزم للتثبيت")
        return False
    
    print(f"📦 وجد {len(all_files)} ملف حزمة")
    
    success_count = 0
    
    for package_file in all_files:
        try:
            package_name = os.path.basename(package_file)
            print(f"📥 تثبيت {package_name}...")
            
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install',
                package_file,
                '--force-reinstall',
                '--no-deps',
                '--quiet'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {package_name}")
                success_count += 1
            else:
                print(f"⚠️ مشكلة في تثبيت {package_name}")
                
        except Exception as e:
            print(f"❌ خطأ في تثبيت {package_name}: {str(e)}")
    
    print(f"\\n📊 تم تثبيت {success_count} من {len(all_files)} حزمة")
    return success_count > 0

def verify_installation():
    """التحقق من تثبيت المكتبات الأساسية"""
    required_modules = ['openpyxl', 'tkinter']
    optional_modules = ['ttkthemes', 'PIL']
    
    print("\\n🔍 التحقق من تثبيت المكتبات...")
    
    # فحص المكتبات الأساسية
    all_required_ok = True
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} متوفر (مطلوب)")
        except ImportError:
            print(f"❌ {module} غير متوفر (مطلوب)")
            all_required_ok = False
    
    # فحص المكتبات الاختيارية
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} متوفر (اختياري)")
        except ImportError:
            print(f"⚠️ {module} غير متوفر (اختياري)")
    
    return all_required_ok

if __name__ == "__main__":
    print("🔧 مثبت المكتبات المحلي")
    print("=" * 40)
    
    # تثبيت المكتبات
    install_success = install_from_local()
    
    if install_success:
        # التحقق من التثبيت
        verify_success = verify_installation()
        
        if verify_success:
            print("\\n✅ تم تثبيت جميع المكتبات الأساسية بنجاح")
            print("🚀 النظام جاهز للتشغيل")
        else:
            print("\\n⚠️ بعض المكتبات الأساسية مفقودة")
    else:
        print("\\n❌ فشل في تثبيت المكتبات")
    
    input("\\nاضغط Enter للإغلاق...")
'''
        
        installer_path = os.path.join(self.libraries_dir, 'install_offline.py')
        
        try:
            with open(installer_path, 'w', encoding='utf-8') as f:
                f.write(installer_content)
            print(f"✅ تم إنشاء المثبت المحلي: {installer_path}")
            return True
        except Exception as e:
            print(f"❌ فشل في إنشاء المثبت المحلي: {str(e)}")
            return False
    
    def create_readme(self):
        """إنشاء ملف README للمكتبات"""
        readme_content = f"""# مجلد المكتبات المدمجة
# Embedded Libraries Folder

تم إنشاء هذا المجلد في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## المحتويات:
- `packages/` - ملفات المكتبات المحملة (.whl و .tar.gz)
- `cache/` - ذاكرة التخزين المؤقت لـ pip
- `requirements.txt` - قائمة المكتبات المطلوبة
- `download_libraries.py` - أداة تحميل المكتبات
- `install_offline.py` - مثبت المكتبات المحلي
- `download_status.json` - حالة التحميل

## المكتبات المطلوبة:
{chr(10).join(f"- {pkg}" for pkg in self.required_libraries)}

## طريقة الاستخدام:

### 1. تحميل المكتبات:
```bash
python download_libraries.py
```

### 2. تثبيت المكتبات المحلية:
```bash
python install_offline.py
```

### 3. التحقق من التثبيت:
```python
import openpyxl
import tkinter
print("جميع المكتبات الأساسية متوفرة!")
```

## ملاحظات مهمة:
- يجب تشغيل `download_libraries.py` مرة واحدة فقط لتحميل المكتبات
- يمكن استخدام `install_offline.py` على أي جهاز بدون إنترنت
- جميع المكتبات محفوظة محلياً في مجلد `packages/`
- لا حاجة لاتصال إنترنت بعد التحميل الأول

## الدعم الفني:
وزارة الصحة الأردنية
<EMAIL>
"""
        
        readme_path = os.path.join(self.libraries_dir, 'README.md')
        
        try:
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            print(f"✅ تم إنشاء ملف README: {readme_path}")
            return True
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف README: {str(e)}")
            return False

def main():
    """الدالة الرئيسية"""
    print("📦 أداة تحميل المكتبات الشاملة")
    print("Comprehensive Library Downloader")
    print("=" * 60)
    
    downloader = LibraryDownloader()
    
    # تحميل المكتبات
    success = downloader.download_all_libraries()
    
    if success:
        print("\n🔧 إنشاء الأدوات الإضافية...")
        
        # إنشاء المثبت المحلي
        downloader.create_offline_installer()
        
        # إنشاء ملف README
        downloader.create_readme()
        
        print("\n" + "=" * 60)
        print("🎉 تم تحميل المكتبات بنجاح!")
        print("📁 جميع الملفات محفوظة في مجلد packages/")
        print("🚀 يمكن الآن تشغيل التطبيق بدون إنترنت")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("⚠️ لم يتم تحميل أي مكتبات")
        print("🔍 تحقق من اتصال الإنترنت وإعدادات pip")
        print("=" * 60)
    
    input("\nاضغط Enter للإغلاق...")
    return success

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التحميل بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        input("اضغط Enter للإغلاق...")
