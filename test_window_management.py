#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام إدارة النوافذ المحسن
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_window_management():
    """اختبار نظام إدارة النوافذ"""
    try:
        print("🧪 بدء اختبار نظام إدارة النوافذ...")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار نظام إدارة النوافذ")
        root.geometry("600x400")
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="🧪 اختبار نظام إدارة النوافذ", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # معلومات النظام
        info_text = """
        ✅ تم تطبيق نظام إدارة النوافذ الجديد
        
        الميزات المضافة:
        • منع فتح نوافذ مكررة
        • تركيز على النوافذ المفتوحة بالفعل
        • إدارة النوافذ الفرعية
        • إغلاق تلقائي للنوافذ الفرعية
        
        النوافذ المحمية:
        • نافذة إدارة الحسابات
        • نافذة إضافة حساب
        • نافذة إضافة مستند
        • نافذة البحث
        • نافذة إدارة المستخدمين
        • نافذة تغيير كلمة المرور
        """
        
        info_label = ttk.Label(main_frame, text=info_text, 
                              font=('Arial', 10), justify=tk.RIGHT)
        info_label.pack(pady=(0, 20))
        
        # أزرار الاختبار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        def test_import_app():
            """اختبار استيراد التطبيق الرئيسي"""
            try:
                from app import AccountingApp
                messagebox.showinfo("نجاح", "✅ تم استيراد التطبيق الرئيسي بنجاح")
                print("✅ تم استيراد app.py بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"❌ فشل في استيراد التطبيق: {str(e)}")
                print(f"❌ خطأ في استيراد app.py: {str(e)}")
        
        def test_import_manage_accounts():
            """اختبار استيراد نافذة إدارة الحسابات"""
            try:
                from manage_accounts import ManageAccountsDialog
                messagebox.showinfo("نجاح", "✅ تم استيراد نافذة إدارة الحسابات بنجاح")
                print("✅ تم استيراد manage_accounts.py بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"❌ فشل في استيراد نافذة إدارة الحسابات: {str(e)}")
                print(f"❌ خطأ في استيراد manage_accounts.py: {str(e)}")
        
        def test_run_app():
            """اختبار تشغيل التطبيق"""
            try:
                messagebox.showinfo("تنبيه", "سيتم إغلاق نافذة الاختبار وتشغيل التطبيق الرئيسي")
                root.destroy()
                
                # تشغيل التطبيق الرئيسي
                from app import AccountingApp
                app_root = tk.Tk()
                app = AccountingApp(app_root)
                
            except Exception as e:
                messagebox.showerror("خطأ", f"❌ فشل في تشغيل التطبيق: {str(e)}")
                print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        
        # أزرار الاختبار
        ttk.Button(buttons_frame, text="🧪 اختبار استيراد التطبيق الرئيسي",
                  command=test_import_app).pack(fill=tk.X, pady=5)
        
        ttk.Button(buttons_frame, text="🧪 اختبار استيراد نافذة إدارة الحسابات",
                  command=test_import_manage_accounts).pack(fill=tk.X, pady=5)
        
        ttk.Button(buttons_frame, text="🚀 تشغيل التطبيق الرئيسي",
                  command=test_run_app).pack(fill=tk.X, pady=10)
        
        # زر الإغلاق
        ttk.Button(buttons_frame, text="❌ إغلاق",
                  command=root.destroy).pack(fill=tk.X, pady=5)
        
        # معلومات إضافية
        footer_label = ttk.Label(main_frame, 
                                text="💡 تم تحديث النظام لمنع فتح نوافذ مكررة وتحسين إدارة النوافذ",
                                font=('Arial', 9), foreground='blue')
        footer_label.pack(pady=(20, 0))
        
        # تشغيل النافذة
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام إدارة النوافذ: {str(e)}")
        messagebox.showerror("خطأ", f"خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    test_window_management()
