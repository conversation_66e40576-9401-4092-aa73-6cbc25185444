// نظام المصادقة للتطبيق المحاسبي
let isLoggedIn = false;
let currentUser = null;

// المستخدمون المسموح لهم
const users = [
    { username: 'admin', password: 'admin', name: 'المدير الرئيسي', role: 'admin' },
    { username: 'user', password: 'user', name: 'مستخدم عادي', role: 'user' },
    { username: 'accountant', password: 'accountant', name: 'محاسب', role: 'accountant' }
];

// فحص حالة تسجيل الدخول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
});

// فحص حالة المصادقة
function checkAuthStatus() {
    const savedUser = localStorage.getItem('currentUser');
    const rememberLogin = localStorage.getItem('rememberLogin');
    
    if (savedUser && rememberLogin === 'true') {
        currentUser = JSON.parse(savedUser);
        isLoggedIn = true;
        showMainContent();
        updateUserInfo();
    } else {
        showLoginModal();
    }
}

// إظهار نافذة تسجيل الدخول
function showLoginModal() {
    // إخفاء المحتوى الرئيسي
    document.body.style.visibility = 'hidden';
    
    // إظهار نافذة تسجيل الدخول
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'), {
        backdrop: 'static',
        keyboard: false
    });
    loginModal.show();
    
    // إضافة مستمع للنموذج
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.removeEventListener('submit', handleLogin); // إزالة المستمع السابق
        loginForm.addEventListener('submit', handleLogin);
    }
}

// معالجة تسجيل الدخول
function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe')?.checked || false;
    
    if (!username || !password) {
        showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'warning');
        return;
    }
    
    // البحث عن المستخدم
    const user = users.find(u => u.username === username && u.password === password);
    
    if (user) {
        // نجح تسجيل الدخول
        currentUser = user;
        isLoggedIn = true;
        
        // حفظ معلومات المستخدم
        localStorage.setItem('currentUser', JSON.stringify(user));
        localStorage.setItem('rememberLogin', rememberMe.toString());
        localStorage.setItem('loginTime', new Date().toISOString());
        
        // إخفاء نافذة تسجيل الدخول
        const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
        if (loginModal) {
            loginModal.hide();
        }
        
        // إظهار المحتوى الرئيسي
        showMainContent();
        updateUserInfo();
        
        // إظهار رسالة ترحيب
        setTimeout(() => {
            showAlert(`مرحباً ${user.name}! تم تسجيل الدخول بنجاح`, 'success');
        }, 500);
        
        // تسجيل عملية الدخول
        console.log(`تم تسجيل دخول المستخدم: ${user.name} في ${new Date().toLocaleString('ar-JO')}`);
        
    } else {
        // فشل تسجيل الدخول
        showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
        
        // تنظيف الحقول
        document.getElementById('loginPassword').value = '';
        document.getElementById('loginPassword').focus();
    }
}

// إظهار المحتوى الرئيسي
function showMainContent() {
    // إظهار المحتوى
    document.body.style.visibility = 'visible';
    
    // إخفاء نافذة تسجيل الدخول إذا كانت مفتوحة
    const loginModalElement = document.getElementById('loginModal');
    const loginModal = bootstrap.Modal.getInstance(loginModalElement);
    if (loginModal) {
        loginModal.hide();
    }
    
    // تحميل البيانات والواجهة
    if (typeof loadData === 'function') {
        loadData();
    }
    if (typeof updateDashboard === 'function') {
        updateDashboard();
    }
    if (typeof showDashboard === 'function') {
        showDashboard();
    }
}

// تحديث معلومات المستخدم في الواجهة
function updateUserInfo() {
    if (currentUser) {
        const userElement = document.getElementById('currentUser');
        if (userElement) {
            userElement.textContent = currentUser.name;
        }
        
        // إضافة معلومات إضافية إذا لزم الأمر
        const userRoleElement = document.getElementById('userRole');
        if (userRoleElement) {
            userRoleElement.textContent = getRoleDisplayName(currentUser.role);
        }
    }
}

// الحصول على اسم الدور للعرض
function getRoleDisplayName(role) {
    const roles = {
        'admin': 'مدير النظام',
        'accountant': 'محاسب',
        'user': 'مستخدم'
    };
    return roles[role] || 'مستخدم';
}

// تسجيل الخروج
function logout() {
    // تأكيد تسجيل الخروج
    if (confirm('هل أنت متأكد من رغبتك في تسجيل الخروج؟')) {
        // تنظيف البيانات
        isLoggedIn = false;
        currentUser = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('rememberLogin');
        localStorage.removeItem('loginTime');
        
        // إظهار رسالة
        showAlert('تم تسجيل الخروج بنجاح', 'info');
        
        // إعادة تحميل الصفحة بعد ثانية
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

// فحص صلاحيات المستخدم
function hasPermission(permission) {
    if (!currentUser) return false;
    
    const permissions = {
        'admin': ['read', 'write', 'delete', 'export', 'import', 'manage_users'],
        'accountant': ['read', 'write', 'export'],
        'user': ['read']
    };
    
    const userPermissions = permissions[currentUser.role] || [];
    return userPermissions.includes(permission);
}

// فحص انتهاء الجلسة (اختياري)
function checkSessionExpiry() {
    const loginTime = localStorage.getItem('loginTime');
    if (loginTime) {
        const sessionDuration = 8 * 60 * 60 * 1000; // 8 ساعات
        const currentTime = new Date().getTime();
        const loginTimeMs = new Date(loginTime).getTime();
        
        if (currentTime - loginTimeMs > sessionDuration) {
            showAlert('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى', 'warning');
            logout();
        }
    }
}

// فحص الجلسة كل 30 دقيقة
setInterval(checkSessionExpiry, 30 * 60 * 1000);

// إضافة مستمع لإغلاق النافذة
window.addEventListener('beforeunload', function() {
    if (isLoggedIn && currentUser) {
        // يمكن إضافة تسجيل لوقت الخروج هنا
        console.log(`المستخدم ${currentUser.name} غادر النظام في ${new Date().toLocaleString('ar-JO')}`);
    }
});

// دالة مساعدة لإظهار/إخفاء عناصر حسب الصلاحيات
function toggleElementsByPermission() {
    // إخفاء/إظهار عناصر حسب صلاحيات المستخدم
    const adminElements = document.querySelectorAll('[data-permission="admin"]');
    const writeElements = document.querySelectorAll('[data-permission="write"]');
    const deleteElements = document.querySelectorAll('[data-permission="delete"]');
    
    adminElements.forEach(el => {
        el.style.display = hasPermission('manage_users') ? 'block' : 'none';
    });
    
    writeElements.forEach(el => {
        el.style.display = hasPermission('write') ? 'block' : 'none';
    });
    
    deleteElements.forEach(el => {
        el.style.display = hasPermission('delete') ? 'block' : 'none';
    });
}

// استدعاء فحص الصلاحيات عند تحميل المحتوى
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(toggleElementsByPermission, 1000);
});
