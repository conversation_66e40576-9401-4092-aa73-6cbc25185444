#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط وآمن لنظام إدارة المستندات المحاسبية
وزارة الصحة - التأمين الصحي
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """تشغيل التطبيق بطريقة آمنة"""
    try:
        print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية")
        print("🏥 وزارة الصحة - التأمين الصحي")
        print("=" * 60)
        
        # التأكد من وجود الملفات المطلوبة
        required_files = ['app.py', 'user_manager.py', 'excel_manager.py']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            input("اضغط Enter للخروج...")
            return
        
        print("✅ جميع الملفات المطلوبة موجودة")
        
        # حذف ملف المستخدمين إذا كان موجوداً لإعادة إنشائه
        if os.path.exists("users.json"):
            try:
                os.remove("users.json")
                print("🔄 تم إعادة تعيين بيانات المستخدمين")
            except:
                pass
        
        print("🔄 تحميل النظام...")
        
        # استيراد التطبيق
        from app import AccountingApp
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")
        
        print("✅ تم تحميل النظام بنجاح")
        print("🔐 سيتم عرض نافذة تسجيل الدخول...")
        print()
        print("📋 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin")
        print("=" * 60)
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {str(e)}")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("   pip install openpyxl")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
