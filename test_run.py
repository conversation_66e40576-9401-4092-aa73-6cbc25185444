#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشغيل التطبيق مع عرض مفصل للأخطاء
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        import tkinter as tk
        print("✅ tkinter - OK")
    except Exception as e:
        print(f"❌ tkinter - خطأ: {e}")
        return False
    
    try:
        from app import AccountingApp
        print("✅ AccountingApp - OK")
    except Exception as e:
        print(f"❌ AccountingApp - خطأ: {e}")
        return False
    
    try:
        from excel_manager import ExcelManager
        print("✅ ExcelManager - OK")
    except Exception as e:
        print(f"❌ ExcelManager - خطأ: {e}")
        return False
    
    try:
        from manage_accounts import ManageAccountsDialog
        print("✅ ManageAccountsDialog - OK")
    except Exception as e:
        print(f"❌ ManageAccountsDialog - خطأ: {e}")
        return False
    
    try:
        from user_manager import UserManager
        print("✅ UserManager - OK")
    except Exception as e:
        print(f"❌ UserManager - خطأ: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    
    print("=" * 80)
    print("🧪 اختبار تشغيل نظام إدارة المستندات المحاسبية")
    print("=" * 80)
    print(f"🕒 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # اختبار الاستيرادات أولاً
    if not test_imports():
        print("❌ فشل في اختبار الاستيرادات!")
        return
    
    print("\n🚀 بدء تشغيل التطبيق...")
    
    try:
        import tkinter as tk
        from app import AccountingApp
        
        # إنشاء النافذة الرئيسية
        print("🖼️ إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        
        # إنشاء التطبيق
        print("🏗️ إنشاء كائن التطبيق...")
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # تشغيل التطبيق
        print("▶️ بدء تشغيل حلقة الأحداث الرئيسية...")
        print("✅ التطبيق يعمل الآن!")
        print("📋 يمكنك الآن استخدام التطبيق...")
        print("-" * 80)
        
        # تشغيل حلقة الأحداث
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        print("\n📋 تفاصيل الخطأ:")
        print(traceback.format_exc())
        
    finally:
        print(f"\n🔚 انتهاء تشغيل التطبيق - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

if __name__ == "__main__":
    main()
