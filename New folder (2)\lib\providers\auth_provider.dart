import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  bool _isAuthenticated = false;
  String _username = '';
  bool _isLoading = false;

  bool get isAuthenticated => _isAuthenticated;
  String get username => _username;
  bool get isLoading => _isLoading;

  AuthProvider() {
    _loadAuthState();
  }

  // تحميل حالة المصادقة من التخزين المحلي
  Future<void> _loadAuthState() async {
    final prefs = await SharedPreferences.getInstance();
    _isAuthenticated = prefs.getBool('isAuthenticated') ?? false;
    _username = prefs.getString('username') ?? '';
    notifyListeners();
  }

  // حفظ حالة المصادقة في التخزين المحلي
  Future<void> _saveAuthState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isAuthenticated', _isAuthenticated);
    await prefs.setString('username', _username);
  }

  // تسجيل الدخول
  Future<String?> login(String username, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final result = await ApiService.login(username, password);
      
      if (result['success']) {
        _isAuthenticated = true;
        _username = username;
        await _saveAuthState();
        _isLoading = false;
        notifyListeners();
        return null; // نجح تسجيل الدخول
      } else {
        _isLoading = false;
        notifyListeners();
        return result['message'];
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return 'خطأ في تسجيل الدخول: $e';
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await ApiService.logout();
    } catch (e) {
      // تجاهل أخطاء تسجيل الخروج
    }

    _isAuthenticated = false;
    _username = '';
    await _saveAuthState();
    _isLoading = false;
    notifyListeners();
  }
}
